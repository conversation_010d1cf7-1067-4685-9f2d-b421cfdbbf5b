import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  NewEfItem,
  NewEfSubcategory4,
} from '../models';
import {NewEfItemRepository} from '../repositories';

export class NewEfItemNewEfSubcategory4Controller {
  constructor(
    @repository(NewEfItemRepository)
    public newEfItemRepository: NewEfItemRepository,
  ) { }

  @get('/new-ef-items/{id}/new-ef-subcategory4', {
    responses: {
      '200': {
        description: 'NewEfSubcategory4 belonging to NewEfItem',
        content: {
          'application/json': {
            schema: getModelSchemaRef(NewEfSubcategory4),
          },
        },
      },
    },
  })
  async getNewEfSubcategory4(
    @param.path.number('id') id: typeof NewEfItem.prototype.id,
  ): Promise<NewEfSubcategory4> {
    return this.newEfItemRepository.subcat4(id);
  }
}
