import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {LocationTwo, LocationTwoRelations, LocationThree} from '../models';
import {LocationThreeRepository} from './location-three.repository';

export class LocationTwoRepository extends DefaultCrudRepository<
  LocationTwo,
  typeof LocationTwo.prototype.id,
  LocationTwoRelations
> {

  public readonly locationThrees: HasManyRepositoryFactory<LocationThree, typeof LocationTwo.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>,
  ) {
    super(LocationTwo, dataSource);
    this.locationThrees = this.createHasManyRepositoryFactoryFor('locationThrees', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('locationThrees', this.locationThrees.inclusionResolver);
  }
}
