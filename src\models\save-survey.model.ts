import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class SaveSurvey extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'object',
  })
  sector?: object;

  @property({
    type: 'array',
    itemType: 'any',
  })
  focusArea?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  selectedTopic?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  questions?: any[];

  @property({
    type: 'array',
    itemType: 'any',
  })
  selectedQuestion?: any[];

  @property({
    type: 'number',
  })
  surveyId?: number;

  @property({
    type: 'number',
  })
  subSurveyId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<SaveSurvey>) {
    super(data);
  }
}

export interface SaveSurveyRelations {
  // describe navigational properties here
}

export type SaveSurveyWithRelations = SaveSurvey & SaveSurveyRelations;
