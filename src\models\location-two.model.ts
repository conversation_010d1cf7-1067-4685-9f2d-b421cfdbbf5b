import {Entity, model, property, hasMany} from '@loopback/repository';
import {LocationThree} from './location-three.model';

@model({settings: {strict: false}})
export class LocationTwo extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];
  
  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'number',
  })
  locationOneId?: number;

  @hasMany(() => LocationThree)
  locationThrees: LocationThree[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<LocationTwo>) {
    super(data);
  }
}

export interface LocationTwoRelations {
  // describe navigational properties here
}

export type LocationTwoWithRelations = LocationTwo & LocationTwoRelations;
