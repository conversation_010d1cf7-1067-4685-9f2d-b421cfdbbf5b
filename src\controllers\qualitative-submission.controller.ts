import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {QualitativeSubmission} from '../models';
import {QualitativeSubmissionRepository} from '../repositories';

export class QualitativeSubmissionController {
  constructor(
    @repository(QualitativeSubmissionRepository)
    public qualitativeSubmissionRepository: QualitativeSubmissionRepository,
  ) { }

  @post('/qualitative-submissions')
  @response(200, {
    description: 'QualitativeSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(QualitativeSubmission)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeSubmission, {
            title: 'NewQualitativeSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    qualitativeSubmission: Omit<QualitativeSubmission, 'id'>,
  ): Promise<QualitativeSubmission> {
    return this.qualitativeSubmissionRepository.create(qualitativeSubmission);
  }

  @get('/qualitative-submissions/count')
  @response(200, {
    description: 'QualitativeSubmission model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QualitativeSubmission) where?: Where<QualitativeSubmission>,
  ): Promise<Count> {
    return this.qualitativeSubmissionRepository.count(where);
  }

  @get('/qualitative-submissions')
  @response(200, {
    description: 'Array of QualitativeSubmission model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QualitativeSubmission, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QualitativeSubmission) filter?: Filter<QualitativeSubmission>,
  ): Promise<QualitativeSubmission[]> {
    return this.qualitativeSubmissionRepository.find(filter);
  }

  @patch('/qualitative-submissions')
  @response(200, {
    description: 'QualitativeSubmission PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeSubmission, {partial: true}),
        },
      },
    })
    qualitativeSubmission: QualitativeSubmission,
    @param.where(QualitativeSubmission) where?: Where<QualitativeSubmission>,
  ): Promise<Count> {
    return this.qualitativeSubmissionRepository.updateAll(qualitativeSubmission, where);
  }

  @get('/qualitative-submissions/{id}')
  @response(200, {
    description: 'QualitativeSubmission model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QualitativeSubmission, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QualitativeSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<QualitativeSubmission>
  ): Promise<QualitativeSubmission> {
    return this.qualitativeSubmissionRepository.findById(id, filter);
  }

  @patch('/qualitative-submissions/{id}')
  @response(204, {
    description: 'QualitativeSubmission PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeSubmission, {partial: true}),
        },
      },
    })
    qualitativeSubmission: QualitativeSubmission,
  ): Promise<void> {
    await this.qualitativeSubmissionRepository.updateById(id, qualitativeSubmission);
  }

  @put('/qualitative-submissions/{id}')
  @response(204, {
    description: 'QualitativeSubmission PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() qualitativeSubmission: QualitativeSubmission,
  ): Promise<void> {
    await this.qualitativeSubmissionRepository.replaceById(id, qualitativeSubmission);
  }

  @del('/qualitative-submissions/{id}')
  @response(204, {
    description: 'QualitativeSubmission DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.qualitativeSubmissionRepository.deleteById(id);
  }

  @post('/qualitative-previous-submissions')
  @response(200, {
    description: 'QualitativeSubmission PATCH success count',

  })
  async recentRPSubmission(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              reporting_period: {
                type: 'string',
              },
              userProfileId: {
                type: 'number',
              },
              locationId: {
                type: 'number', nullable: true
              },
              level: {
                type: 'number', nullable: true
              },
              dfId: {
                type: 'number',
              },
              categoryId: {
                type: 'number',
              },
              topicId: {
                type: 'number',
              },
              indicatorId: {
                type: 'number',
              },
            },
            required: ['reporting_period', 'dfId', 'level', 'locationId', 'userProfileId', 'categoryId', 'topicId', 'indicatorId'],
          }
        }
      },
    })
    requestBody: {
      reporting_period: string;
      dfId: number;
      locationId: any;
      level: any;
      topicId: number;
      indicatorId: number;
      categoryId: number;
      userProfileId: number;
    },
  ): Promise<any> {
    const {reporting_period, dfId, locationId, level, userProfileId, categoryId, topicId, indicatorId} = requestBody
    let find = await this.qualitativeSubmissionRepository.find({where: {userProfileId, level, locationId, dfId, categoryId, topicId, indicatorId, type: 1}})
    console.log(find, 'find')
    if (find) {
      let filter = find.filter(item => item.reporting_period && this.getRPTextFormat([item.reporting_period[item.reporting_period.length - 1]]) === this.getPreviousPeriod(reporting_period))
      console.log(filter, 'filter')
      if (filter.length === 1) {
        return {result: true, data: filter[0]}
      } else {
        return {result: null, data: filter}
      }
    } else {
      return {result: false}
    }

  }
  getRPTextFormat(item: string[]) {
    if (item.length !== 0) {
      if (item.length >= 2) {
        const startDate = DateTime.fromFormat(item[0], "MM-yyyy").toFormat(
          "LLL-yyyy"
        );
        const endDate = DateTime.fromFormat(
          item[item.length - 1],
          "MM-yyyy"
        ).toFormat("LLL-yyyy");
        return `${startDate} to ${endDate}`;
      } else {
        return DateTime.fromFormat(item[0], "MM-yyyy").toFormat("LLL-yyyy");
      }
    }
  };
  getPreviousPeriod(key: string) {
    if (!key.includes(" to ")) {
      const [month, year] = key.split("-");
      const previousDate = DateTime.fromFormat(
        `${month}-1-${year}`,
        "LLL-d-yyyy"
      ).minus({months: 1});
      const previousMonth = previousDate.toFormat("LLL");
      const previousYear = previousDate.year;
      return previousMonth + "-" + previousYear;
    } else {
      const endMonth = key.split(" to ")[0].split("-")[0].trim();
      const endYear = key.split(" to ")[0].split("-")[1].trim();
      const previousDate = DateTime.fromFormat(
        `${endMonth}-1-${endYear}`,
        "LLL-d-yyyy"
      ).minus({months: 1});
      const previousMonth = previousDate.toFormat("LLL");
      const previousYear = previousDate.year;
      return previousMonth + "-" + previousYear;
    }
  }
}
