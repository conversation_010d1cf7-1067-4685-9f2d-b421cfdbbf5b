import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewMetric, QnIndicatorApproval, QnIndicatorApprovalRelations} from '../models';
import {NewMetricRepository} from './new-metric.repository';

export class QnIndicatorApprovalRepository extends DefaultCrudRepository<
  QnIndicatorApproval,
  typeof QnIndicatorApproval.prototype.id,
  QnIndicatorApprovalRelations
> {

  public readonly newMetric: BelongsToAccessor<NewMetric, typeof QnIndicatorApproval.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewMetricRepository') protected newMetricRepositoryGetter: Getter<NewMetricRepository>,
  ) {
    super(QnIndicatorApproval, dataSource);
    this.newMetric = this.createBelongsToAccessorFor('indicator', newMetricRepositoryGetter,);
    this.registerInclusionResolver('indicator', this.newMetric.inclusionResolver);
  }
}
