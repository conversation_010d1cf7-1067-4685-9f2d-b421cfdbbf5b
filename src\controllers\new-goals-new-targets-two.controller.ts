import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewGoals,
  NewTargetsTwo,
} from '../models';
import {NewGoalsRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewGoalsNewTargetsTwoController {
  constructor(
    @repository(NewGoalsRepository) protected newGoalsRepository: NewGoalsRepository,
  ) { }

  @get('/new-goals/{id}/new-targets-twos', {
    responses: {
      '200': {
        description: 'Array of NewGoals has many NewTargetsTwo',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewTargetsTwo)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewTargetsTwo>,
  ): Promise<NewTargetsTwo[]> {
    return this.newGoalsRepository.newTargetsTwos(id).find(filter);
  }

  @post('/new-goals/{id}/new-targets-twos', {
    responses: {
      '200': {
        description: 'NewGoals model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewTargetsTwo)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewGoals.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTargetsTwo, {
            title: 'NewNewTargetsTwoInNewGoals',
            exclude: ['id'],
            optional: ['newGoalsId']
          }),
        },
      },
    }) newTargetsTwo: Omit<NewTargetsTwo, 'id'>,
  ): Promise<NewTargetsTwo> {
    return this.newGoalsRepository.newTargetsTwos(id).create(newTargetsTwo);
  }

  // @patch('/new-goals/{id}/new-targets-twos', {
  //   responses: {
  //     '200': {
  //       description: 'NewGoals.NewTargetsTwo PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewTargetsTwo, {partial: true}),
  //       },
  //     },
  //   })
  //   newTargetsTwo: Partial<NewTargetsTwo>,
  //   @param.query.object('where', getWhereSchemaFor(NewTargetsTwo)) where?: Where<NewTargetsTwo>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.newTargetsTwos(id).patch(newTargetsTwo, where);
  // }

  // @del('/new-goals/{id}/new-targets-twos', {
  //   responses: {
  //     '200': {
  //       description: 'NewGoals.NewTargetsTwo DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewTargetsTwo)) where?: Where<NewTargetsTwo>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.newTargetsTwos(id).delete(where);
  // }
  
}
