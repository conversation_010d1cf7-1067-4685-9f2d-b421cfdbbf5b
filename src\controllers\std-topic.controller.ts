import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {StdTopic} from '../models';
import {StdTopicRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdTopicController {
  constructor(
    @repository(StdTopicRepository)
    public stdTopicRepository : StdTopicRepository,
  ) {}

  @post('/std-topics')
  @response(200, {
    description: 'StdTopic model instance',
    content: {'application/json': {schema: getModelSchemaRef(StdTopic)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdTopic, {
            title: 'NewStdTopic',
            exclude: ['id'],
          }),
        },
      },
    })
    stdTopic: Omit<StdTopic, 'id'>,
  ): Promise<StdTopic> {
    return this.stdTopicRepository.create(stdTopic);
  }

  @get('/std-topics/count')
  @response(200, {
    description: 'StdTopic model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(StdTopic) where?: Where<StdTopic>,
  ): Promise<Count> {
    return this.stdTopicRepository.count(where);
  }

  @get('/std-topics')
  @response(200, {
    description: 'Array of StdTopic model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(StdTopic, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(StdTopic) filter?: Filter<StdTopic>,
  ): Promise<StdTopic[]> {
    return this.stdTopicRepository.find(filter);
  }

  // @patch('/std-topics')
  // @response(200, {
  //   description: 'StdTopic PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StdTopic, {partial: true}),
  //       },
  //     },
  //   })
  //   stdTopic: StdTopic,
  //   @param.where(StdTopic) where?: Where<StdTopic>,
  // ): Promise<Count> {
  //   return this.stdTopicRepository.updateAll(stdTopic, where);
  // }

  @get('/std-topics/{id}')
  @response(200, {
    description: 'StdTopic model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(StdTopic, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(StdTopic, {exclude: 'where'}) filter?: FilterExcludingWhere<StdTopic>
  ): Promise<StdTopic> {
    return this.stdTopicRepository.findById(id, filter);
  }

  @patch('/std-topics/{id}')
  @response(204, {
    description: 'StdTopic PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdTopic, {partial: true}),
        },
      },
    })
    stdTopic: StdTopic,
  ): Promise<void> {
    await this.stdTopicRepository.updateById(id, stdTopic);
  }

  @put('/std-topics/{id}')
  @response(204, {
    description: 'StdTopic PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() stdTopic: StdTopic,
  ): Promise<void> {
    await this.stdTopicRepository.replaceById(id, stdTopic);
  }

  @del('/std-topics/{id}')
  @response(204, {
    description: 'StdTopic DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.stdTopicRepository.deleteById(id);
  }
}
