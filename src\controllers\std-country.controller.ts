import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {StdCountry} from '../models';
import {StdCountryRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdCountryController {
  constructor(
    @repository(StdCountryRepository)
    public stdCountryRepository : StdCountryRepository,
  ) {}

  @post('/std-countries')
  @response(200, {
    description: 'StdCountry model instance',
    content: {'application/json': {schema: getModelSchemaRef(StdCountry)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdCountry, {
            title: 'NewStdCountry',
            exclude: ['id'],
          }),
        },
      },
    })
    stdCountry: Omit<StdCountry, 'id'>,
  ): Promise<StdCountry> {
    return this.stdCountryRepository.create(stdCountry);
  }

  @get('/std-countries/count')
  @response(200, {
    description: 'StdCountry model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(StdCountry) where?: Where<StdCountry>,
  ): Promise<Count> {
    return this.stdCountryRepository.count(where);
  }

  @get('/std-countries')
  @response(200, {
    description: 'Array of StdCountry model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(StdCountry, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(StdCountry) filter?: Filter<StdCountry>,
  ): Promise<StdCountry[]> {
    return this.stdCountryRepository.find(filter);
  }

  // @patch('/std-countries')
  // @response(200, {
  //   description: 'StdCountry PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StdCountry, {partial: true}),
  //       },
  //     },
  //   })
  //   stdCountry: StdCountry,
  //   @param.where(StdCountry) where?: Where<StdCountry>,
  // ): Promise<Count> {
  //   return this.stdCountryRepository.updateAll(stdCountry, where);
  // }

  @get('/std-countries/{id}')
  @response(200, {
    description: 'StdCountry model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(StdCountry, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(StdCountry, {exclude: 'where'}) filter?: FilterExcludingWhere<StdCountry>
  ): Promise<StdCountry> {
    return this.stdCountryRepository.findById(id, filter);
  }

  @patch('/std-countries/{id}')
  @response(204, {
    description: 'StdCountry PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdCountry, {partial: true}),
        },
      },
    })
    stdCountry: StdCountry,
  ): Promise<void> {
    await this.stdCountryRepository.updateById(id, stdCountry);
  }

  @put('/std-countries/{id}')
  @response(204, {
    description: 'StdCountry PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() stdCountry: StdCountry,
  ): Promise<void> {
    await this.stdCountryRepository.replaceById(id, stdCountry);
  }

  @del('/std-countries/{id}')
  @response(204, {
    description: 'StdCountry DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.stdCountryRepository.deleteById(id);
  }
}
