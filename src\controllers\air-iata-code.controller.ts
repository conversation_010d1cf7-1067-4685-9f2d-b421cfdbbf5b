import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response
} from '@loopback/rest';
import {AirIataCode} from '../models';
import {AirIataCodeRepository} from '../repositories';

export class AirIataCodeController {
  constructor(
    @repository(AirIataCodeRepository)
    public airIataCodeRepository: AirIataCodeRepository,
  ) { }

  @post('/air-iata-codes')
  @response(200, {
    description: 'AirIataCode model instance',
    content: {'application/json': {schema: getModelSchemaRef(AirIataCode)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {
            title: 'NewAirIataCode',
            exclude: ['id'],
          }),
        },
      },
    })
    airIataCode: Omit<AirIataCode, 'id'>,
  ): Promise<AirIataCode> {
    return this.airIataCodeRepository.create(airIataCode);
  }

  @get('/air-iata-codes/count')
  @response(200, {
    description: 'AirIataCode model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AirIataCode) where?: Where<AirIataCode>,
  ): Promise<Count> {
    return this.airIataCodeRepository.count(where);
  }

  @get('/air-iata-codes')
  @response(200, {
    description: 'Array of AirIataCode model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirIataCode, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AirIataCode) filter?: Filter<AirIataCode>,
  ): Promise<AirIataCode[]> {
    return this.airIataCodeRepository.find(filter);
  }

  @patch('/air-iata-codes')
  @response(200, {
    description: 'AirIataCode PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {partial: true}),
        },
      },
    })
    airIataCode: AirIataCode,
    @param.where(AirIataCode) where?: Where<AirIataCode>,
  ): Promise<Count> {
    return this.airIataCodeRepository.updateAll(airIataCode, where);
  }

  @get('/air-iata-codes/{id}')
  @response(200, {
    description: 'AirIataCode model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AirIataCode, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AirIataCode, {exclude: 'where'}) filter?: FilterExcludingWhere<AirIataCode>
  ): Promise<AirIataCode> {
    const result = await this.airIataCodeRepository.findById(id, filter);
    console.log('Retrieved from DB:', result);
    return result;
  }

  @patch('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {partial: true}),
        },
      },
    })
    airIataCode: AirIataCode,
  ): Promise<void> {
    await this.airIataCodeRepository.updateById(id, airIataCode);
  }

  @put('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() airIataCode: AirIataCode,
  ): Promise<void> {
    await this.airIataCodeRepository.replaceById(id, airIataCode);
  }

  @del('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.airIataCodeRepository.deleteById(id);
  }

  @del('/air-iata-codes')
  @response(200, {
    description: 'Delete all AirIataCode records with optional where filter',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            deletedCount: {type: 'number'}
          }
        }
      }
    }
  })
  async deleteAll(
    @param.where(AirIataCode) where?: Where<AirIataCode>,
  ): Promise<{status: boolean; message: string; deletedCount: number}> {
    try {
      // Delete records based on where filter
      const deleteResult = await this.airIataCodeRepository.deleteAll(where);

      const deletedCount = deleteResult.count || 0;

      return {
        status: true,
        message: where
          ? `Successfully deleted ${deletedCount} IATA code records matching the filter criteria`
          : `Successfully deleted all ${deletedCount} IATA code records`,
        deletedCount
      };
    } catch (error) {
      return {
        status: false,
        message: `Error deleting IATA codes: ${error instanceof Error ? error.message : 'Unknown error'}`,
        deletedCount: 0
      };
    }
  }

  @post('/air-iata-codes/upload')
  @response(200, {
    description: 'Upload JSON data to populate IATA codes',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            totalRecords: {type: 'number'},
            successfulRecords: {type: 'number'},
            failedRecords: {type: 'number'},
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  row: {type: 'number'},
                  error: {type: 'string'},
                  data: {type: 'object'}
                }
              }
            }
          }
        }
      }
    }
  })
  async uploadJson(
    @requestBody({
      description: 'JSON data array for IATA codes',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              data: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    name: {type: 'string', description: 'Airport name (required)'},
                    latitude_deg: {type: 'string', description: 'Latitude in degrees as string (required)'},
                    longitude_deg: {type: 'string', description: 'Longitude in degrees as string (required)'},
                    iata_code: {type: 'string', description: 'IATA code (required)'},
                    iso_country: {type: 'string', description: 'Country code (optional)'},
                    iso_region: {type: 'string', description: 'Region code (optional)'},
                    municipality: {type: 'string', description: 'Location name (optional)'}
                  },
                  additionalProperties: true
                }
              }
            },
            required: ['data']
          }
        },
      },
    })
    requestBody: {data: any[]},
  ): Promise<any> {
    try {
      // Validate input
      if (!requestBody || typeof requestBody !== 'object') {
        return {
          status: false,
          message: 'Request body must be an object with a "data" property',
          totalRecords: 0,
          successfulRecords: 0,
          failedRecords: 0,
          errors: []
        };
      }

      if (!requestBody.data || !Array.isArray(requestBody.data)) {
        return {
          status: false,
          message: 'Request body must contain a "data" property that is an array of objects',
          totalRecords: 0,
          successfulRecords: 0,
          failedRecords: 0,
          errors: []
        };
      }

      if (requestBody.data.length === 0) {
        return {
          status: false,
          message: 'Data array cannot be empty',
          totalRecords: 0,
          successfulRecords: 0,
          failedRecords: 0,
          errors: []
        };
      }

      const results = await this.processJsonData(requestBody.data);
      return results;
    } catch (error) {
      return {
        status: false,
        message: `Processing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        errors: []
      };
    }
  }

  /**
   * Process JSON data and insert into database
   */
  private async processJsonData(jsonData: any[]): Promise<any> {
    const results: any[] = [];
    const errors: any[] = [];
    let totalRecords = jsonData.length;
    let successfulRecords = 0;
    let failedRecords = 0;

    // Process each record
    for (let i = 0; i < jsonData.length; i++) {
      const data = jsonData[i];
      const rowNumber = i + 1;

      try {
        // Map JSON data to model properties
        const mappedData = this.mapJsonToModel(data);

        // Validate required fields
        const validation = this.validateRecord(mappedData);
        if (!validation.isValid) {
          errors.push({
            row: rowNumber,
            error: validation.error,
            data: data
          });
          failedRecords++;
          continue;
        }

        results.push(mappedData);
      } catch (error) {
        errors.push({
          row: rowNumber,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: data
        });
        failedRecords++;
      }
    }

    // Insert valid records into database
    for (const record of results) {
      try {
        const savedRecord = await this.airIataCodeRepository.create(record);

        successfulRecords++;
      } catch (dbError) {
        const rowIndex = results.indexOf(record) + 1;
        errors.push({
          row: rowIndex,
          error: `Database error: ${dbError instanceof Error ? dbError.message : 'Unknown database error'}`,
          data: record
        });
        failedRecords++;
      }
    }

    return {
      status: true,
      message: `Processing completed. ${successfulRecords} records inserted successfully.`,
      totalRecords,
      successfulRecords,
      failedRecords,
      errors
    };
  }

  /**
   * Map JSON data to model properties
   */
  private mapJsonToModel(jsonData: any): Partial<AirIataCode> {
    const currentDate = new Date().toISOString();

    return {
      iataCode: jsonData.iata_code?.toString().trim() || '',
      airportName: jsonData.name?.toString().trim() || '',
      lat: this.validateAndFormatCoordinate(jsonData.latitude_deg),
      long: this.validateAndFormatCoordinate(jsonData.longitude_deg),
      countryCode: jsonData.iso_country?.toString().trim() || null,
      regionCode: jsonData.iso_region?.toString().trim() || null,
      locationName: jsonData.municipality?.toString().trim() || null,
      created_on: currentDate,
      modified_on: currentDate
    };
  }

  /**
   * Validate and format coordinate value as string
   */
  private validateAndFormatCoordinate(value: any): string {
    if (value === null || value === undefined || value === '') {
      throw new Error('Coordinate value cannot be empty');
    }

    const stringValue = value.toString();
    const parsed = parseFloat(stringValue);

    if (isNaN(parsed)) {
      throw new Error(`Invalid coordinate value: ${value}`);
    }

    // Return the original string value without trimming to preserve decimals
    return stringValue;
  }

  /**
   * Validate record data
   */
  private validateRecord(data: Partial<AirIataCode>): {isValid: boolean; error?: string} {
    // Check mandatory fields
    if (!data.iataCode || data.iataCode.trim() === '') {
      return {isValid: false, error: 'iata_code is mandatory and cannot be empty'};
    }

    if (!data.airportName || data.airportName.trim() === '') {
      return {isValid: false, error: 'name is mandatory and cannot be empty'};
    }

    if (data.lat === undefined || data.lat === null) {
      return {isValid: false, error: 'latitude_deg is mandatory'};
    }

    if (data.long === undefined || data.long === null) {
      return {isValid: false, error: 'longitude_deg is mandatory'};
    }

    // Validate latitude range (-90 to 90)
    const latValue = parseFloat(data.lat);
    if (isNaN(latValue) || latValue < -90 || latValue > 90) {
      return {isValid: false, error: 'latitude_deg must be a valid number between -90 and 90'};
    }

    // Validate longitude range (-180 to 180)
    const longValue = parseFloat(data.long);
    if (isNaN(longValue) || longValue < -180 || longValue > 180) {
      return {isValid: false, error: 'longitude_deg must be a valid number between -180 and 180'};
    }

    // Validate IATA code format (3 characters)
    if (data.iataCode.length !== 3) {
      return {isValid: false, error: 'iata_code must be exactly 3 characters'};
    }

    return {isValid: true};
  }

  @post('/air-iata-codes/calculate-flight-distances')
  @response(200, {
    description: 'Calculate distances for flight data',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            totalRecords: {type: 'number'},
            validRecords: {type: 'number'},
            rejectedRecords: {type: 'number'},
            validFlights: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  no_of_passenger: {type: 'number'},
                  class: {type: 'string'},
                  from: {type: 'string'},
                  to: {type: 'string'},
                  date: {type: 'string'},
                  distance: {type: 'number'}
                }
              }
            },
            rejectedFlights: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  originalData: {type: 'object'},
                  remarks: {type: 'string'}
                }
              }
            }
          }
        }
      }
    }
  })
  async calculateFlightDistances(
    @requestBody({
      description: 'Array of flight objects to validate and calculate distances',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                no_of_passenger: {
                  oneOf: [
                    {type: 'number'},
                    {type: 'string'}
                  ],
                  description: 'Number of passengers (number or string that can be converted to number)'
                },
                class: {type: 'string', description: 'Flight class'},
                from: {type: 'string', description: '3-digit IATA code for departure airport'},
                to: {type: 'string', description: '3-digit IATA code for arrival airport'},
                date: {
                  oneOf: [
                    {type: 'number'},
                    {type: 'string'}
                  ],
                  description: 'Flight date (number or string)'
                }
              },
              required: ['no_of_passenger', 'class', 'from', 'to', 'date']
            }
          }
        }
      }
    })
    flightData: any[]
  ): Promise<any> {
    try {
      if (!Array.isArray(flightData)) {
        return {
          status: false,
          message: 'Request body must be an array of flight objects',
          totalRecords: 0,
          validRecords: 0,
          rejectedRecords: 0,
          validFlights: [],
          rejectedFlights: []
        };
      }

      if (flightData.length === 0) {
        return {
          status: false,
          message: 'Flight data array cannot be empty',
          totalRecords: 0,
          validRecords: 0,
          rejectedRecords: 0,
          validFlights: [],
          rejectedFlights: []
        };
      }

      const validFlights: any[] = [];
      const rejectedFlights: any[] = [];

      // Get all IATA codes from database for validation
      const allIataCodes = await this.airIataCodeRepository.find({
        fields: ['iataCode', 'lat', 'long']
      });

      // Create a map for faster lookup (case-insensitive)
      const iataMap = new Map<string, {lat: string, long: string}>();
      allIataCodes.forEach(airport => {
        if (airport.iataCode) {
          iataMap.set(airport.iataCode.toLowerCase(), {
            lat: airport.lat,
            long: airport.long
          });
        }
      });

      // Process each flight record
      for (let i = 0; i < flightData.length; i++) {
        const flight = flightData[i];
        const validation = this.validateFlightData(flight, iataMap);

        if (validation.isValid && validation.processedFlight) {
          // Calculate distance between airports
          const fromCoords = iataMap.get(validation.processedFlight.from.toLowerCase());
          const toCoords = iataMap.get(validation.processedFlight.to.toLowerCase());

          if (fromCoords && toCoords) {
            const distance = this.calculateDistance(
              parseFloat(fromCoords.lat),
              parseFloat(fromCoords.long),
              parseFloat(toCoords.lat),
              parseFloat(toCoords.long)
            );

            // Generate UUID for the record
            const uuid = this.generateUUID();

            // Find the air class type object
            const airClassType = [
              {name: "Economy", efKey: "287-396-956-612"},
              {name: "Premium", efKey: "287-396-957-613"},
              {name: "First", efKey: "287-396-959-615"},
              {name: "Business", efKey: "287-396-958-614"},
            ];

            const classTypeObj = airClassType.find(x =>
              x.name.toString().trim().toLowerCase() === validation.processedFlight!.class.toString().trim().toLowerCase()
            );

            validFlights.push({
              "DPA0290": validation.processedFlight!.date, // UTC date
              "DPA0291": {name: "Air"}, // common
              "id": uuid,
              "DPA0292": parseInt(validation.processedFlight!.no_of_passenger.toString()),
              "DPA0293": validation.processedFlight!.from.trim().toLowerCase(),
              "DPA0294": validation.processedFlight!.to.trim().toLowerCase(),
              "DPA0296": classTypeObj,
              "DP_KM": Math.round(distance * 100) / 100 // Round to 2 decimal places
            });
          } else {
            // Create rejection data with formatted date
            const rejectionData = this.createRejectionData(flight);
            rejectedFlights.push({
              originalData: rejectionData,
              remarks: 'Unable to find coordinates for one or both airports'
            });
          }
        } else {
          // Create rejection data with formatted date
          const rejectionData = this.createRejectionData(flight);
          rejectedFlights.push({
            originalData: rejectionData,
            remarks: validation.error || 'Unknown validation error'
          });
        }
      }

      return {
        status: true,
        message: `Processing completed. ${validFlights.length} flights processed successfully, ${rejectedFlights.length} flights rejected.`,
        totalRecords: flightData.length,
        validRecords: validFlights.length,
        rejectedRecords: rejectedFlights.length,
        validFlights,
        rejectedFlights
      };

    } catch (error) {
      return {
        status: false,
        message: `Processing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        totalRecords: 0,
        validRecords: 0,
        rejectedRecords: 0,
        validFlights: [],
        rejectedFlights: []
      };
    }
  }

  /**
   * Validate flight data
   */
  private validateFlightData(
    flight: any,
    iataMap: Map<string, {lat: string, long: string}>
  ): {isValid: boolean; error?: string; processedFlight?: any} {
    // Define valid air class types
    const airClassType = [
      {name: "Economy", efKey: "287-396-956-612"},
      {name: "Premium", efKey: "287-396-957-613"},
      {name: "First", efKey: "287-396-959-615"},
      {name: "Business", efKey: "287-396-958-614"},
    ];

    // Check if flight is an object
    if (!flight || typeof flight !== 'object') {
      return {isValid: false, error: 'Flight data must be an object'};
    }

    // Validate no_of_passenger
    if (flight.no_of_passenger === undefined || flight.no_of_passenger === null) {
      return {isValid: false, error: 'no_of_passenger is required'};
    }

    let passengerCount: number;
    if (typeof flight.no_of_passenger === 'string') {
      passengerCount = parseFloat(flight.no_of_passenger.trim());
      if (isNaN(passengerCount)) {
        return {isValid: false, error: 'no_of_passenger must be a valid number'};
      }
    } else if (typeof flight.no_of_passenger === 'number') {
      passengerCount = flight.no_of_passenger;
    } else {
      return {isValid: false, error: 'no_of_passenger must be a number or string'};
    }

    if (passengerCount <= 0) {
      return {isValid: false, error: 'no_of_passenger must be greater than 0'};
    }

    // Validate class (case-insensitive matching)
    if (!flight.class || typeof flight.class !== 'string' || flight.class.trim() === '') {
      return {isValid: false, error: 'class is required and must be a non-empty string'};
    }

    const flightClass = flight.class.trim();
    const validClass = airClassType.find(classType =>
      classType.name.toLowerCase() === flightClass.toLowerCase()
    );

    if (!validClass) {
      const validClassNames = airClassType.map(c => c.name).join(', ');
      return {isValid: false, error: `class must be one of: ${validClassNames} (case-insensitive)`};
    }

    // Validate from (3-digit IATA code)
    if (!flight.from || typeof flight.from !== 'string' || flight.from.trim().length !== 3) {
      return {isValid: false, error: 'from must be a 3-character IATA code'};
    }

    const fromCode = flight.from.trim().toLowerCase();
    if (!iataMap.has(fromCode)) {
      return {isValid: false, error: `from airport code '${flight.from}' not found in IATA database`};
    }

    // Validate to (3-digit IATA code)
    if (!flight.to || typeof flight.to !== 'string' || flight.to.trim().length !== 3) {
      return {isValid: false, error: 'to must be a 3-character IATA code'};
    }

    const toCode = flight.to.trim().toLowerCase();
    if (!iataMap.has(toCode)) {
      return {isValid: false, error: `to airport code '${flight.to}' not found in IATA database`};
    }

    // Check if from and to are the same
    if (fromCode === toCode) {
      return {isValid: false, error: 'from and to airports cannot be the same'};
    }

    // Validate date
    if (flight.date === undefined || flight.date === null) {
      return {isValid: false, error: 'date is required'};
    }

    let dateValue: string;
    if (typeof flight.date === 'number') {
      dateValue = flight.date.toString();
    } else if (typeof flight.date === 'string') {
      dateValue = flight.date.trim();
    } else {
      return {isValid: false, error: 'date must be a number or string'};
    }

    if (dateValue === '') {
      return {isValid: false, error: 'date cannot be empty'};
    }

    // Parse and validate date, convert to UTC
    const parsedDate = this.parseAndValidateDate(dateValue);
    if (!parsedDate.isValid) {
      return {isValid: false, error: parsedDate.error};
    }

    return {
      isValid: true,
      processedFlight: {
        no_of_passenger: passengerCount,
        class: validClass.name, // Use the matched class name with proper casing
        from: flight.from.trim().toUpperCase(),
        to: flight.to.trim().toUpperCase(),
        date: parsedDate.utcDate,
        efKey: validClass.efKey // Include efKey for reference
      }
    };
  }

  /**
   * Parse and validate date, convert to UTC format
   */
  private parseAndValidateDate(dateValue: string): {isValid: boolean; error?: string; utcDate?: string; formattedDate?: string} {

    let parsedDate: Date | null = null;
    let formattedDate = 'Invalid';

    try {
      // Try ISO format first (YYYY-MM-DD)
      if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(dateValue)) {
        parsedDate = new Date(dateValue + 'T00:00:00.000Z');
        if (!isNaN(parsedDate.getTime())) {
          const parts = dateValue.split('-');
          formattedDate = `${parts[2].padStart(2, '0')}-${parts[1].padStart(2, '0')}-${parts[0]}`;
        }
      }
      // Try DD-MM-YYYY format
      else if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(dateValue)) {
        const parts = dateValue.split('-');
        if (parts.length === 3) {
          const day = parseInt(parts[0]);
          const month = parseInt(parts[1]);
          const year = parseInt(parts[2]);

          if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900) {
            parsedDate = new Date(Date.UTC(year, month - 1, day));
            formattedDate = `${day.toString().padStart(2, '0')}-${month.toString().padStart(2, '0')}-${year}`;
          }
        }
      }
      // Try DD/MM/YYYY format
      else if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateValue)) {
        const parts = dateValue.split('/');
        if (parts.length === 3) {
          const day = parseInt(parts[0]);
          const month = parseInt(parts[1]);
          const year = parseInt(parts[2]);

          if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900) {
            parsedDate = new Date(Date.UTC(year, month - 1, day));
            formattedDate = `${day.toString().padStart(2, '0')}-${month.toString().padStart(2, '0')}-${year}`;
          }
        }
      }
      // Try YYYYMMDD format
      else if (/^\d{8}$/.test(dateValue) && parseInt(dateValue.substring(0, 4)) >= 1900) {
        const year = parseInt(dateValue.substring(0, 4));
        const month = parseInt(dateValue.substring(4, 6));
        const day = parseInt(dateValue.substring(6, 8));

        if (day >= 1 && day <= 31 && month >= 1 && month <= 12) {
          parsedDate = new Date(Date.UTC(year, month - 1, day));
          formattedDate = `${day.toString().padStart(2, '0')}-${month.toString().padStart(2, '0')}-${year}`;
        }
      }
      // Try DDMMYYYY format (if year is reasonable)
      else if (/^\d{8}$/.test(dateValue) && parseInt(dateValue.substring(4, 8)) >= 1900) {
        const day = parseInt(dateValue.substring(0, 2));
        const month = parseInt(dateValue.substring(2, 4));
        const year = parseInt(dateValue.substring(4, 8));

        if (day >= 1 && day <= 31 && month >= 1 && month <= 12) {
          parsedDate = new Date(Date.UTC(year, month - 1, day));
          formattedDate = `${day.toString().padStart(2, '0')}-${month.toString().padStart(2, '0')}-${year}`;
        }
      }

      if (!parsedDate || isNaN(parsedDate.getTime())) {
        return {
          isValid: false,
          error: `Invalid date format. Expected formats: YYYY-MM-DD, DD-MM-YYYY, DD/MM/YYYY, YYYYMMDD, or DDMMYYYY. Received: ${dateValue}`,
          formattedDate
        };
      }

      // Convert to UTC ISO string
      const utcDate = parsedDate.toISOString();

      return {
        isValid: true,
        utcDate,
        formattedDate
      };

    } catch (error) {
      return {
        isValid: false,
        error: `Date parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        formattedDate
      };
    }
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   * Returns distance in kilometers
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Generate a simple UUID in format "a1b2c3d4-e5f6-4789"
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-xxxx'.replace(/[x]/g, function () {
      const r = Math.random() * 16 | 0;
      return r.toString(16);
    });
  }

  /**
   * Create rejection data with formatted date
   */
  private createRejectionData(flight: any): any {
    const rejectionData = {...flight};

    // Format the date for rejection
    if (flight.date !== undefined && flight.date !== null) {
      let dateValue: string;
      if (typeof flight.date === 'number') {
        dateValue = flight.date.toString();
      } else if (typeof flight.date === 'string') {
        dateValue = flight.date.trim();
      } else {
        rejectionData.date = 'Invalid';
        return rejectionData;
      }

      if (dateValue === '') {
        rejectionData.date = 'Invalid';
        return rejectionData;
      }

      // Try to parse and format the date
      const parsedDate = this.parseAndValidateDate(dateValue);
      rejectionData.date = parsedDate.formattedDate || 'Invalid';
    } else {
      rejectionData.date = 'Invalid';
    }

    return rejectionData;
  }

  @get('/air-iata-codes/distance')
  @response(200, {
    description: 'Calculate distance between two IATA codes',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            distance: {type: 'number', description: 'Distance in kilometers'}
          }
        }
      }
    }
  })
  async getDistanceBetweenAirports(
    @param.query.string('from', {
      description: '3-digit IATA code for departure airport',
      required: false
    }) from?: string,
    @param.query.string('to', {
      description: '3-digit IATA code for arrival airport',
      required: false
    }) to?: string
  ): Promise<any> {
    try {
      // Check if from parameter is missing
      if (!from || from.trim() === '') {
        return {
          status: false,
          message: 'Missing from parameter'
        };
      }

      // Check if to parameter is missing
      if (!to || to.trim() === '') {
        return {
          status: false,
          message: 'Missing to parameter'
        };
      }

      const fromCode = from.trim();
      const toCode = to.trim();

      // Validate IATA code format (3 characters)
      if (fromCode.length !== 3) {
        return {
          status: false,
          message: 'from code must be exactly 3 characters'
        };
      }

      if (toCode.length !== 3) {
        return {
          status: false,
          message: 'to code must be exactly 3 characters'
        };
      }

      // Check if from and to are the same (case-insensitive)
      if (fromCode.toLowerCase() === toCode.toLowerCase()) {
        return {
          status: false,
          message: 'from and to airports cannot be the same'
        };
      }

      // Get all IATA codes and find matches (case-insensitive)
      const allAirports = await this.airIataCodeRepository.find({
        fields: ['iataCode', 'lat', 'long', 'airportName']
      });

      const fromAirport = allAirports.find(airport =>
        airport.iataCode?.toLowerCase() === fromCode.toLowerCase()
      );

      const toAirport = allAirports.find(airport =>
        airport.iataCode?.toLowerCase() === toCode.toLowerCase()
      );

      // Check which airports are invalid
      const invalidCodes: string[] = [];
      if (!fromAirport) {
        invalidCodes.push('from');
      }
      if (!toAirport) {
        invalidCodes.push('to');
      }

      if (invalidCodes.length > 0) {
        let errorMessage: string;
        if (invalidCodes.length === 2) {
          errorMessage = 'Both codes are invalid';
        } else if (invalidCodes.includes('from')) {
          errorMessage = 'from code is invalid';
        } else {
          errorMessage = 'to code is invalid';
        }

        return {
          status: false,
          message: errorMessage
        };
      }

      // Calculate distance
      const distance = this.calculateDistance(
        parseFloat(fromAirport!.lat),
        parseFloat(fromAirport!.long),
        parseFloat(toAirport!.lat),
        parseFloat(toAirport!.long)
      );

      return {
        status: true,
        message: `Distance calculated successfully between ${fromAirport!.iataCode} and ${toAirport!.iataCode}`,
        distance: Math.round(distance * 100) / 100 // Round to 2 decimal places
      };

    } catch (error) {
      return {
        status: false,
        message: `Error calculating distance: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
