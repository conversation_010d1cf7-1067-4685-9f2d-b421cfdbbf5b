import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response
} from '@loopback/rest';
import {AirIataCode} from '../models';
import {AirIataCodeRepository} from '../repositories';

export class AirIataCodeController {
  constructor(
    @repository(AirIataCodeRepository)
    public airIataCodeRepository: AirIataCodeRepository,
  ) { }

  @post('/air-iata-codes')
  @response(200, {
    description: 'AirIataCode model instance',
    content: {'application/json': {schema: getModelSchemaRef(AirIataCode)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {
            title: 'NewAirIataCode',
            exclude: ['id'],
          }),
        },
      },
    })
    airIataCode: Omit<AirIataCode, 'id'>,
  ): Promise<AirIataCode> {
    return this.airIataCodeRepository.create(airIataCode);
  }

  @get('/air-iata-codes/count')
  @response(200, {
    description: 'AirIataCode model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AirIataCode) where?: Where<AirIataCode>,
  ): Promise<Count> {
    return this.airIataCodeRepository.count(where);
  }

  @get('/air-iata-codes')
  @response(200, {
    description: 'Array of AirIataCode model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AirIataCode, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AirIataCode) filter?: Filter<AirIataCode>,
  ): Promise<AirIataCode[]> {
    return this.airIataCodeRepository.find(filter);
  }

  @patch('/air-iata-codes')
  @response(200, {
    description: 'AirIataCode PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {partial: true}),
        },
      },
    })
    airIataCode: AirIataCode,
    @param.where(AirIataCode) where?: Where<AirIataCode>,
  ): Promise<Count> {
    return this.airIataCodeRepository.updateAll(airIataCode, where);
  }

  @get('/air-iata-codes/{id}')
  @response(200, {
    description: 'AirIataCode model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AirIataCode, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AirIataCode, {exclude: 'where'}) filter?: FilterExcludingWhere<AirIataCode>
  ): Promise<AirIataCode> {
    const result = await this.airIataCodeRepository.findById(id, filter);
    console.log('Retrieved from DB:', result);
    return result;
  }

  @patch('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AirIataCode, {partial: true}),
        },
      },
    })
    airIataCode: AirIataCode,
  ): Promise<void> {
    await this.airIataCodeRepository.updateById(id, airIataCode);
  }

  @put('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() airIataCode: AirIataCode,
  ): Promise<void> {
    await this.airIataCodeRepository.replaceById(id, airIataCode);
  }

  @del('/air-iata-codes/{id}')
  @response(204, {
    description: 'AirIataCode DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.airIataCodeRepository.deleteById(id);
  }

  @del('/air-iata-codes')
  @response(200, {
    description: 'Delete all AirIataCode records with optional where filter',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            deletedCount: {type: 'number'}
          }
        }
      }
    }
  })
  async deleteAll(
    @param.where(AirIataCode) where?: Where<AirIataCode>,
  ): Promise<{status: boolean; message: string; deletedCount: number}> {
    try {
      // Delete records based on where filter
      const deleteResult = await this.airIataCodeRepository.deleteAll(where);

      const deletedCount = deleteResult.count || 0;

      return {
        status: true,
        message: where
          ? `Successfully deleted ${deletedCount} IATA code records matching the filter criteria`
          : `Successfully deleted all ${deletedCount} IATA code records`,
        deletedCount
      };
    } catch (error) {
      return {
        status: false,
        message: `Error deleting IATA codes: ${error instanceof Error ? error.message : 'Unknown error'}`,
        deletedCount: 0
      };
    }
  }

  @post('/air-iata-codes/upload')
  @response(200, {
    description: 'Upload JSON data to populate IATA codes',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            totalRecords: {type: 'number'},
            successfulRecords: {type: 'number'},
            failedRecords: {type: 'number'},
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  row: {type: 'number'},
                  error: {type: 'string'},
                  data: {type: 'object'}
                }
              }
            }
          }
        }
      }
    }
  })
  async uploadJson(
    @requestBody({
      description: 'JSON data array for IATA codes',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              data: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    name: {type: 'string', description: 'Airport name (required)'},
                    latitude_deg: {type: 'string', description: 'Latitude in degrees as string (required)'},
                    longitude_deg: {type: 'string', description: 'Longitude in degrees as string (required)'},
                    iata_code: {type: 'string', description: 'IATA code (required)'},
                    iso_country: {type: 'string', description: 'Country code (optional)'},
                    iso_region: {type: 'string', description: 'Region code (optional)'},
                    municipality: {type: 'string', description: 'Location name (optional)'}
                  },
                  additionalProperties: true
                }
              }
            },
            required: ['data']
          }
        },
      },
    })
    requestBody: {data: any[]},
  ): Promise<any> {
    try {
      // Validate input
      if (!requestBody || typeof requestBody !== 'object') {
        return {
          status: false,
          message: 'Request body must be an object with a "data" property',
          totalRecords: 0,
          successfulRecords: 0,
          failedRecords: 0,
          errors: []
        };
      }

      if (!requestBody.data || !Array.isArray(requestBody.data)) {
        return {
          status: false,
          message: 'Request body must contain a "data" property that is an array of objects',
          totalRecords: 0,
          successfulRecords: 0,
          failedRecords: 0,
          errors: []
        };
      }

      if (requestBody.data.length === 0) {
        return {
          status: false,
          message: 'Data array cannot be empty',
          totalRecords: 0,
          successfulRecords: 0,
          failedRecords: 0,
          errors: []
        };
      }

      const results = await this.processJsonData(requestBody.data);
      return results;
    } catch (error) {
      return {
        status: false,
        message: `Processing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        totalRecords: 0,
        successfulRecords: 0,
        failedRecords: 0,
        errors: []
      };
    }
  }

  /**
   * Process JSON data and insert into database
   */
  private async processJsonData(jsonData: any[]): Promise<any> {
    const results: any[] = [];
    const errors: any[] = [];
    let totalRecords = jsonData.length;
    let successfulRecords = 0;
    let failedRecords = 0;

    // Process each record
    for (let i = 0; i < jsonData.length; i++) {
      const data = jsonData[i];
      const rowNumber = i + 1;

      try {
        // Map JSON data to model properties
        const mappedData = this.mapJsonToModel(data);

        // Validate required fields
        const validation = this.validateRecord(mappedData);
        if (!validation.isValid) {
          errors.push({
            row: rowNumber,
            error: validation.error,
            data: data
          });
          failedRecords++;
          continue;
        }

        results.push(mappedData);
      } catch (error) {
        errors.push({
          row: rowNumber,
          error: error instanceof Error ? error.message : 'Unknown error',
          data: data
        });
        failedRecords++;
      }
    }

    // Insert valid records into database
    for (const record of results) {
      try {
        const savedRecord = await this.airIataCodeRepository.create(record);

        successfulRecords++;
      } catch (dbError) {
        const rowIndex = results.indexOf(record) + 1;
        errors.push({
          row: rowIndex,
          error: `Database error: ${dbError instanceof Error ? dbError.message : 'Unknown database error'}`,
          data: record
        });
        failedRecords++;
      }
    }

    return {
      status: true,
      message: `Processing completed. ${successfulRecords} records inserted successfully.`,
      totalRecords,
      successfulRecords,
      failedRecords,
      errors
    };
  }

  /**
   * Map JSON data to model properties
   */
  private mapJsonToModel(jsonData: any): Partial<AirIataCode> {
    const currentDate = new Date().toISOString();

    return {
      iataCode: jsonData.iata_code?.toString().trim() || '',
      airportName: jsonData.name?.toString().trim() || '',
      lat: this.validateAndFormatCoordinate(jsonData.latitude_deg),
      long: this.validateAndFormatCoordinate(jsonData.longitude_deg),
      countryCode: jsonData.iso_country?.toString().trim() || null,
      regionCode: jsonData.iso_region?.toString().trim() || null,
      locationName: jsonData.municipality?.toString().trim() || null,
      created_on: currentDate,
      modified_on: currentDate
    };
  }

  /**
   * Validate and format coordinate value as string
   */
  private validateAndFormatCoordinate(value: any): string {
    if (value === null || value === undefined || value === '') {
      throw new Error('Coordinate value cannot be empty');
    }

    const stringValue = value.toString();
    const parsed = parseFloat(stringValue);

    if (isNaN(parsed)) {
      throw new Error(`Invalid coordinate value: ${value}`);
    }

    // Return the original string value without trimming to preserve decimals
    return stringValue;
  }

  /**
   * Validate record data
   */
  private validateRecord(data: Partial<AirIataCode>): {isValid: boolean; error?: string} {
    // Check mandatory fields
    if (!data.iataCode || data.iataCode.trim() === '') {
      return {isValid: false, error: 'iata_code is mandatory and cannot be empty'};
    }

    if (!data.airportName || data.airportName.trim() === '') {
      return {isValid: false, error: 'name is mandatory and cannot be empty'};
    }

    if (data.lat === undefined || data.lat === null) {
      return {isValid: false, error: 'latitude_deg is mandatory'};
    }

    if (data.long === undefined || data.long === null) {
      return {isValid: false, error: 'longitude_deg is mandatory'};
    }

    // Validate latitude range (-90 to 90)
    const latValue = parseFloat(data.lat);
    if (isNaN(latValue) || latValue < -90 || latValue > 90) {
      return {isValid: false, error: 'latitude_deg must be a valid number between -90 and 90'};
    }

    // Validate longitude range (-180 to 180)
    const longValue = parseFloat(data.long);
    if (isNaN(longValue) || longValue < -180 || longValue > 180) {
      return {isValid: false, error: 'longitude_deg must be a valid number between -180 and 180'};
    }

    // Validate IATA code format (3 characters)
    if (data.iataCode.length !== 3) {
      return {isValid: false, error: 'iata_code must be exactly 3 characters'};
    }

    return {isValid: true};
  }

  @post('/air-iata-codes/calculate-flight-distances')
  @response(200, {
    description: 'Calculate distances for flight data',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            totalRecords: {type: 'number'},
            validRecords: {type: 'number'},
            rejectedRecords: {type: 'number'},
            validFlights: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  no_of_passenger: {type: 'number'},
                  class: {type: 'string'},
                  from: {type: 'string'},
                  to: {type: 'string'},
                  date: {type: 'string'},
                  distance: {type: 'number'}
                }
              }
            },
            rejectedFlights: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  originalData: {type: 'object'},
                  remarks: {type: 'string'}
                }
              }
            }
          }
        }
      }
    }
  })
  async calculateFlightDistances(
    @requestBody({
      description: 'Array of flight objects to validate and calculate distances',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                no_of_passenger: {
                  oneOf: [
                    {type: 'number'},
                    {type: 'string'}
                  ],
                  description: 'Number of passengers (number or string that can be converted to number)'
                },
                class: {type: 'string', description: 'Flight class'},
                from: {type: 'string', description: '3-digit IATA code for departure airport'},
                to: {type: 'string', description: '3-digit IATA code for arrival airport'},
                date: {
                  oneOf: [
                    {type: 'number'},
                    {type: 'string'}
                  ],
                  description: 'Flight date (number or string)'
                }
              },
              required: ['no_of_passenger', 'class', 'from', 'to', 'date']
            }
          }
        }
      }
    })
    flightData: any[]
  ): Promise<any> {
    try {
      if (!Array.isArray(flightData)) {
        return {
          status: false,
          message: 'Request body must be an array of flight objects',
          totalRecords: 0,
          validRecords: 0,
          rejectedRecords: 0,
          validFlights: [],
          rejectedFlights: []
        };
      }

      if (flightData.length === 0) {
        return {
          status: false,
          message: 'Flight data array cannot be empty',
          totalRecords: 0,
          validRecords: 0,
          rejectedRecords: 0,
          validFlights: [],
          rejectedFlights: []
        };
      }

      const validFlights: any[] = [];
      const rejectedFlights: any[] = [];

      // Get all IATA codes from database for validation
      const allIataCodes = await this.airIataCodeRepository.find({
        fields: ['iataCode', 'lat', 'long']
      });

      // Create a map for faster lookup (case-insensitive)
      const iataMap = new Map<string, {lat: string, long: string}>();
      allIataCodes.forEach(airport => {
        if (airport.iataCode) {
          iataMap.set(airport.iataCode.toLowerCase(), {
            lat: airport.lat,
            long: airport.long
          });
        }
      });

      // Process each flight record
      for (let i = 0; i < flightData.length; i++) {
        const flight = flightData[i];
        const validation = this.validateFlightData(flight, iataMap);

        if (validation.isValid && validation.processedFlight) {
          // Calculate distance between airports
          const fromCoords = iataMap.get(validation.processedFlight.from.toLowerCase());
          const toCoords = iataMap.get(validation.processedFlight.to.toLowerCase());

          if (fromCoords && toCoords) {
            const distance = this.calculateDistance(
              parseFloat(fromCoords.lat),
              parseFloat(fromCoords.long),
              parseFloat(toCoords.lat),
              parseFloat(toCoords.long)
            );

            validFlights.push({
              ...validation.processedFlight,
              distance: Math.round(distance * 100) / 100 // Round to 2 decimal places
            });
          } else {
            rejectedFlights.push({
              originalData: flight,
              remarks: 'Unable to find coordinates for one or both airports'
            });
          }
        } else {
          rejectedFlights.push({
            originalData: flight,
            remarks: validation.error || 'Unknown validation error'
          });
        }
      }

      return {
        status: true,
        message: `Processing completed. ${validFlights.length} flights processed successfully, ${rejectedFlights.length} flights rejected.`,
        totalRecords: flightData.length,
        validRecords: validFlights.length,
        rejectedRecords: rejectedFlights.length,
        validFlights,
        rejectedFlights
      };

    } catch (error) {
      return {
        status: false,
        message: `Processing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        totalRecords: 0,
        validRecords: 0,
        rejectedRecords: 0,
        validFlights: [],
        rejectedFlights: []
      };
    }
  }

  /**
   * Validate flight data
   */
  private validateFlightData(
    flight: any,
    iataMap: Map<string, {lat: string, long: string}>
  ): {isValid: boolean; error?: string; processedFlight?: any} {
    // Check if flight is an object
    if (!flight || typeof flight !== 'object') {
      return {isValid: false, error: 'Flight data must be an object'};
    }

    // Validate no_of_passenger
    if (flight.no_of_passenger === undefined || flight.no_of_passenger === null) {
      return {isValid: false, error: 'no_of_passenger is required'};
    }

    let passengerCount: number;
    if (typeof flight.no_of_passenger === 'string') {
      passengerCount = parseFloat(flight.no_of_passenger.trim());
      if (isNaN(passengerCount)) {
        return {isValid: false, error: 'no_of_passenger must be a valid number'};
      }
    } else if (typeof flight.no_of_passenger === 'number') {
      passengerCount = flight.no_of_passenger;
    } else {
      return {isValid: false, error: 'no_of_passenger must be a number or string'};
    }

    if (passengerCount <= 0) {
      return {isValid: false, error: 'no_of_passenger must be greater than 0'};
    }

    // Validate class
    if (!flight.class || typeof flight.class !== 'string' || flight.class.trim() === '') {
      return {isValid: false, error: 'class is required and must be a non-empty string'};
    }

    // Validate from (3-digit IATA code)
    if (!flight.from || typeof flight.from !== 'string' || flight.from.trim().length !== 3) {
      return {isValid: false, error: 'from must be a 3-character IATA code'};
    }

    const fromCode = flight.from.trim().toLowerCase();
    if (!iataMap.has(fromCode)) {
      return {isValid: false, error: `from airport code '${flight.from}' not found in IATA database`};
    }

    // Validate to (3-digit IATA code)
    if (!flight.to || typeof flight.to !== 'string' || flight.to.trim().length !== 3) {
      return {isValid: false, error: 'to must be a 3-character IATA code'};
    }

    const toCode = flight.to.trim().toLowerCase();
    if (!iataMap.has(toCode)) {
      return {isValid: false, error: `to airport code '${flight.to}' not found in IATA database`};
    }

    // Check if from and to are the same
    if (fromCode === toCode) {
      return {isValid: false, error: 'from and to airports cannot be the same'};
    }

    // Validate date
    if (flight.date === undefined || flight.date === null) {
      return {isValid: false, error: 'date is required'};
    }

    let dateValue: string;
    if (typeof flight.date === 'number') {
      dateValue = flight.date.toString();
    } else if (typeof flight.date === 'string') {
      dateValue = flight.date.trim();
    } else {
      return {isValid: false, error: 'date must be a number or string'};
    }

    if (dateValue === '') {
      return {isValid: false, error: 'date cannot be empty'};
    }

    return {
      isValid: true,
      processedFlight: {
        no_of_passenger: passengerCount,
        class: flight.class.trim(),
        from: flight.from.trim().toUpperCase(),
        to: flight.to.trim().toUpperCase(),
        date: dateValue
      }
    };
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   * Returns distance in kilometers
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance;
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}
