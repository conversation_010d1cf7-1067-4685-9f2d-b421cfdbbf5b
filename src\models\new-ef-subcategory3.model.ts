import {Entity, model, property, hasMany} from '@loopback/repository';
import {NewEfSubcategory4} from './new-ef-subcategory4.model';

@model()
export class NewEfSubcategory3 extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  extra?: string;

  @property({
    type: 'number',
  })
  newEfSubcategory2Id?: number;

  @hasMany(() => NewEfSubcategory4)
  newEfSubcategory4s: NewEfSubcategory4[];

  constructor(data?: Partial<NewEfSubcategory3>) {
    super(data);
  }
}

export interface NewEfSubcategory3Relations {
  // describe navigational properties here
}

export type NewEfSubcategory3WithRelations = NewEfSubcategory3 & NewEfSubcategory3Relations;
