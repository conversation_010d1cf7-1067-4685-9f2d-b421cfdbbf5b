import {Entity, model, property, hasMany} from '@loopback/repository';
import {ApplicationRoles} from './application-roles.model';

@model()
export class ApplicationList extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @hasMany(() => ApplicationRoles)
  applicationRoles: ApplicationRoles[];

  constructor(data?: Partial<ApplicationList>) {
    super(data);
  }
}

export interface ApplicationListRelations {
  // describe navigational properties here
}

export type ApplicationListWithRelations = ApplicationList & ApplicationListRelations;
