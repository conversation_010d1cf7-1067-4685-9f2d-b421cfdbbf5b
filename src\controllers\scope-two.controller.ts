import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ScopeTwo} from '../models';
import {ScopeTwoRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ScopeTwoController {
  constructor(
    @repository(ScopeTwoRepository)
    public scopeTwoRepository : ScopeTwoRepository,
  ) {}

  @post('/scope-twos')
  @response(200, {
    description: 'ScopeTwo model instance',
    content: {'application/json': {schema: getModelSchemaRef(ScopeTwo)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeTwo, {
            title: 'NewScopeTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    scopeTwo: Omit<ScopeTwo, 'id'>,
  ): Promise<ScopeTwo> {
    return this.scopeTwoRepository.create(scopeTwo);
  }

  @get('/scope-twos/count')
  @response(200, {
    description: 'ScopeTwo model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ScopeTwo) where?: Where<ScopeTwo>,
  ): Promise<Count> {
    return this.scopeTwoRepository.count(where);
  }

  @get('/scope-twos')
  @response(200, {
    description: 'Array of ScopeTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ScopeTwo, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ScopeTwo) filter?: Filter<ScopeTwo>,
  ): Promise<ScopeTwo[]> {
    return this.scopeTwoRepository.find(filter);
  }

  // @patch('/scope-twos')
  // @response(200, {
  //   description: 'ScopeTwo PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ScopeTwo, {partial: true}),
  //       },
  //     },
  //   })
  //   scopeTwo: ScopeTwo,
  //   @param.where(ScopeTwo) where?: Where<ScopeTwo>,
  // ): Promise<Count> {
  //   return this.scopeTwoRepository.updateAll(scopeTwo, where);
  // }

  @get('/scope-twos/{id}')
  @response(200, {
    description: 'ScopeTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ScopeTwo, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ScopeTwo, {exclude: 'where'}) filter?: FilterExcludingWhere<ScopeTwo>
  ): Promise<ScopeTwo> {
    return this.scopeTwoRepository.findById(id, filter);
  }

  @patch('/scope-twos/{id}')
  @response(204, {
    description: 'ScopeTwo PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeTwo, {partial: true}),
        },
      },
    })
    scopeTwo: ScopeTwo,
  ): Promise<void> {
    await this.scopeTwoRepository.updateById(id, scopeTwo);
  }

  @put('/scope-twos/{id}')
  @response(204, {
    description: 'ScopeTwo PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() scopeTwo: ScopeTwo,
  ): Promise<void> {
    await this.scopeTwoRepository.replaceById(id, scopeTwo);
  }

  @del('/scope-twos/{id}')
  @response(204, {
    description: 'ScopeTwo DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.scopeTwoRepository.deleteById(id);
  }
}
