import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DealerChecklistSubmission, DealerChecklistSubmissionRelations, UserProfile, VendorCode} from '../models';
import {UserProfileRepository} from './user-profile.repository';
import {VendorCodeRepository} from './vendor-code.repository';

export class DealerChecklistSubmissionRepository extends DefaultCrudRepository<
  DealerChecklistSubmission,
  typeof DealerChecklistSubmission.prototype.id,
  DealerChecklistSubmissionRelations
> {

  public readonly dealer: BelongsToAccessor<UserProfile, typeof DealerChecklistSubmission.prototype.id>;

  public readonly vendor: BelongsToAccessor<VendorCode, typeof DealerChecklistSubmission.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('UserProfileRepository') protected userProfileRepositoryGetter: Getter<UserProfileRepository>, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>,
  ) {
    super(DealerChecklistSubmission, dataSource);
    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
    this.dealer = this.createBelongsToAccessorFor('dealer', userProfileRepositoryGetter,);
    this.registerInclusionResolver('dealer', this.dealer.inclusionResolver);

  }
}
