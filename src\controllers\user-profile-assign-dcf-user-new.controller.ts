import {
  Filter,
  repository
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody
} from '@loopback/rest';
import {
  AssignDcfUserNew,
  UserProfile,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileAssignDcfUserNewController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-dcf-user-news', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDcfUserNew',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDcfUserNew)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDcfUserNew>,
  ): Promise<AssignDcfUserNew[]> {
    return this.userProfileRepository.assignDcfUserNews(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-dcf-user-news', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDcfUserNew)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfUserNew, {
            title: 'NewAssignDcfUserNewInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDcfUserNew: Omit<AssignDcfUserNew, 'id'>,
  ): Promise<AssignDcfUserNew> {
    return this.userProfileRepository.assignDcfUserNews(id).create(assignDcfUserNew);
  }

  // @patch('/user-profiles/{id}/assign-dcf-user-news', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDcfUserNew PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfUserNew, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfUserNew: Partial<AssignDcfUserNew>,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfUserNew)) where?: Where<AssignDcfUserNew>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDcfUserNews(id).patch(assignDcfUserNew, where);
  // }

  // @del('/user-profiles/{id}/assign-dcf-user-news', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDcfUserNew DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfUserNew)) where?: Where<AssignDcfUserNew>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDcfUserNews(id).delete(where);
  // }
}
