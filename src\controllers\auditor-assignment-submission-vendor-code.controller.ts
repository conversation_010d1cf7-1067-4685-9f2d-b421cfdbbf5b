import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AuditorAssignmentSubmission,
  VendorCode,
} from '../models';
import {AuditorAssignmentSubmissionRepository} from '../repositories';

export class AuditorAssignmentSubmissionVendorCodeController {
  constructor(
    @repository(AuditorAssignmentSubmissionRepository)
    public auditorAssignmentSubmissionRepository: AuditorAssignmentSubmissionRepository,
  ) { }

  @get('/auditor-assignment-submissions/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to AuditorAssignmentSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof AuditorAssignmentSubmission.prototype.id,
  ): Promise<VendorCode> {
    return this.auditorAssignmentSubmissionRepository.vendor(id);
  }
}
