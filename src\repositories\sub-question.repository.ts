import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SubQuestion, SubQuestionRelations} from '../models';

export class SubQuestionRepository extends DefaultCrudRepository<
  SubQuestion,
  typeof SubQuestion.prototype.id,
  SubQuestionRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SubQuestion, dataSource);
  }
}
