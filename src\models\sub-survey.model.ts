import { Entity, model, property, hasOne, hasMany, belongsTo} from '@loopback/repository';
import {SaveSurvey} from './save-survey.model';
import {Response} from './response.model';
import {Survey} from './survey.model';

@model()
export class SubSurvey extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  introduction?: string;


  @property({
    type: 'array',
    itemType: 'object'
  })
  category_selected?: object[];

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'number',
  })
  surveyTitleId?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @hasOne(() => SaveSurvey)
  saveSurvey: SaveSurvey;

  @hasMany(() => Response)
  responses: Response[];

  @belongsTo(() => Survey)
  surveyId: number;

  constructor(data?: Partial<SubSurvey>) {
    super(data);
  }
}

export interface SubSurveyRelations {
  // describe navigational properties here
}

export type SubSurveyWithRelations = SubSurvey & SubSurveyRelations;
