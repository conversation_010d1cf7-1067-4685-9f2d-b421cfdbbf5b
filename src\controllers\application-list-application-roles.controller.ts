import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  ApplicationList,
  ApplicationRoles,
} from '../models';
import {ApplicationListRepository} from '../repositories';

export class ApplicationListApplicationRolesController {
  constructor(
    @repository(ApplicationListRepository) protected applicationListRepository: ApplicationListRepository,
  ) { }

  @get('/application-lists/{id}/application-roles', {
    responses: {
      '200': {
        description: 'Array of ApplicationList has many ApplicationRoles',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ApplicationRoles)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ApplicationRoles>,
  ): Promise<ApplicationRoles[]> {
    return this.applicationListRepository.applicationRoles(id).find(filter);
  }

  @post('/application-lists/{id}/application-roles', {
    responses: {
      '200': {
        description: 'ApplicationList model instance',
        content: {'application/json': {schema: getModelSchemaRef(ApplicationRoles)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof ApplicationList.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ApplicationRoles, {
            title: 'NewApplicationRolesInApplicationList',
            exclude: ['id'],
            optional: ['applicationListId']
          }),
        },
      },
    }) applicationRoles: Omit<ApplicationRoles, 'id'>,
  ): Promise<ApplicationRoles> {
    return this.applicationListRepository.applicationRoles(id).create(applicationRoles);
  }

  @patch('/application-lists/{id}/application-roles', {
    responses: {
      '200': {
        description: 'ApplicationList.ApplicationRoles PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ApplicationRoles, {partial: true}),
        },
      },
    })
    applicationRoles: Partial<ApplicationRoles>,
    @param.query.object('where', getWhereSchemaFor(ApplicationRoles)) where?: Where<ApplicationRoles>,
  ): Promise<Count> {
    return this.applicationListRepository.applicationRoles(id).patch(applicationRoles, where);
  }

  @del('/application-lists/{id}/application-roles', {
    responses: {
      '200': {
        description: 'ApplicationList.ApplicationRoles DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(ApplicationRoles)) where?: Where<ApplicationRoles>,
  ): Promise<Count> {
    return this.applicationListRepository.applicationRoles(id).delete(where);
  }
}
