import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  response
} from '@loopback/rest';
import {
  AssignSrfEntityUser,
  UserProfile,
} from '../models';
import {AssignSrfEntityRepository, AssignSrfEntityUserRepository, UserProfileRepository} from '../repositories';

export class UserProfileAssignSrfEntityUserController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(AssignSrfEntityUserRepository) protected assignSrfEntityUserRepository: AssignSrfEntityUserRepository,
    @repository(AssignSrfEntityRepository) protected assignSrfEntityRepository: AssignSrfEntityRepository,

  ) { }

  @get('/user-profiles/{id}/assign-srf-entity-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignSrfEntityUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignSrfEntityUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignSrfEntityUser>,
  ): Promise<AssignSrfEntityUser[]> {
    return this.userProfileRepository.assignSrfEntityUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-srf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignSrfEntityUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {
            title: 'NewAssignSrfEntityUserInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignSrfEntityUser: Omit<AssignSrfEntityUser, 'id'>,
  ): Promise<AssignSrfEntityUser> {
    return this.userProfileRepository.assignSrfEntityUsers(id).create(assignSrfEntityUser);
  }

  @patch('/user-profiles/{id}/assign-srf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignSrfEntityUser PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {partial: true}),
        },
      },
    })
    assignSrfEntityUser: Partial<AssignSrfEntityUser>,
    @param.query.object('where', getWhereSchemaFor(AssignSrfEntityUser)) where?: Where<AssignSrfEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignSrfEntityUsers(id).patch(assignSrfEntityUser, where);
  }

  @del('/user-profiles/{id}/assign-srf-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignSrfEntityUser DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignSrfEntityUser)) where?: Where<AssignSrfEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignSrfEntityUsers(id).delete(where);
  }
  @post('/user-profiles/{id}/assign-srf-entity-users-custom')
  @response(204, {
    description: 'AssignDcfEntityUser PATCH success',
  })
  async updateByIdCustom(
    @param.path.number('id') uid: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {partial: true}),
        },
      },
    })
    assignDcfEntityUser: AssignSrfEntityUser,
  ): Promise<any> {
    try {
      const {entityAssId, id, srfId, type, dealerType} = assignDcfEntityUser
      if (entityAssId && id) {
        await this.assignSrfEntityUserRepository.updateById(id, assignDcfEntityUser)
        return {result: 1, message: 'Updated Successfully', data: {}}
      } else if (!entityAssId && !id && srfId && type) {
        let found = await this.assignSrfEntityRepository.findOne({where: {userProfileId: uid, srfId: srfId, type}})
        if (!found) {
          const createdData = await this.assignSrfEntityRepository.create({userProfileId: uid, tier0_ids: [0], srfId: srfId, tier1_ids: [], tier2_ids: [], tier3_ids: [], type, created_by: assignDcfEntityUser.created_by, created_on: assignDcfEntityUser.created_on})
          const data = await this.userProfileRepository.assignSrfEntityUsers(uid).create({...assignDcfEntityUser, entityAssId: createdData.id})
          return {result: 2, message: 'Created Successfully', data, entityData: createdData}
        } else {
          return {result: 0, message: 'Something went wrong while creating/updating assignment', data: {}}


        }
      } else if (entityAssId && !id) {

        const anotherData = await this.userProfileRepository.assignSrfEntityUsers(uid).create({...assignDcfEntityUser, entityAssId: entityAssId})
        return {result: 3, message: 'Created Successfully', data: anotherData}
        // let assignedData = await this.userProfileRepository.assignSrfEntityUsers(uid).find({where: {srfId: srfId, type, entityAssId, dealerType}})
        // if ((assignedData.length && assignedData.every(item => item.end_date) || !assignedData.length)) {
        //   const anotherData = await this.userProfileRepository.assignSrfEntityUsers(uid).create({...assignDcfEntityUser, entityAssId: entityAssId})
        //   return {result: 3, message: 'Created Successfully', data: anotherData}
        // } else {
        //   console.log(assignedData)
        //   return {result: 0, message: 'Something went wrong while  assignment', data: {}}

        // }


      } else {
        return {result: 0, message: 'Something went wrong while creating/updating assignment', data: {}}

      }
    } catch (e) {
      return {result: 0, message: 'Something went wrong', data: {}}

    }

  }

  @post('/user-profiles/{id}/assign-srf-entity-users-bulk-custom')
  @response(204, {
    description: 'AssignDcfEntityUser PATCH success',
  })
  async bulkSRFAddition(
    @param.path.number('id') uid: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {partial: true}),
        },
      },
    })
    assignDcfEntityUser: AssignSrfEntityUser,
  ): Promise<any> {
    try {
      const {entityAssId, id, srfId, type, dealerType, reporter_ids} = assignDcfEntityUser
      if (entityAssId && id) {
        await this.assignSrfEntityUserRepository.updateById(id, assignDcfEntityUser)
        return {result: 1, message: 'Updated Successfully', data: []}
      } else if (!entityAssId && !id && srfId && type && reporter_ids?.length) {
        let found = await this.assignSrfEntityRepository.findOne({where: {userProfileId: uid, srfId: srfId, type}})
        if (!found) {
          const createdData = await this.assignSrfEntityRepository.create({userProfileId: uid, tier0_ids: [0], srfId: srfId, tier1_ids: [], tier2_ids: [], tier3_ids: [], type, created_by: assignDcfEntityUser.created_by, created_on: assignDcfEntityUser.created_on})

          const promises = reporter_ids.map((item) =>
            this.userProfileRepository.assignSrfEntityUsers(uid).create({
              ...assignDcfEntityUser,
              reporter_ids: [item],
              entityAssId: createdData.id
            })
          );


          const resultArray = await Promise.all(promises);
          return {result: 2, message: 'Created Successfully', data: resultArray, entityData: createdData}
        } else {
          return {result: 0, message: 'Something went wrong while creating/updating assignment', data: []}


        }
      } else if (entityAssId && !id && reporter_ids?.length) {

        const promises = reporter_ids.map((item) =>
          this.userProfileRepository.assignSrfEntityUsers(uid).create({
            ...assignDcfEntityUser,
            reporter_ids: [item],
            entityAssId: entityAssId
          })
        );


        const resultArray = await Promise.all(promises);

        return {result: 3, message: 'Created Successfully', data: resultArray}



      } else {
        return {result: 0, message: 'Something went wrong while creating/updating assignment', data: {}}

      }
    } catch (e) {
      return {result: 0, message: 'Something went wrong', data: {}}

    }

  }
}
