import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewEf} from '../models';
import {NewEfRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfController {
  constructor(
    @repository(NewEfRepository)
    public newEfRepository : NewEfRepository,
  ) {}

  @post('/new-efs')
  @response(200, {
    description: 'NewEf model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEf)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEf, {
            title: 'NewNewEf',
            exclude: ['id'],
          }),
        },
      },
    })
    newEf: Omit<NewEf, 'id'>,
  ): Promise<NewEf> {
    return this.newEfRepository.create(newEf);
  }

  @get('/new-efs/count')
  @response(200, {
    description: 'NewEf model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEf) where?: Where<NewEf>,
  ): Promise<Count> {
    return this.newEfRepository.count(where);
  }

  @get('/new-efs')
  @response(200, {
    description: 'Array of NewEf model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEf, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEf) filter?: Filter<NewEf>,
  ): Promise<NewEf[]> {
    return this.newEfRepository.find(filter);
  }

  // @patch('/new-efs')
  // @response(200, {
  //   description: 'NewEf PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEf, {partial: true}),
  //       },
  //     },
  //   })
  //   newEf: NewEf,
  //   @param.where(NewEf) where?: Where<NewEf>,
  // ): Promise<Count> {
  //   return this.newEfRepository.updateAll(newEf, where);
  // }

  @get('/new-efs/{id}')
  @response(200, {
    description: 'NewEf model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEf, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEf, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEf>
  ): Promise<NewEf> {
    return this.newEfRepository.findById(id, filter);
  }

  @patch('/new-efs/{id}')
  @response(204, {
    description: 'NewEf PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEf, {partial: true}),
        },
      },
    })
    newEf: NewEf,
  ): Promise<void> {
    await this.newEfRepository.updateById(id, newEf);
  }

  @put('/new-efs/{id}')
  @response(204, {
    description: 'NewEf PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEf: NewEf,
  ): Promise<void> {
    await this.newEfRepository.replaceById(id, newEf);
  }

  @del('/new-efs/{id}')
  @response(204, {
    description: 'NewEf DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfRepository.deleteById(id);
  }
}
