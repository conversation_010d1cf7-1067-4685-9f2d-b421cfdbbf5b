import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  ScopeName,
  TopicName,
} from '../models';
import {ScopeNameRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ScopeNameTopicNameController {
  constructor(
    @repository(ScopeNameRepository) protected scopeNameRepository: ScopeNameRepository,
  ) { }

  @get('/scope-names/{id}/topic-names', {
    responses: {
      '200': {
        description: 'Array of ScopeName has many TopicName',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(TopicName)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<TopicName>,
  ): Promise<TopicName[]> {
    return this.scopeNameRepository.topicNames(id).find(filter);
  }

  @post('/scope-names/{id}/topic-names', {
    responses: {
      '200': {
        description: 'ScopeName model instance',
        content: {'application/json': {schema: getModelSchemaRef(TopicName)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof ScopeName.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TopicName, {
            title: 'NewTopicNameInScopeName',
            exclude: ['id'],
            optional: ['scopeNameId']
          }),
        },
      },
    }) topicName: Omit<TopicName, 'id'>,
  ): Promise<TopicName> {
    return this.scopeNameRepository.topicNames(id).create(topicName);
  }

  // @patch('/scope-names/{id}/topic-names', {
  //   responses: {
  //     '200': {
  //       description: 'ScopeName.TopicName PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(TopicName, {partial: true}),
  //       },
  //     },
  //   })
  //   topicName: Partial<TopicName>,
  //   @param.query.object('where', getWhereSchemaFor(TopicName)) where?: Where<TopicName>,
  // ): Promise<Count> {
  //   return this.scopeNameRepository.topicNames(id).patch(topicName, where);
  // }

  // @del('/scope-names/{id}/topic-names', {
  //   responses: {
  //     '200': {
  //       description: 'ScopeName.TopicName DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(TopicName)) where?: Where<TopicName>,
  // ): Promise<Count> {
  //   return this.scopeNameRepository.topicNames(id).delete(where);
  // }
}
