import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {SupplierAction} from '../models';
import {
  SupplierActionRepository,
  SupplierAssessmentAssignmentRepository,
  UserProfileRepository,
  UserRoleAuthorizationRepository,
  VendorCodeRepository
} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class SupplierActionController {
  constructor(
    @repository(SupplierActionRepository)
    public supplierActionRepository: SupplierActionRepository,
    @repository(VendorCodeRepository)
    public vendorCodeRepository: VendorCodeRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,

  ) { }

  @post('/supplier-actions')
  @response(200, {
    description: 'SupplierAction model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierAction)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAction, {
            title: 'NewSupplierAction',
            exclude: ['id'],
          }),
        },
      },
    })
    supplierAction: Omit<SupplierAction, 'id'>,
  ): Promise<SupplierAction> {
    return this.supplierActionRepository.create(supplierAction);
  }

  @get('/supplier-actions/count')
  @response(200, {
    description: 'SupplierAction model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplierAction) where?: Where<SupplierAction>,
  ): Promise<Count> {
    return this.supplierActionRepository.count(where);
  }

  @get('/supplier-actions')
  @response(200, {
    description: 'Array of SupplierAction model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplierAction, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplierAction) filter?: Filter<SupplierAction>,
  ): Promise<SupplierAction[]> {
    return this.supplierActionRepository.find(filter);
  }

  @patch('/supplier-actions')
  @response(200, {
    description: 'SupplierAction PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAction, {partial: true}),
        },
      },
    })
    supplierAction: SupplierAction,
    @param.where(SupplierAction) where?: Where<SupplierAction>,
  ): Promise<Count> {
    return this.supplierActionRepository.updateAll(supplierAction, where);
  }

  @get('/supplier-actions/{id}')
  @response(200, {
    description: 'SupplierAction model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplierAction, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SupplierAction, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplierAction>
  ): Promise<SupplierAction> {
    return this.supplierActionRepository.findById(id, filter);
  }

  @patch('/supplier-actions/{id}')
  @response(204, {
    description: 'SupplierAction PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAction, {partial: true}),
        },
      },
    })
    supplierAction: SupplierAction,
  ): Promise<void> {
    await this.supplierActionRepository.updateById(id, supplierAction);

    if (supplierAction.approved_on && supplierAction.approved_by && supplierAction.type === 3 && supplierAction.reject === 0) {
      const action = await this.supplierActionRepository.findById(id);
      await this.handleSupplierActionStatusUpdate(action, 'approved');
    } else if (supplierAction.type === 1 && supplierAction.reject) {
      const action = await this.supplierActionRepository.findById(id);
      await this.handleSupplierActionStatusUpdate(action, 'rejected');
    }
  }

  @put('/supplier-actions/{id}')
  @response(204, {
    description: 'SupplierAction PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() supplierAction: SupplierAction,
  ): Promise<void> {
    await this.supplierActionRepository.replaceById(id, supplierAction);
  }

  @del('/supplier-actions/{id}')
  @response(204, {
    description: 'SupplierAction DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.supplierActionRepository.deleteById(id);
  }


  @post('/submit-supplier-action-plan')
  @response(200, {
    description: 'Bulk update SupplierActions',
    content: {'application/json': {schema: {type: 'object', properties: {count: {type: 'number'}}}}}
  })
  async actionPlanSubmission(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {type: 'number'},
                rootCause: {type: 'string'},
                proposedCorrectiveAction: {type: 'string'},
                actionTargetDate: {type: 'string'}
                // Additional properties are allowed by default
              },
              required: ['id'],
              additionalProperties: true
            }
          },
        },
      },
    })
    updates: SupplierAction[],
  ): Promise<{count: number}> {
    let updatedCount = 0;

    for (const update of updates) {
      if (!update.id) continue;

      const existing = await this.supplierActionRepository.findById(update.id);
      if (existing && (existing.type === 12 || existing.type === 102)) {
        const {rootCause, proposedCorrectiveAction, actionTargetDate} = update
        let newObj = {
          rootCause, proposedCorrectiveAction, actionTargetDate, type: 21, actionPlanRejectedBy: null, actionPlanRejectedOn: null
        }
        await this.supplierActionRepository.updateById(update.id, newObj);

        // Update the SupplierAssessmentAssignment with actionPlanType and submission date
        if (existing.supplierAssessmentAssignmentId && updatedCount === 0) {
          const currentDate = new Date().toISOString();
          await this.supplierAssessmentAssignmentRepository.updateById(
            existing.supplierAssessmentAssignmentId,
            {
              actionPlanType: 21,
              actionPlanSubmittedDate: currentDate
            }
          );
        }

        updatedCount++;
      }
    }
    if (updatedCount !== 0) {
      const existing = await this.supplierActionRepository.findById(updates[0].id);
      if (existing.supplierAssessmentAssignmentId) {
        const assignment = await this.supplierAssessmentAssignmentRepository.findById(existing.supplierAssessmentAssignmentId, {include: ['vendor', 'auditorAssignmentSubmission']});

        // Send email notification to users with role 12
        await this.sendApprovalRequestEmail(assignment);
      }
    }

    return {count: updatedCount};
  }

  @post('/draft-supplier-action-plan')
  @response(200, {
    description: 'Bulk update SupplierActions',
    content: {'application/json': {schema: {type: 'object', properties: {count: {type: 'number'}}}}}
  })
  async actionPlanDraft(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {type: 'number'},
                rootCause: {type: 'string'},
                proposedCorrectiveAction: {type: 'string'},
                actionTargetDate: {
                  oneOf: [
                    {type: 'string'},
                    {type: 'null'}
                  ]
                }
                // Additional properties are allowed by default
              },
              required: ['id'],
              additionalProperties: true
            }
          },
        },
      },
    })
    updates: SupplierAction[],
  ): Promise<{count: number}> {
    let updatedCount = 0;

    for (const update of updates) {
      if (!update.id) continue;

      const existing = await this.supplierActionRepository.findById(update.id);
      if (existing && (existing.type === 12 || existing.type === 102)) {
        const {rootCause, proposedCorrectiveAction, actionTargetDate} = update
        let newObj: any = {
          type: 102, actionPlanRejectedBy: null, actionPlanRejectedOn: null
        }
        if (actionTargetDate) {
          newObj['actionTargetDate'] = actionTargetDate
        }
        if (rootCause) {
          newObj['rootCause'] = rootCause
        }
        if (proposedCorrectiveAction) {
          newObj['proposedCorrectiveAction'] = proposedCorrectiveAction
        }
        await this.supplierActionRepository.updateById(update.id, newObj);

        // Update the SupplierAssessmentAssignment with actionPlanType and submission date
        if (existing.supplierAssessmentAssignmentId && updatedCount === 0) {
          const currentDate = new Date().toISOString();
          await this.supplierAssessmentAssignmentRepository.updateById(
            existing.supplierAssessmentAssignmentId,
            {
              actionPlanType: 102,
              actionPlanSubmittedDate: currentDate
            }
          );
        }

        updatedCount++;
      }
    }

    return {count: updatedCount};
  }

  @post('/reject-supplier-action-plan')
  @response(200, {
    description: 'Bulk update SupplierActions',
    content: {'application/json': {schema: {type: 'object', properties: {count: {type: 'number'}}}}}
  })
  async actionPlanRejection(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {type: 'number'},
                actionPlanRejectedBy: {type: 'number'},
                actionPlanRejectedOn: {type: 'string'},
                actionPlanApproverComments: {type: 'string'}
                // Additional properties are allowed
              },
              required: ['id'],
              additionalProperties: true
            }
          },
        },
      },
    })
    updates: SupplierAction[],
  ): Promise<{count: number}> {
    let updatedCount = 0;

    for (const update of updates) {
      if (!update.id) continue;

      const existing = await this.supplierActionRepository.findById(update.id);
      if (existing && existing.type === 21) {
        const {actionPlanRejectedBy, actionPlanRejectedOn, actionPlanApproverComments} = update
        let newObj = {
          actionPlanRejectedBy, actionPlanRejectedOn, actionPlanApproverComments, type: 12
        }
        await this.supplierActionRepository.updateById(update.id, newObj);

        // Update the SupplierAssessmentAssignment with actionPlanType
        if (existing.supplierAssessmentAssignmentId && updatedCount === 0) {
          await this.supplierAssessmentAssignmentRepository.updateById(
            existing.supplierAssessmentAssignmentId,
            {
              actionPlanType: 12,
              // Reset the submission date since it was rejected
              actionPlanSubmittedDate: null
            }
          );
        }

        updatedCount++;
      }
    }
    if (updatedCount !== 0) {
      const existing = await this.supplierActionRepository.findById(updates[0].id);
      if (existing.supplierAssessmentAssignmentId) {
        const assignment = await this.supplierAssessmentAssignmentRepository.findById(existing.supplierAssessmentAssignmentId, {include: ['vendor', 'auditorAssignmentSubmission']});

        // Send email notification to users with role 12
        await this.sendRejectionRequestEmail(assignment);
      }
    }
    return {count: updatedCount};
  }
  @post('/approve-supplier-action-plan')
  @response(200, {
    description: 'Bulk update SupplierActions',
    content: {'application/json': {schema: {type: 'object', properties: {count: {type: 'number'}}}}}
  })
  async actionPlanApproval(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {type: 'number'},
                actionPlanApprovedBy: {type: 'number'},
                actionPlanApprovedOn: {type: 'string'},
                actionPlanApproverComments: {type: 'string'}
                // Additional properties are allowed
              },
              required: ['id'],
              additionalProperties: true
            }
          },
        },
      },
    })
    updates: SupplierAction[],
  ): Promise<{count: number}> {
    let updatedCount = 0;
    const update = updates[0]; // assuming updates has only one item, or you only care about the first
    const supplierAction = await this.supplierActionRepository.findById(update.id);





    if (!supplierAction) return {count: 0};
    for (const update of updates) {
      if (!update.id) continue;

      const existing = await this.supplierActionRepository.findById(update.id);
      if (existing && existing.type === 21) {
        const {actionPlanApprovedBy, actionPlanApprovedOn, actionPlanApproverComments} = update
        let newObj = {
          actionPlanApprovedBy, actionPlanApprovedOn, actionPlanApproverComments, type: 1, actionPlanRejectedBy: null, actionPlanRejectedOn: null
        }
        await this.supplierActionRepository.updateById(update.id, newObj);

        // Update the SupplierAssessmentAssignment with actionPlanType and approval date
        if (existing.supplierAssessmentAssignmentId && updatedCount === 0) {
          const currentDate = new Date().toISOString();
          await this.supplierAssessmentAssignmentRepository.updateById(
            existing.supplierAssessmentAssignmentId,
            {
              actionPlanType: 1,
              actionPlanApprovedDate: currentDate
            }
          );
        }

        updatedCount++;
      }
    }
    const supplierAssessmentId = supplierAction.supplierAssessmentAssignmentId;

    const assessmentAssignment = await this.supplierAssessmentAssignmentRepository.findById(
      supplierAssessmentId,
      {
        include: [
          {
            relation: 'auditorAssignmentSubmission',
            scope: {
              fields: {
                type: true,
                approved_on: true,
                approverComments: true,
                rejected_on: true,
                auditorMSIScore: true,
                submitted_on: true,
                modified_on: true,
                id: true,
              },
            },
          },
          {relation: 'vendor'},
        ],
      }
    );
    const roleAgainsCategory = {
      1: 25,
      2: 26,
      3: 27,
      4: 28,
      5: 29,
      6: 30,
      7: 31,
      8: 32,
      9: 33,
      10: 34,
      11: 35
    }
    const vendorData = await this.vendorCodeRepository.findById(assessmentAssignment.vendorId);
    const role_id = roleAgainsCategory[vendorData?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
    const roles = await this.userRoleAuthorizationRepository.execute(

      `SELECT * FROM UserRoleAuthorization
            WHERE roles IS NOT NULL
              AND JSON_LENGTH(roles) > 0
              AND JSON_CONTAINS(roles, ?, '$')`,
      [JSON.stringify([role_id])]
    )
    const roleUsers = await this.userRoleAuthorizationRepository.execute(
      `SELECT * FROM UserRoleAuthorization
         WHERE roles IS NOT NULL
           AND JSON_LENGTH(roles) > 0
           AND JSON_CONTAINS(roles, ?, '$')`,
      [JSON.stringify([12])]
    );

    const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
    const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
    const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)
    const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
    const approveDate = DateTime.fromISO(assessmentAssignment?.auditorAssignmentSubmission.approved_on ?? '').toFormat('dd-MM-yyyy');
    const adminObj = await this.userProfileRepository.findById(289);
    const supplierOtherSpoc = this.userProfileController.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x)
    const supplierMailIds = [...supplierOtherSpoc, vendorSpoc?.[0]?.email].filter((x: any) => x)
    const userIds = roleUsers.map((user: any) => user.user_id).filter((id: any) => id);



    // Get user profiles and extract email addresses
    const users = await this.userProfileController.filteredUP({
      where: {id: {inq: userIds}}
    });

    const emailAddresses = users.map((user: any) => user.email).filter((email: any) => email);
    if (supplierAction.supplierAssessmentAssignmentId) {

      // Get all supplier actions for this assignment to create the table
      const allSupplierActions = await this.supplierActionRepository.find({
        where: {
          categoryOfFinding: {inq: [2, 3]},
          supplierAssessmentAssignmentId: supplierAction.supplierAssessmentAssignmentId
        },
        fields: {
          id: true,
          finding: true,
          description: true,
          rootCause: true,
          proposedCorrectiveAction: true,
          actionTargetDate: true,
          actionPlanApproverComments: true
        }
      });

      // Generate action plan table
      const actionPlanTable = this.generateActionPlanTable(allSupplierActions);

      const subject = ` Acknowledgement on the Approval of Submitted Action Plan - ${vendorData.supplierName} (${vendorData.code})`;
      const body = `<p>Dear ${vendorData.supplierName},</p>
 <p style="margin: 10px 0px;">Hope you are doing well.</p>

<p style="margin: 10px 0px;">
This is to acknowledge the receipt of your Corrective Action Plan (CAP) and Root Cause Analysis (RCA) submitted via the Navigos Sustainability Platform in response to the audit report shared on ${approveDate}.</p>

<p style="margin: 10px 0px;"> We have reviewed your submission and are pleased to inform you that the Action Plan has been approved. Please proceed with implementing the agreed actions and updating the status and evidence of closures on the platform as per the timelines indicated.</p>

<p style="margin: 10px 0px;"><strong>Action Plan Details:</strong></p>
${actionPlanTable}

 ${adminObj?.supplierPortalUrl ? `<p style="margin: 10px 0px;">
   Please log in to the  <a href=${adminObj?.supplierPortalUrl} target="_blank">EiSqr – ESG Platform</a> to complete and submit the required action
 </p>` : ''}

<p style="margin: 10px 0px;"> Should you require any further assistance, please feel free to reach out to us at <a href="mailto:<EMAIL>"><EMAIL></a>, copying <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

<p style="margin: 10px 0px;">We appreciate your continued commitment to sustainability and collaboration in this process.</p>

<p style="margin: 10px 0px;">
Warm regards,<br/>
TVS Motor Company Limited</p>

<p style="margin: 10px 0px;"><em>This is an automated message. Please do not reply to this email.</em></p>

    `;

      try {

        await this.sqsService.sendEmail([...supplierMailIds].filter(y => y), subject, body, [...headSpocMailId, ...emailAddresses])
          .then(info => console.log('Email sent:', info))
          .catch(err => console.error('Error sending email:', err));
      } catch (error) {
        console.error('Error sending email:', error);

      }
    }

    return {count: updatedCount};
  }

  /**
   * Send email notification to users with role 12 for supplier action plan approval request
   */
  private async sendApprovalRequestEmail(assignment: any): Promise<void> {
    try {
      // Get users with role 12 using execute query
      const roleUsers = await this.userRoleAuthorizationRepository.execute(
        `SELECT * FROM UserRoleAuthorization
         WHERE roles IS NOT NULL
           AND JSON_LENGTH(roles) > 0
           AND JSON_CONTAINS(roles, ?, '$')`,
        [JSON.stringify([12])]
      );

      if (roleUsers.length === 0) {
        console.log('No users found with role 12');
        return;
      }

      // Extract user IDs
      const userIds = roleUsers.map((user: any) => user.user_id).filter((id: any) => id);

      if (userIds.length === 0) {
        console.log('No valid user IDs found for role 12');
        return;
      }

      // Get user profiles and extract email addresses
      const users = await this.userProfileController.filteredUP({
        where: {id: {inq: userIds}}
      });

      const emailAddresses = users.map((user: any) => user.email).filter((email: any) => email);

      if (emailAddresses.length === 0) {
        console.log('No valid email addresses found for users with role 12');
        return;
      }

      // Get supplier information
      const supplierName = assignment.vendor?.supplierName || 'Unknown Supplier';

      // Get audit report date (assuming it's from the assignment)
      const reportDate = assignment.auditorAssignmentSubmission?.approved_on
        ? DateTime.fromISO(assignment.auditorAssignmentSubmission.approved_on, {zone: 'utc'}).setZone('Asia/Kolkata').toFormat('dd-MM-yyyy')
        : 'N/A';

      // Prepare email content
      const subject = `Request for Approval – Supplier Action Plan Submission – ${supplierName} (${assignment?.vendor?.code})`;

      const body = `
        <p style="margin: 5px 0px;">Dear Sectional Admin(s)</p>
        <p style="margin: 5px 0px;">Hope you are doing well.</p>

        <p style="margin: 5px 0px;">This is to inform you that ${supplierName} has submitted their Corrective Action Plan (CAP) and Root Cause Analysis (RCA) via the Navigos Sustainability Platform, in response to the audit report issued on ${reportDate}.</p>

        <p style="margin: 5px 0px;">We kindly request you to log in to <a href="https://tvsmotor.eisqr.com"> EiSqr Navigos </a> platform and review the submitted action plan. Please mark the action plan as Approved or Not Approved based on alignment with the audit observations.</p>

        <p style="margin: 5px 0px;">Your timely review is essential to ensure smooth progression of the action closure process. Should you need any assistance, please reach <NAME_EMAIL>.</p>

        <p style="margin: 5px 0px;">Warm Regards.</p>

      `;
      try {

        await this.sqsService.sendEmail([...emailAddresses].filter(y => y), subject, body, [])
          .then(info => console.log('Email sent:', info))
          .catch(err => console.error('Error sending email:', err));
      } catch (error) {
        console.error('Error sending email:', error);

      }



    } catch (error) {
      console.error('Error in sendApprovalRequestEmail:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Send rejection email notification to supplier with CC to sectional admin and category head
   */
  private async sendRejectionRequestEmail(assignment: any): Promise<void> {
    try {
      // Get vendor data
      const vendorData = await this.vendorCodeRepository.findById(assignment.vendorId);
      if (!vendorData) {
        console.log('No vendor data found for assignment');
        return;
      }

      // Get supplier contact emails (TO)
      const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
      const supplierOtherSpoc = this.userProfileController.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x);
      const supplierMailIds = [...supplierOtherSpoc, vendorSpoc?.[0]?.email].filter((x: any) => x);

      if (supplierMailIds.length === 0) {
        console.log('No valid supplier email addresses found');
        return;
      }

      // Get sectional admin emails (role 12) for CC
      const sectionalAdminUsers = await this.userRoleAuthorizationRepository.execute(
        `SELECT * FROM UserRoleAuthorization
         WHERE roles IS NOT NULL
           AND JSON_LENGTH(roles) > 0
           AND JSON_CONTAINS(roles, ?, '$')`,
        [JSON.stringify([12])]
      );

      const sectionalAdminUserIds = sectionalAdminUsers.map((user: any) => user.user_id).filter((id: any) => id);
      const sectionalAdminData = await this.userProfileController.filteredUP({where: {id: {inq: sectionalAdminUserIds}}});
      const sectionalAdminEmails = sectionalAdminData.map((x: any) => x.email).filter((x: any) => x);

      // Get category head emails based on supplier category for CC
      const roleAgainsCategory = {
        1: 25,
        2: 26,
        3: 27,
        4: 28,
        5: 29,
        6: 30,
        7: 31,
        8: 32,
        9: 33,
        10: 34,
        11: 35
      };

      const categoryRoleId = roleAgainsCategory[vendorData?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
      let categoryHeadEmails: string[] = [];

      if (categoryRoleId > 0) {
        const categoryHeadUsers = await this.userRoleAuthorizationRepository.execute(
          `SELECT * FROM UserRoleAuthorization
           WHERE roles IS NOT NULL
             AND JSON_LENGTH(roles) > 0
             AND JSON_CONTAINS(roles, ?, '$')`,
          [JSON.stringify([categoryRoleId])]
        );

        const categoryHeadUserIds = categoryHeadUsers.map((user: any) => user.user_id).filter((id: any) => id);
        const categoryHeadData = await this.userProfileController.filteredUP({where: {id: {inq: categoryHeadUserIds}}});
        categoryHeadEmails = categoryHeadData.map((x: any) => x.email).filter((x: any) => x);
      }

      // Combine CC emails (sectional admin + category head + fixed emails)
      const ccEmails = [
        ...sectionalAdminEmails,
        ...categoryHeadEmails,
        '<EMAIL>'
      ].filter((email, index, self) => email && self.indexOf(email) === index); // Remove duplicates

      // Get supplier information
      const supplierName = vendorData.supplierName || 'Unknown Supplier';

      // Get audit report date
      const reportDate = assignment.auditorAssignmentSubmission?.approved_on
        ? DateTime.fromISO(assignment.auditorAssignmentSubmission.approved_on).toFormat('dd-MM-yyyy')
        : 'N/A';

      // Prepare email content
      const subject = `Action Plan Review – Resubmission Required – ${supplierName} (${vendorData.code})`;

      const body = `
        <p style="margin: 5px 0px;">Dear ${supplierName},</p>
        <p style="margin: 5px 0px;">Hope you are doing well.</p>

        <p style="margin: 5px 0px;">This is to acknowledge the receipt of your Corrective Action Plan (CAP) and Root Cause Analysis (RCA) submitted via the Navigos Sustainability Platform in response to the audit report shared on ${reportDate}.</p>

        <p style="margin: 5px 0px;">Following our review, we regret to inform you that the submitted Action Plan has not been approved. Kindly revisit the observations and feedback provided by the TVS Motor review team and revise your CAP and RCA accordingly.</p>

        <p style="margin: 5px 0px;">We request you to resubmit the updated Action Plan along with relevant supporting evidence on  <a href="https://tvsmotor-supplier.eisqr.com">EiSqr Navigos</a> within the stipulated timeline.</p>

        <p style="margin: 5px 0px;">Should you require any clarification or support, please reach <NAME_EMAIL>, copying <EMAIL>. We appreciate your continued efforts toward sustainability and look forward to receiving the revised submission.</p>

        <p style="margin: 5px 0px;">Warm regards,<br/>TVS Motor Company Limited</p>

      `;
      console.log('CC Emails:', ccEmails, "TO:", supplierMailIds);
      // Send email to supplier with CC to sectional admin and category head

      try {

        await this.sqsService.sendEmail([...supplierMailIds].filter(y => y), subject, body, [...ccEmails])
          .then(info => console.log('Email sent:', info))
          .catch(err => console.error('Error sending email:', err));
      } catch (error) {
        console.error('Error sending email:', error);

      }
    } catch (error) {
      console.error('Error in sendRejectionRequestEmail:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Handle supplier action status update and send appropriate email notification
   */
  private async handleSupplierActionStatusUpdate(supplierAction: any, status: 'approved' | 'rejected'): Promise<void> {
    try {
      if (!supplierAction.supplierAssessmentAssignmentId) {
        console.log('No supplier assessment assignment ID found');
        return;
      }

      // Get assignment with vendor and audit details
      const assignment = await this.supplierAssessmentAssignmentRepository.findById(
        supplierAction.supplierAssessmentAssignmentId,
        {
          include: [
            'vendor',
            {
              relation: 'auditorAssignmentSubmission',
              scope: {
                fields: {
                  approved_on: true,
                  submitted_on: true,
                  id: true
                }
              }
            }
          ]
        }
      );

      if (!assignment) {
        console.log('No assignment found for supplier action');
        return;
      }

      // Get the specific supplier action with its history to get approver comments
      const actionWithHistory = await this.supplierActionRepository.findById(supplierAction.id, {
        include: [
          {
            relation: 'supplierActionHistories',
            scope: {
              order: ['id DESC'], // Get latest history first
              limit: 1,
              fields: {
                approverComments: true,
                id: true
              }
            }
          }
        ]
      });

      // Get approver comments from the last element of supplierActionHistories
      const approverComments = actionWithHistory?.supplierActionHistories?.[0]?.approverComments || '';

      // Send email notification for single action
      await this.sendSingleSupplierActionStatusEmail(assignment, actionWithHistory, status, approverComments);

    } catch (error) {
      console.error('Error in handleSupplierActionStatusUpdate:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Send email notification for single supplier action status update (approval/rejection)
   */
  private async sendSingleSupplierActionStatusEmail(
    assignment: any,
    supplierAction: any,
    status: 'approved' | 'rejected',
    approverComments: string
  ): Promise<void> {
    try {
      // Get vendor data
      const vendorData = await this.vendorCodeRepository.findById(assignment.vendorId);
      if (!vendorData) {
        console.log('No vendor data found for assignment');
        return;
      }

      // Get supplier contact emails (TO)
      const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
      const supplierOtherSpoc = this.userProfileController.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x);
      const supplierMailIds = [...supplierOtherSpoc, vendorSpoc?.[0]?.email].filter((x: any) => x);

      if (supplierMailIds.length === 0) {
        console.log('No valid supplier email addresses found');
        return;
      }

      // Get sectional admin emails (role 12) for CC
      const sectionalAdminUsers = await this.userRoleAuthorizationRepository.execute(
        `SELECT * FROM UserRoleAuthorization
         WHERE roles IS NOT NULL
           AND JSON_LENGTH(roles) > 0
           AND JSON_CONTAINS(roles, ?, '$')`,
        [JSON.stringify([12])]
      );

      const sectionalAdminUserIds = sectionalAdminUsers.map((user: any) => user.user_id).filter((id: any) => id);
      const sectionalAdminData = await this.userProfileController.filteredUP({where: {id: {inq: sectionalAdminUserIds}}});
      const sectionalAdminEmails = sectionalAdminData.map((x: any) => x.email).filter((x: any) => x);

      // Get category head emails based on supplier category for CC
      const roleAgainsCategory = {
        1: 25, 2: 26, 3: 27, 4: 28, 5: 29, 6: 30,
        7: 31, 8: 32, 9: 33, 10: 34, 11: 35
      };

      const categoryRoleId = roleAgainsCategory[vendorData?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
      let categoryHeadEmails: string[] = [];

      if (categoryRoleId > 0) {
        const categoryHeadUsers = await this.userRoleAuthorizationRepository.execute(
          `SELECT * FROM UserRoleAuthorization
           WHERE roles IS NOT NULL
             AND JSON_LENGTH(roles) > 0
             AND JSON_CONTAINS(roles, ?, '$')`,
          [JSON.stringify([categoryRoleId])]
        );

        const categoryHeadUserIds = categoryHeadUsers.map((user: any) => user.user_id).filter((id: any) => id);
        const categoryHeadData = await this.userProfileController.filteredUP({where: {id: {inq: categoryHeadUserIds}}});
        categoryHeadEmails = categoryHeadData.map((x: any) => x.email).filter((x: any) => x);
      }

      // Combine CC emails (sectional admin + category head + fixed emails)
      const ccEmails = [
        ...sectionalAdminEmails,
        ...categoryHeadEmails,
        '<EMAIL>'
      ].filter((email, index, self) => email && self.indexOf(email) === index); // Remove duplicates

      // Get supplier information
      const supplierName = vendorData.supplierName || 'Unknown Supplier';

      // Get audit report date
      const reportDate = assignment.auditorAssignmentSubmission?.approved_on
        ? DateTime.fromISO(assignment.auditorAssignmentSubmission.approved_on).toFormat('dd-MM-yyyy')
        : 'N/A';

      // Get action details
      const finding = supplierAction?.finding || supplierAction?.description || 'N/A';
      const proposedAction = supplierAction?.proposedCorrectiveAction || 'N/A';
      const rootCause = supplierAction?.rootCause || 'N/A';
      // Determine status text and message
      const statusText = status === 'approved' ? 'Approved' : 'Rejected';
      const statusMessage = status === 'approved'
        ? 'We are pleased to inform you that your submitted action has been approved. Please proceed with implementing the agreed actions and updating the status and evidence of closures on the platform as per the timelines indicated.'
        : 'Following our review, we regret to inform you that the submitted action has not been approved. Please revisit and update the action with the required corrections and re-upload the supporting evidence via the platform.';

      // Prepare email content
      const subject = `Status Update on Submitted Action – ${supplierName} (${vendorData.code})`;

      const body = `
        <p style="margin: 5px 0px;">Dear ${supplierName},</p>
        <p style="margin: 5px 0px;">Hope you are doing well.</p>

        <p style="margin: 5px 0px;">This is in reference to the Corrective Action Plan (CAP) and respective Action submitted via the Navigos Sustainability Platform, as part of the closure process for the audit conducted on ${reportDate}.</p>

        <p style="margin: 5px 0px;"><strong>Action Details:</strong></p>
        <p style="margin: 5px 0px;"><strong>Finding/Observation:</strong> ${finding}</p>
                <p style="margin: 5px 0px;"><strong>Root Cause:</strong> ${rootCause}</p>
        <p style="margin: 5px 0px;"><strong>Proposed Action:</strong> ${proposedAction}</p>
        <p style="margin: 5px 0px;"><strong>Status:</strong> <span style="color: ${status === 'approved' ? '#28a745' : '#dc3545'}; font-weight: bold;">${statusText}</span></p>

        ${approverComments ? `<p style="margin: 5px 0px;"><strong>Reviewer Comments:</strong> ${approverComments}</p>` : ''}

        <p style="margin: 5px 0px;">${statusMessage}</p>

        <p style="margin: 5px 0px;">We request you to complete the necessary updates within the stipulated timelines to ensure timely closure of the audit process.</p>

        <p style="margin: 5px 0px;">For any assistance, feel free to reach <NAME_EMAIL>, copying <EMAIL>. Thank you for your continued cooperation.</p>

        <p style="margin: 5px 0px;">Warm regards,<br/>TVS Motor Company Limited</p>

        <p style="margin: 5px 0px;"><em>This is an automated message. Please do not reply to this email.</em></p>
      `;
      console.log("to", supplierMailIds, "CC", ccEmails)

      try {

        await this.sqsService.sendEmail([...supplierMailIds].filter(y => y), subject, body, [...ccEmails])
          .then(info => console.log('Email sent:', info))
          .catch(err => console.error('Error sending email:', err));
      } catch (error) {
        console.error('Error sending email:', error);

      }
    } catch (error) {
      console.error('Error in sendSupplierActionStatusEmail:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Generate HTML table for action plan approval email showing Root Cause, Proposed Corrective Action, Target Date & Approver Comments
   */
  private generateActionPlanTable(supplierActions: any[]): string {
    if (!supplierActions || supplierActions.length === 0) {
      return '<p style="margin: 10px 0px;">No actions found.</p>';
    }

    let tableHtml = `
      <table style="border-collapse: collapse; width: 100%; margin: 10px 0px; font-family: Arial, sans-serif;">
        <thead>
          <tr style="background-color: #f8f9fa;">
            <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left; font-weight: bold; color: #495057;">S.No.</th>
            <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left; font-weight: bold; color: #495057;">Finding/Observation</th>
            <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left; font-weight: bold; color: #495057;">Root Cause</th>
            <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left; font-weight: bold; color: #495057;">Proposed Corrective Action</th>
            <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left; font-weight: bold; color: #495057;">Target Date</th>
            <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left; font-weight: bold; color: #495057;">Approver Comments</th>
          </tr>
        </thead>
        <tbody>
    `;

    supplierActions.forEach((action, index) => {
      const finding = action.finding || action.description || 'N/A';
      const rootCause = action.rootCause || 'N/A';
      const proposedAction = action.proposedCorrectiveAction || 'N/A';
      const targetDate = action.actionTargetDate
        ? DateTime.fromISO(action.actionTargetDate).toFormat('dd-MM-yyyy')
        : 'N/A';
      const approverComments = action.actionPlanApproverComments || 'N/A';

      // Alternate row colors for better readability
      const rowColor = index % 2 === 0 ? '#ffffff' : '#f8f9fa';

      tableHtml += `
        <tr style="background-color: ${rowColor};">
          <td style="border: 1px solid #dee2e6; padding: 10px; vertical-align: top;">${index + 1}</td>
          <td style="border: 1px solid #dee2e6; padding: 10px; vertical-align: top; max-width: 200px; word-wrap: break-word;">${finding}</td>
          <td style="border: 1px solid #dee2e6; padding: 10px; vertical-align: top; max-width: 150px; word-wrap: break-word;">${rootCause}</td>
          <td style="border: 1px solid #dee2e6; padding: 10px; vertical-align: top; max-width: 200px; word-wrap: break-word;">${proposedAction}</td>
          <td style="border: 1px solid #dee2e6; padding: 10px; vertical-align: top; text-align: center;">${targetDate}</td>
          <td style="border: 1px solid #dee2e6; padding: 10px; vertical-align: top; max-width: 150px; word-wrap: break-word;">${approverComments}</td>
        </tr>
      `;
    });

    tableHtml += `
        </tbody>
      </table>
    `;

    return tableHtml;
  }

}
