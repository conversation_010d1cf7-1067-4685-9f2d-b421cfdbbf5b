import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SupplierAssignmentSubmission, SupplierAssignmentSubmissionRelations, VendorCode} from '../models';
import {VendorCodeRepository} from './vendor-code.repository';

export class SupplierAssignmentSubmissionRepository extends DefaultCrudRepository<
  SupplierAssignmentSubmission,
  typeof SupplierAssignmentSubmission.prototype.id,
  SupplierAssignmentSubmissionRelations
> {

  public readonly vendor: BelongsToAccessor<VendorCode, typeof SupplierAssignmentSubmission.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>,
  ) {
    super(SupplierAssignmentSubmission, dataSource);
    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
  }
}
