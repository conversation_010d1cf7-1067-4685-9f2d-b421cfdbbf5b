import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  QSection,
  ConsolidateFormCollection,
} from '../models';
import {QSectionRepository} from '../repositories';

export class QSectionConsolidateFormCollectionController {
  constructor(
    @repository(QSectionRepository)
    public qSectionRepository: QSectionRepository,
  ) { }

  @get('/q-sections/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to QSection',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ConsolidateFormCollection),
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.number('id') id: typeof QSection.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.qSectionRepository.srf(id);
  }
}
