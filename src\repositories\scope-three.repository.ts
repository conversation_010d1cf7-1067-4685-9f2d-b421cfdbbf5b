import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ScopeThree, ScopeThreeRelations} from '../models';

export class ScopeThreeRepository extends DefaultCrudRepository<
  ScopeThree,
  typeof ScopeThree.prototype.id,
  ScopeThreeRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(ScopeThree, dataSource);
  }
}
