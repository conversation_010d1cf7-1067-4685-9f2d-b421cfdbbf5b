import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {UserRoleAuthorization, UserRoleAuthorizationRelations} from '../models';

export class UserRoleAuthorizationRepository extends DefaultCrudRepository<
  UserRoleAuthorization,
  typeof UserRoleAuthorization.prototype.id,
  UserRoleAuthorizationRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(UserRoleAuthorization, dataSource);
  }
}
