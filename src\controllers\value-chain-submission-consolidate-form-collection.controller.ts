import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ValueChainSubmission,
  ConsolidateFormCollection,
} from '../models';
import {ValueChainSubmissionRepository} from '../repositories';

export class ValueChainSubmissionConsolidateFormCollectionController {
  constructor(
    @repository(ValueChainSubmissionRepository)
    public valueChainSubmissionRepository: ValueChainSubmissionRepository,
  ) { }

  @get('/value-chain-submissions/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to ValueChainSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ConsolidateFormCollection),
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.number('id') id: typeof ValueChainSubmission.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.valueChainSubmissionRepository.srf(id);
  }
}
