import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AssessmentSubSection2,
  AssessmentSubSection3,
} from '../models';
import {AssessmentSubSection2Repository} from '../repositories';

export class AssessmentSubSection2AssessmentSubSection3Controller {
  constructor(
    @repository(AssessmentSubSection2Repository) protected assessmentSubSection2Repository: AssessmentSubSection2Repository,
  ) { }

  @get('/assessment-sub-section2s/{id}/assessment-sub-section3s', {
    responses: {
      '200': {
        description: 'Array of AssessmentSubSection2 has many AssessmentSubSection3',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssessmentSubSection3)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<AssessmentSubSection3>,
  ): Promise<AssessmentSubSection3[]> {
    return this.assessmentSubSection2Repository.assessmentSubSection3s(id).find(filter);
  }

  @post('/assessment-sub-section2s/{id}/assessment-sub-section3s', {
    responses: {
      '200': {
        description: 'AssessmentSubSection2 model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssessmentSubSection3)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof AssessmentSubSection2.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection3, {
            title: 'NewAssessmentSubSection3InAssessmentSubSection2',
            exclude: ['id'],
            optional: ['assessmentSubSection2Id']
          }),
        },
      },
    }) assessmentSubSection3: Omit<AssessmentSubSection3, 'id'>,
  ): Promise<AssessmentSubSection3> {
    return this.assessmentSubSection2Repository.assessmentSubSection3s(id).create(assessmentSubSection3);
  }

  @patch('/assessment-sub-section2s/{id}/assessment-sub-section3s', {
    responses: {
      '200': {
        description: 'AssessmentSubSection2.AssessmentSubSection3 PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection3, {partial: true}),
        },
      },
    })
    assessmentSubSection3: Partial<AssessmentSubSection3>,
    @param.query.object('where', getWhereSchemaFor(AssessmentSubSection3)) where?: Where<AssessmentSubSection3>,
  ): Promise<Count> {
    return this.assessmentSubSection2Repository.assessmentSubSection3s(id).patch(assessmentSubSection3, where);
  }

  @del('/assessment-sub-section2s/{id}/assessment-sub-section3s', {
    responses: {
      '200': {
        description: 'AssessmentSubSection2.AssessmentSubSection3 DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(AssessmentSubSection3)) where?: Where<AssessmentSubSection3>,
  ): Promise<Count> {
    return this.assessmentSubSection2Repository.assessmentSubSection3s(id).delete(where);
  }
}
