import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, HasManyRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SubSurvey, SubSurveyRelations, SaveSurvey, Response, Survey} from '../models';
import {SaveSurveyRepository} from './save-survey.repository';
import {ResponseRepository} from './response.repository';
import {SurveyRepository} from './survey.repository';

export class SubSurveyRepository extends DefaultCrudRepository<
  SubSurvey,
  typeof SubSurvey.prototype.id,
  SubSurveyRelations
> {

  public readonly saveSurvey: HasOneRepositoryFactory<SaveSurvey, typeof SubSurvey.prototype.id>;

  public readonly responses: HasManyRepositoryFactory<Response, typeof SubSurvey.prototype.id>;

  public readonly survey: BelongsToAccessor<Survey, typeof SubSurvey.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('SaveSurveyRepository') protected saveSurveyRepositoryGetter: Getter<SaveSurveyRepository>, @repository.getter('ResponseRepository') protected responseRepositoryGetter: Getter<ResponseRepository>, @repository.getter('SurveyRepository') protected surveyRepositoryGetter: Getter<SurveyRepository>,
  ) {
    super(SubSurvey, dataSource);
    this.survey = this.createBelongsToAccessorFor('survey', surveyRepositoryGetter,);
    this.registerInclusionResolver('survey', this.survey.inclusionResolver);
    this.responses = this.createHasManyRepositoryFactoryFor('responses', responseRepositoryGetter,);
    this.registerInclusionResolver('responses', this.responses.inclusionResolver);
    this.saveSurvey = this.createHasOneRepositoryFactoryFor('saveSurvey', saveSurveyRepositoryGetter);
    this.registerInclusionResolver('saveSurvey', this.saveSurvey.inclusionResolver);
  }
}
