import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewCategory,
  NewTopic,
} from '../models';
import {NewCategoryRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewCategoryNewTopicController {
  constructor(
    @repository(NewCategoryRepository) protected newCategoryRepository: NewCategoryRepository,
  ) { }

  @get('/new-categories/{id}/new-topics', {
    responses: {
      '200': {
        description: 'Array of NewCategory has many NewTopic',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewTopic)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewTopic>,
  ): Promise<NewTopic[]> {
    return this.newCategoryRepository.newTopics(id).find(filter);
  }

  @post('/new-categories/{id}/new-topics', {
    responses: {
      '200': {
        description: 'NewCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewTopic)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTopic, {
            title: 'NewNewTopicInNewCategory',
            exclude: ['id'],
            optional: ['newCategoryId']
          }),
        },
      },
    }) newTopic: Omit<NewTopic, 'id'>,
  ): Promise<NewTopic> {
    return this.newCategoryRepository.newTopics(id).create(newTopic);
  }

  // @patch('/new-categories/{id}/new-topics', {
  //   responses: {
  //     '200': {
  //       description: 'NewCategory.NewTopic PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewTopic, {partial: true}),
  //       },
  //     },
  //   })
  //   newTopic: Partial<NewTopic>,
  //   @param.query.object('where', getWhereSchemaFor(NewTopic)) where?: Where<NewTopic>,
  // ): Promise<Count> {
  //   return this.newCategoryRepository.newTopics(id).patch(newTopic, where);
  // }

  // @del('/new-categories/{id}/new-topics', {
  //   responses: {
  //     '200': {
  //       description: 'NewCategory.NewTopic DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewTopic)) where?: Where<NewTopic>,
  // ): Promise<Count> {
  //   return this.newCategoryRepository.newTopics(id).delete(where);
  // }

}
