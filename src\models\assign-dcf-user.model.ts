import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class AssignDcfUser extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  site?: any[];


  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  comments?: any[];

  @property({
    type: 'number',
  })
  frequency?: number;
  
  @property({
    type: 'number',
  })
  creator_id?: number;
  
  @property({
    type: 'number',
  })
  modifier_id?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  approver_id?: number;

  @property({
    type: 'number',
  })
  user_id?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  start_date?: string;

  @property({
    type: 'string',
  })
  end_date?: string;

  @property({
    type: 'number',
  })
  formCollectionId?: number;

  
  @property({
    type: 'number',
  })
  standard?: number;

  @property({
    type: 'number',
  })
  assignDcfClientId?: number;
  @property({
    type: 'number',
  })
  dcfId?: number;
  @property({
    type: 'number',
  })
  userProfileId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<AssignDcfUser>) {
    super(data);
  }
}

export interface AssignDcfUserRelations {
  // describe navigational properties here
}

export type AssignDcfUserWithRelations = AssignDcfUser & AssignDcfUserRelations;
