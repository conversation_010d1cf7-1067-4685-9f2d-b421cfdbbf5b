import {Entity, model, property} from '@loopback/repository';

@model()
export class ClientInitiative extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  startDate?: string;

  @property({
    type: 'string',
  })
  completionDate?: string;

  @property({
    type: 'number',
  })
  targetReduction?: number;

  @property({
    type: 'number',
  })
  baselineData?: number;

  @property({
    type: 'string',
  })
  measurementUnit?: string;
  @property({
    type: 'object',
  })
  applicability: 'object'
  @property({
    type: 'number',
  })
  category?: number;

  @property({
    type: 'array',
    itemType: 'number',
  })
  responsibility?: number[];

  @property({
    type: 'number',
  })
  investment?: number;

  @property({
    type: 'string',
  })
  monitorMethod?: string;

  @property({
    type: 'string',
  })
  expectedAchievement?: string;

  @property({
    type: 'number',
  })
  currentStatus?: number;

  @property({
    type: 'array',
    itemType: 'number',
  })
  goal?: number[];

  @property({
    type: 'number',
  })
  materialTopic?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  attachment?: any[];

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'string',
  })
  currencyUnit?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<ClientInitiative>) {
    super(data);
  }
}

export interface ClientInitiativeRelations {
  // describe navigational properties here
}

export type ClientInitiativeWithRelations = ClientInitiative & ClientInitiativeRelations;
