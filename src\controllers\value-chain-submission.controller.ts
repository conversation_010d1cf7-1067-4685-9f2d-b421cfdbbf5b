import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ValueChainSubmission} from '../models';
import {ValueChainSubmissionRepository} from '../repositories';

export class ValueChainSubmissionController {
  constructor(
    @repository(ValueChainSubmissionRepository)
    public valueChainSubmissionRepository : ValueChainSubmissionRepository,
  ) {}

  @post('/value-chain-submissions')
  @response(200, {
    description: 'ValueChainSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(ValueChainSubmission)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ValueChainSubmission, {
            title: 'NewValueChainSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    valueChainSubmission: Omit<ValueChainSubmission, 'id'>,
  ): Promise<ValueChainSubmission> {
    return this.valueChainSubmissionRepository.create(valueChainSubmission);
  }

  @get('/value-chain-submissions/count')
  @response(200, {
    description: 'ValueChainSubmission model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ValueChainSubmission) where?: Where<ValueChainSubmission>,
  ): Promise<Count> {
    return this.valueChainSubmissionRepository.count(where);
  }

  @get('/value-chain-submissions')
  @response(200, {
    description: 'Array of ValueChainSubmission model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ValueChainSubmission, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ValueChainSubmission) filter?: Filter<ValueChainSubmission>,
  ): Promise<ValueChainSubmission[]> {
    return this.valueChainSubmissionRepository.find(filter);
  }

  @patch('/value-chain-submissions')
  @response(200, {
    description: 'ValueChainSubmission PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ValueChainSubmission, {partial: true}),
        },
      },
    })
    valueChainSubmission: ValueChainSubmission,
    @param.where(ValueChainSubmission) where?: Where<ValueChainSubmission>,
  ): Promise<Count> {
    return this.valueChainSubmissionRepository.updateAll(valueChainSubmission, where);
  }

  @get('/value-chain-submissions/{id}')
  @response(200, {
    description: 'ValueChainSubmission model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ValueChainSubmission, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ValueChainSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<ValueChainSubmission>
  ): Promise<ValueChainSubmission> {
    return this.valueChainSubmissionRepository.findById(id, filter);
  }

  @patch('/value-chain-submissions/{id}')
  @response(204, {
    description: 'ValueChainSubmission PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ValueChainSubmission, {partial: true}),
        },
      },
    })
    valueChainSubmission: ValueChainSubmission,
  ): Promise<void> {
    await this.valueChainSubmissionRepository.updateById(id, valueChainSubmission);
  }

  @put('/value-chain-submissions/{id}')
  @response(204, {
    description: 'ValueChainSubmission PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() valueChainSubmission: ValueChainSubmission,
  ): Promise<void> {
    await this.valueChainSubmissionRepository.replaceById(id, valueChainSubmission);
  }

  @del('/value-chain-submissions/{id}')
  @response(204, {
    description: 'ValueChainSubmission DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.valueChainSubmissionRepository.deleteById(id);
  }
}
