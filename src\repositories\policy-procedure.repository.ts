import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {PolicyProcedure, PolicyProcedureRelations} from '../models';

export class PolicyProcedureRepository extends DefaultCrudRepository<
  PolicyProcedure,
  typeof PolicyProcedure.prototype.id,
  PolicyProcedureRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(PolicyProcedure, dataSource);
  }
}
