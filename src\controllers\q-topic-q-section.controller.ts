import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  QTopic,
  QSection,
} from '../models';
import {QTopicRepository} from '../repositories';

export class QTopicQSectionController {
  constructor(
    @repository(QTopicRepository) protected qTopicRepository: QTopicRepository,
  ) { }

  @get('/q-topics/{id}/q-sections', {
    responses: {
      '200': {
        description: 'Array of QTopic has many QSection',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QSection)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QSection>,
  ): Promise<QSection[]> {
    return this.qTopicRepository.qSections(id).find(filter);
  }

  @post('/q-topics/{id}/q-sections', {
    responses: {
      '200': {
        description: 'QTopic model instance',
        content: {'application/json': {schema: getModelSchemaRef(QSection)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof QTopic.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QSection, {
            title: 'NewQSectionInQTopic',
            exclude: ['id'],
            optional: ['qTopicId']
          }),
        },
      },
    }) qSection: Omit<QSection, 'id'>,
  ): Promise<QSection> {
    return this.qTopicRepository.qSections(id).create(qSection);
  }

  @patch('/q-topics/{id}/q-sections', {
    responses: {
      '200': {
        description: 'QTopic.QSection PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QSection, {partial: true}),
        },
      },
    })
    qSection: Partial<QSection>,
    @param.query.object('where', getWhereSchemaFor(QSection)) where?: Where<QSection>,
  ): Promise<Count> {
    return this.qTopicRepository.qSections(id).patch(qSection, where);
  }

  @del('/q-topics/{id}/q-sections', {
    responses: {
      '200': {
        description: 'QTopic.QSection DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(QSection)) where?: Where<QSection>,
  ): Promise<Count> {
    return this.qTopicRepository.qSections(id).delete(where);
  }
}
