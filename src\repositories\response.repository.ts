import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {Response, ResponseRelations, UserProfile, StakeHolder} from '../models';
import {UserProfileRepository} from './user-profile.repository';
import {StakeHolderRepository} from './stake-holder.repository';

export class ResponseRepository extends DefaultCrudRepository<
  Response,
  typeof Response.prototype.id,
  ResponseRelations
> {

  public readonly userProfile: BelongsToAccessor<UserProfile, typeof Response.prototype.id>;

  public readonly stakeHolder: BelongsToAccessor<StakeHolder, typeof Response.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('UserProfileRepository') protected userProfileRepositoryGetter: Getter<UserProfileRepository>, @repository.getter('StakeHolderRepository') protected stakeHolderRepositoryGetter: Getter<StakeHolderRepository>,
  ) {
    super(Response, dataSource);
    this.stakeHolder = this.createBelongsToAccessorFor('stakeHolder', stakeHolderRepositoryGetter,);
    this.registerInclusionResolver('stakeHolder', this.stakeHolder.inclusionResolver);
    this.userProfile = this.createBelongsToAccessorFor('userProfile', userProfileRepositoryGetter,);
    this.registerInclusionResolver('userProfile', this.userProfile.inclusionResolver);
  }
}
