import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {TopicName, TopicNameRelations, Frequency} from '../models';
import {FrequencyRepository} from './frequency.repository';

export class TopicNameRepository extends DefaultCrudRepository<
  TopicName,
  typeof TopicName.prototype.id,
  TopicNameRelations
> {

  public readonly frequencies: HasManyRepositoryFactory<Frequency, typeof TopicName.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('FrequencyRepository') protected frequencyRepositoryGetter: Getter<FrequencyRepository>,
  ) {
    super(TopicName, dataSource);
    this.frequencies = this.createHasManyRepositoryFactoryFor('frequencies', frequencyRepositoryGetter,);
    this.registerInclusionResolver('frequencies', this.frequencies.inclusionResolver);
  }
}
