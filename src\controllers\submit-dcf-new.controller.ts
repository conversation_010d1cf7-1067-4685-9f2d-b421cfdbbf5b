import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SubmitDcfNew} from '../models';
import {DpReportNewRepository, SubmitDcfNewRepository, SubmitDcfRepository} from '../repositories';

export class SubmitDcfNewController {
  constructor(
    @repository(SubmitDcfNewRepository)
    public submitDcfNewRepository: SubmitDcfNewRepository,
    @repository(SubmitDcfRepository)
    public submitDcfRepository: SubmitDcfRepository,
    @repository(DpReportNewRepository)
    public dpReportNewRepository: DpReportNewRepository,
  ) { }

  @post('/submit-dcf-news')
  @response(200, {
    description: 'SubmitDcfNew model instance',
    content: {'application/json': {schema: getModelSchemaRef(SubmitDcfNew)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitDcfNew, {
            title: 'NewSubmitDcfNew',
            exclude: ['id'],
          }),
        },
      },
    })
    submitDcfNew: Omit<SubmitDcfNew, 'id'>,
  ): Promise<SubmitDcfNew> {
    return this.submitDcfNewRepository.create(submitDcfNew);
  }

  @get('/submit-dcf-news/count')
  @response(200, {
    description: 'SubmitDcfNew model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SubmitDcfNew) where?: Where<SubmitDcfNew>,
  ): Promise<Count> {
    return this.submitDcfNewRepository.count(where);
  }

  @get('/submit-dcf-news')
  @response(200, {
    description: 'Array of SubmitDcfNew model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SubmitDcfNew, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SubmitDcfNew) filter?: Filter<SubmitDcfNew>,
  ): Promise<SubmitDcfNew[]> {
    return this.submitDcfNewRepository.find(filter);
  }

  // @patch('/submit-dcf-news')
  // @response(200, {
  //   description: 'SubmitDcfNew PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitDcfNew, {partial: true}),
  //       },
  //     },
  //   })
  //   submitDcfNew: SubmitDcfNew,
  //   @param.where(SubmitDcfNew) where?: Where<SubmitDcfNew>,
  // ): Promise<Count> {
  //   return this.submitDcfNewRepository.updateAll(submitDcfNew, where);
  // }

  @get('/submit-dcf-news/{id}')
  @response(200, {
    description: 'SubmitDcfNew model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SubmitDcfNew, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SubmitDcfNew, {exclude: 'where'}) filter?: FilterExcludingWhere<SubmitDcfNew>
  ): Promise<SubmitDcfNew> {
    return this.submitDcfNewRepository.findById(id, filter);
  }

  @patch('/submit-dcf-news/{id}')
  @response(204, {
    description: 'SubmitDcfNew PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitDcfNew, {partial: true}),
        },
      },
    })
    submitDcfNew: SubmitDcfNew,
  ): Promise<void> {
    await this.submitDcfNewRepository.updateById(id, submitDcfNew);
  }

  @put('/submit-dcf-news/{id}')
  @response(204, {
    description: 'SubmitDcfNew PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() submitDcfNew: SubmitDcfNew,
  ): Promise<void> {
    await this.submitDcfNewRepository.replaceById(id, submitDcfNew);
  }

  @del('/submit-dcf-news/{id}')
  @response(204, {
    description: 'SubmitDcfNew DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.submitDcfNewRepository.deleteById(id);
  }




  @del('/submitDcf-dpReports-by-submitId/{id}')
  @response(204, {
    description: 'SubmitDcf & DP Reports DELETE success',
  })
  async deleteDCFDPById(@param.path.number('id') id: number): Promise<void> {
    await this.dpReportNewRepository.deleteAll({submitId: id});
    await this.submitDcfNewRepository.deleteById(id);

  }


  @post('/clone-submit-dcfs')
  @response(200, {
    description: 'SubmitDcf PATCH success',
  })
  async clone(): Promise<Boolean> {
    try {
      let data = await this.submitDcfRepository.find({where: {userProfileId: 94}});

      // Use Promise.all() to await the creation of all objects in parallel
      await Promise.all(data.map(async (item) => {
        delete item.id
        item.reviewed_by = item.submitted_by
        item.reviewed_on = item.created_on
        item.self = true

        await this.submitDcfNewRepository.create(item);
      }));

      // If all creations are successful, return true
      return true;
    } catch (error) {
      // If an error occurs during creation, return false
      console.error('Error occurred:', error);
      return false;
    }
  }
}
