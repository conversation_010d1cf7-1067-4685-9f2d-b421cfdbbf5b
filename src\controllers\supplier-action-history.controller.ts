import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SupplierActionHistory} from '../models';
import {SupplierActionHistoryRepository} from '../repositories';

export class SupplierActionHistoryController {
  constructor(
    @repository(SupplierActionHistoryRepository)
    public supplierActionHistoryRepository : SupplierActionHistoryRepository,
  ) {}

  @post('/supplier-action-histories')
  @response(200, {
    description: 'SupplierActionHistory model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierActionHistory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierActionHistory, {
            title: 'NewSupplierActionHistory',
            exclude: ['id'],
          }),
        },
      },
    })
    supplierActionHistory: Omit<SupplierActionHistory, 'id'>,
  ): Promise<SupplierActionHistory> {
    return this.supplierActionHistoryRepository.create(supplierActionHistory);
  }

  @get('/supplier-action-histories/count')
  @response(200, {
    description: 'SupplierActionHistory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplierActionHistory) where?: Where<SupplierActionHistory>,
  ): Promise<Count> {
    return this.supplierActionHistoryRepository.count(where);
  }

  @get('/supplier-action-histories')
  @response(200, {
    description: 'Array of SupplierActionHistory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplierActionHistory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplierActionHistory) filter?: Filter<SupplierActionHistory>,
  ): Promise<SupplierActionHistory[]> {
    return this.supplierActionHistoryRepository.find(filter);
  }

  @patch('/supplier-action-histories')
  @response(200, {
    description: 'SupplierActionHistory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierActionHistory, {partial: true}),
        },
      },
    })
    supplierActionHistory: SupplierActionHistory,
    @param.where(SupplierActionHistory) where?: Where<SupplierActionHistory>,
  ): Promise<Count> {
    return this.supplierActionHistoryRepository.updateAll(supplierActionHistory, where);
  }

  @get('/supplier-action-histories/{id}')
  @response(200, {
    description: 'SupplierActionHistory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplierActionHistory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SupplierActionHistory, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplierActionHistory>
  ): Promise<SupplierActionHistory> {
    return this.supplierActionHistoryRepository.findById(id, filter);
  }

  @patch('/supplier-action-histories/{id}')
  @response(204, {
    description: 'SupplierActionHistory PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierActionHistory, {partial: true}),
        },
      },
    })
    supplierActionHistory: SupplierActionHistory,
  ): Promise<void> {
    await this.supplierActionHistoryRepository.updateById(id, supplierActionHistory);
  }

  @put('/supplier-action-histories/{id}')
  @response(204, {
    description: 'SupplierActionHistory PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() supplierActionHistory: SupplierActionHistory,
  ): Promise<void> {
    await this.supplierActionHistoryRepository.replaceById(id, supplierActionHistory);
  }

  @del('/supplier-action-histories/{id}')
  @response(204, {
    description: 'SupplierActionHistory DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.supplierActionHistoryRepository.deleteById(id);
  }
}
