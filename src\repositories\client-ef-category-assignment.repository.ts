import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ClientEfCategoryAssignment, ClientEfCategoryAssignmentRelations} from '../models';

export class ClientEfCategoryAssignmentRepository extends DefaultCrudRepository<
  ClientEfCategoryAssignment,
  typeof ClientEfCategoryAssignment.prototype.id,
  ClientEfCategoryAssignmentRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ClientEfCategoryAssignment, dataSource);
  }
}
