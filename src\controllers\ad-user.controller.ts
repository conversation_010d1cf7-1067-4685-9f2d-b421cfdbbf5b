import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import { AdUser } from '../models';
import { AdUserRepository } from '../repositories';

export class AdUserController {
  constructor(
    @repository(AdUserRepository)
    public adUserRepository: AdUserRepository,
  ) { }

  @post('/ad-users')
  @response(200, {
    description: 'AdUser model instance',
    content: { 'application/json': { schema: getModelSchemaRef(AdUser) } },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AdUser, {
            title: 'NewAdUser',
            exclude: ['id'],
          }),
        },
      },
    })
    adUser: Omit<AdUser, 'id'>,
  ): Promise<AdUser> {

    const user = await this.adUserRepository.findOne({ where: { email: adUser.email } })
    if (user) {
      const updatedUser = await this.adUserRepository.update(adUser, { where: { email: adUser.email } })
      return user;
    } else {
      return this.adUserRepository.create(adUser);
    }

  }

  @get('/ad-users/count')
  @response(200, {
    description: 'AdUser model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(AdUser) where?: Where<AdUser>,
  ): Promise<Count> {
    return this.adUserRepository.count(where);
  }

  @get('/ad-users')
  @response(200, {
    description: 'Array of AdUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AdUser, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @param.filter(AdUser) filter?: Filter<AdUser>,
  ): Promise<AdUser[]> {
    return this.adUserRepository.find(filter);
  }

  @post('/ad-users-by-mail')
  @response(200, {
    description: 'AdUser GET success',
  })
  async getById(

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AdUser, { partial: true }),
        },
      },
    })
    adUser: AdUser,
  ): Promise<any> {
    const user = await this.adUserRepository.findOne({ where: { email: adUser.email } });
    if (user) {
      return user;
    } else {
      return '';
    }

  }


  @patch('/ad-users-by-mail')
  @response(200, {
    description: 'AdUser PATCH success',
  })
  async updateByMail(

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AdUser, { partial: true }),
        },
      },
    })
    adUser: AdUser,
  ): Promise<void> {
    await this.adUserRepository.update(adUser, { where: { email: adUser.email } });
  }



  @patch('/ad-users')
  @response(200, {
    description: 'AdUser PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AdUser, { partial: true }),
        },
      },
    })
    adUser: AdUser,
    @param.where(AdUser) where?: Where<AdUser>,
  ): Promise<Count> {
    return this.adUserRepository.updateAll(adUser, where);
  }

  @get('/ad-users/{id}')
  @response(200, {
    description: 'AdUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AdUser, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AdUser, { exclude: 'where' }) filter?: FilterExcludingWhere<AdUser>
  ): Promise<AdUser> {
    return this.adUserRepository.findById(id, filter);
  }

  @patch('/ad-users/{id}')
  @response(204, {
    description: 'AdUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AdUser, { partial: true }),
        },
      },
    })
    adUser: AdUser,
  ): Promise<void> {
    await this.adUserRepository.updateById(id, adUser);
  }

  @put('/ad-users/{id}')
  @response(204, {
    description: 'AdUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() adUser: AdUser,
  ): Promise<void> {
    await this.adUserRepository.replaceById(id, adUser);
  }

  @del('/ad-users/{id}')
  @response(204, {
    description: 'AdUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.adUserRepository.deleteById(id);
  }
}
