import {
  repository,
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
} from '@loopback/rest';
import {
  SupplierAssignmentSubmission,
  VendorCode,
} from '../models';
import {SupplierAssignmentSubmissionRepository} from '../repositories';

export class SupplierAssignmentSubmissionVendorCodeController {
  constructor(
    @repository(SupplierAssignmentSubmissionRepository)
    public supplierAssignmentSubmissionRepository: SupplierAssignmentSubmissionRepository,
  ) { }

  @get('/supplier-assignment-submissions/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to SupplierAssignmentSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof SupplierAssignmentSubmission.prototype.id,
  ): Promise<VendorCode> {
    return this.supplierAssignmentSubmissionRepository.vendor(id);
  }
}
