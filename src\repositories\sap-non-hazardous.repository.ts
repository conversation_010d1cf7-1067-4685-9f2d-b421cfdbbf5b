import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SapNonHazardous, SapNonHazardousRelations} from '../models';

export class SapNonHazardousRepository extends DefaultCrudRepository<
  SapNonHazardous,
  typeof SapNonHazardous.prototype.id,
  SapNonHazardousRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SapNonHazardous, dataSource);
  }
}
