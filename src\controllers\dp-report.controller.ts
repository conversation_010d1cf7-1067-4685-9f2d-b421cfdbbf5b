import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DpReport} from '../models';
import {DpReportRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class DpReportController {
  constructor(
    @repository(DpReportRepository)
    public dpReportRepository : DpReportRepository,
  ) {}

  @post('/dp-reports')
  @response(200, {
    description: 'DpReport model instance',
    content: {'application/json': {schema: getModelSchemaRef(DpReport)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DpReport, {
            title: 'NewDpReport',
            exclude: ['id'],
          }),
        },
      },
    })
    dpReport: Omit<DpReport, 'id'>,
  ): Promise<DpReport> {
    return this.dpReportRepository.create(dpReport);
  }

  @get('/dp-reports/count')
  @response(200, {
    description: 'DpReport model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DpReport) where?: Where<DpReport>,
  ): Promise<Count> {
    return this.dpReportRepository.count(where);
  }

  @get('/dp-reports')
  @response(200, {
    description: 'Array of DpReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DpReport, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DpReport) filter?: Filter<DpReport>,
  ): Promise<DpReport[]> {
    return this.dpReportRepository.find(filter);
  }

  // @patch('/dp-reports')
  // @response(200, {
  //   description: 'DpReport PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(DpReport, {partial: true}),
  //       },
  //     },
  //   })
  //   dpReport: DpReport,
  //   @param.where(DpReport) where?: Where<DpReport>,
  // ): Promise<Count> {
  //   return this.dpReportRepository.updateAll(dpReport, where);
  // }

  @get('/dp-reports/{id}')
  @response(200, {
    description: 'DpReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DpReport, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DpReport, {exclude: 'where'}) filter?: FilterExcludingWhere<DpReport>
  ): Promise<DpReport> {
    return this.dpReportRepository.findById(id, filter);
  }

  @patch('/dp-reports/{id}')
  @response(204, {
    description: 'DpReport PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DpReport, {partial: true}),
        },
      },
    })
    dpReport: DpReport,
  ): Promise<void> {
    await this.dpReportRepository.updateById(id, dpReport);
  }

  @put('/dp-reports/{id}')
  @response(204, {
    description: 'DpReport PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() dpReport: DpReport,
  ): Promise<void> {
    await this.dpReportRepository.replaceById(id, dpReport);
  }

  @del('/dp-reports/{id}')
  @response(204, {
    description: 'DpReport DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.dpReportRepository.deleteById(id);
  }

  @del('/dp-reports/by/submitId/{id}')
  @response(204, {
    description: 'DpReport DELETE success',
  })
  async deleteBySubmitId(@param.path.number('id') id: number): Promise<void> {
    await this.dpReportRepository.deleteAll({submitId: id})
  }

}
