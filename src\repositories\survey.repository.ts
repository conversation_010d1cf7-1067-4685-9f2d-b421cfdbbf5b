import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasOneRepositoryFactory, HasManyRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {Survey, SurveyRelations, SaveSurvey, Response, Category, SubSurvey} from '../models';
import {SaveSurveyRepository} from './save-survey.repository';
import {ResponseRepository} from './response.repository';
import {CategoryRepository} from './category.repository';
import {SubSurveyRepository} from './sub-survey.repository';


export class SurveyRepository extends DefaultCrudRepository<
  Survey,
  typeof Survey.prototype.id,
  SurveyRelations
> {

  public readonly saveSurvey: HasOneRepositoryFactory<SaveSurvey, typeof Survey.prototype.id>;

  public readonly responses: HasManyRepositoryFactory<Response, typeof Survey.prototype.id>;

  public readonly category: BelongsToAccessor<Category, typeof Survey.prototype.id>;

  public readonly subSurveys: HasManyRepositoryFactory<SubSurvey, typeof Survey.prototype.id>;

  public readonly surveys: HasManyRepositoryFactory<Survey, typeof Survey.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('SaveSurveyRepository') protected saveSurveyRepositoryGetter: Getter<SaveSurveyRepository>, @repository.getter('ResponseRepository') protected responseRepositoryGetter: Getter<ResponseRepository>, @repository.getter('CategoryRepository') protected categoryRepositoryGetter: Getter<CategoryRepository>, @repository.getter('SubSurveyRepository') protected subSurveyRepositoryGetter: Getter<SubSurveyRepository>, @repository.getter('SurveyRepository') protected surveyRepositoryGetter: Getter<SurveyRepository>,
  ) {
    super(Survey, dataSource);
    this.surveys = this.createHasManyRepositoryFactoryFor('surveys', surveyRepositoryGetter,);
    this.registerInclusionResolver('surveys', this.surveys.inclusionResolver);
    this.subSurveys = this.createHasManyRepositoryFactoryFor('subSurveys', subSurveyRepositoryGetter,);
    this.registerInclusionResolver('subSurveys', this.subSurveys.inclusionResolver);
    this.category = this.createBelongsToAccessorFor('category', categoryRepositoryGetter,);
    this.registerInclusionResolver('category', this.category.inclusionResolver);
    this.responses = this.createHasManyRepositoryFactoryFor('responses', responseRepositoryGetter,);
    this.registerInclusionResolver('responses', this.responses.inclusionResolver);
    this.saveSurvey = this.createHasOneRepositoryFactoryFor('saveSurvey', saveSurveyRepositoryGetter);
    this.registerInclusionResolver('saveSurvey', this.saveSurvey.inclusionResolver);
  }
}
