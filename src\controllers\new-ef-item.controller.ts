import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
} from '@loopback/rest';
import {NewEfItem} from '../models';
import {NewEfItemRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfItemController {
  constructor(
    @repository(NewEfItemRepository)
    public newEfItemRepository : NewEfItemRepository,
  ) {}

  @post('/new-ef-items')
  @response(200, {
    description: 'NewEfItem model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEfItem)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfItem, {
            title: 'NewNewEfItem',
            exclude: ['id'],
          }),
        },
      },
    })
    newEfItem: Omit<NewEfItem, 'id'>,
  ): Promise<NewEfItem> {
    const existingObject = await this.newEfItemRepository.findOne({
      where: {
        and: [
          {ef_id: newEfItem.ef_id},
          {newEfId: newEfItem.newEfId},
        ],
      },
    });
    if (existingObject) {
      throw new HttpErrors.BadRequest(`Combination ef_id ${newEfItem.ef_id} and newEfId ${newEfItem.newEfId} already exists`);

    } else {
      return this.newEfItemRepository.create(newEfItem);

    }
  }

  @get('/new-ef-items/count')
  @response(200, {
    description: 'NewEfItem model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEfItem) where?: Where<NewEfItem>,
  ): Promise<Count> {
    return this.newEfItemRepository.count(where);
  }

  @get('/new-ef-items')
  @response(200, {
    description: 'Array of NewEfItem model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEfItem, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEfItem) filter?: Filter<NewEfItem>,
  ): Promise<NewEfItem[]> {
    return this.newEfItemRepository.find(filter);
  }

  // @patch('/new-ef-items')
  // @response(200, {
  //   description: 'NewEfItem PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfItem, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfItem: NewEfItem,
  //   @param.where(NewEfItem) where?: Where<NewEfItem>,
  // ): Promise<Count> {
  //   return this.newEfItemRepository.updateAll(newEfItem, where);
  // }

  @get('/new-ef-items/{id}')
  @response(200, {
    description: 'NewEfItem model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEfItem, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEfItem, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEfItem>
  ): Promise<NewEfItem> {
    return this.newEfItemRepository.findById(id, filter);
  }

  @patch('/new-ef-items/{id}')
  @response(204, {
    description: 'NewEfItem PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfItem, {partial: true}),
        },
      },
    })
    newEfItem: NewEfItem,
  ): Promise<void> {
    await this.newEfItemRepository.updateById(id, newEfItem);
  }

  @put('/new-ef-items/{id}')
  @response(204, {
    description: 'NewEfItem PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEfItem: NewEfItem,
  ): Promise<void> {
    await this.newEfItemRepository.replaceById(id, newEfItem);
  }

  @del('/new-ef-items/{id}')
  @response(204, {
    description: 'NewEfItem DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfItemRepository.deleteById(id);
  }

  @post('/new-ef-items/multiple')
  @response(200,
    {
      description: 'Array of yourModel model instances',
      content: {
        'application/json': {
          schema: {
            type: 'array',
            items: {'x-ts-type': NewEfItem},
          },
        },
      }
    }
  )
  async createNewEFItemsRowsFromArray(
    @requestBody() dataArray: NewEfItem[],
  ): Promise<{}> {
    const createdRows: NewEfItem[] = [];
    const rejectedRows: NewEfItem[] = []
    for (const item of dataArray) {
      const existingObject = await this.newEfItemRepository.findOne({
        where: {
          and: [
            {ef_id: item.ef_id},
            {newEfId: item.newEfId},
          ],
        },
      });
      if (existingObject) {
        rejectedRows.push(existingObject);
      } else {
        const createdRow = await this.newEfItemRepository.create(item);
        createdRows.push(createdRow);
      }

    }

    return {status: rejectedRows.length, rejectedRows, createdRows}
  }


}



