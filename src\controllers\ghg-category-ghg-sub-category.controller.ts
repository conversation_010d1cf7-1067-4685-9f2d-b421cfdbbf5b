import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  GhgCategory,
  GhgSubCategory,
} from '../models';
import {GhgCategoryRepository} from '../repositories';

export class GhgCategoryGhgSubCategoryController {
  constructor(
    @repository(GhgCategoryRepository) protected ghgCategoryRepository: GhgCategoryRepository,
  ) { }

  @get('/ghg-categories/{id}/ghg-sub-categories', {
    responses: {
      '200': {
        description: 'Array of GhgCategory has many GhgSubCategory',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhgSubCategory)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<GhgSubCategory>,
  ): Promise<GhgSubCategory[]> {
    return this.ghgCategoryRepository.ghgSubCategories(id).find(filter);
  }

  @post('/ghg-categories/{id}/ghg-sub-categories', {
    responses: {
      '200': {
        description: 'GhgCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(GhgSubCategory)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof GhgCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgSubCategory, {
            title: 'NewGhgSubCategoryInGhgCategory',
            exclude: ['id'],
            optional: ['ghgCategoryId']
          }),
        },
      },
    }) ghgSubCategory: Omit<GhgSubCategory, 'id'>,
  ): Promise<GhgSubCategory> {
    return this.ghgCategoryRepository.ghgSubCategories(id).create(ghgSubCategory);
  }

  @patch('/ghg-categories/{id}/ghg-sub-categories', {
    responses: {
      '200': {
        description: 'GhgCategory.GhgSubCategory PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgSubCategory, {partial: true}),
        },
      },
    })
    ghgSubCategory: Partial<GhgSubCategory>,
    @param.query.object('where', getWhereSchemaFor(GhgSubCategory)) where?: Where<GhgSubCategory>,
  ): Promise<Count> {
    return this.ghgCategoryRepository.ghgSubCategories(id).patch(ghgSubCategory, where);
  }

  @del('/ghg-categories/{id}/ghg-sub-categories', {
    responses: {
      '200': {
        description: 'GhgCategory.GhgSubCategory DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(GhgSubCategory)) where?: Where<GhgSubCategory>,
  ): Promise<Count> {
    return this.ghgCategoryRepository.ghgSubCategories(id).delete(where);
  }
}
