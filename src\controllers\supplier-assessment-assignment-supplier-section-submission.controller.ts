import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SupplierAssessmentAssignment,
  SupplierSectionSubmission,
} from '../models';
import {SupplierAssessmentAssignmentRepository} from '../repositories';

export class SupplierAssessmentAssignmentSupplierSectionSubmissionController {
  constructor(
    @repository(SupplierAssessmentAssignmentRepository) protected supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
  ) { }

  @get('/supplier-assessment-assignments/{id}/assessment-section-submissions', {
    responses: {
      '200': {
        description: 'Array of SupplierAssessmentAssignment has many SupplierSectionSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplierSectionSubmission)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<SupplierSectionSubmission>,
  ): Promise<SupplierSectionSubmission[]> {
    return this.supplierAssessmentAssignmentRepository.supplierSectionSubmissions(id).find(filter);
  }

  @post('/supplier-assessment-assignments/{id}/assessment-section-submissions', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(SupplierSectionSubmission)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof SupplierAssessmentAssignment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierSectionSubmission, {
            title: 'NewSupplierSectionSubmissionInSupplierAssessmentAssignment',
            exclude: ['id'],
            optional: ['supplierAssessmentAssignmentId']
          }),
        },
      },
    }) SupplierSectionSubmission: Omit<SupplierSectionSubmission, 'id'>,
  ): Promise<SupplierSectionSubmission> {
    return this.supplierAssessmentAssignmentRepository.supplierSectionSubmissions(id).create(SupplierSectionSubmission);
  }

  @patch('/supplier-assessment-assignments/{id}/assessment-section-submissions', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment.supplierSectionSubmissions PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierSectionSubmission, {partial: true}),
        },
      },
    })
    SupplierSectionSubmission: Partial<SupplierSectionSubmission>,
    @param.query.object('where', getWhereSchemaFor(SupplierSectionSubmission)) where?: Where<SupplierSectionSubmission>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.supplierSectionSubmissions(id).patch(SupplierSectionSubmission, where);
  }

  @del('/supplier-assessment-assignments/{id}/assessment-section-submissions', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment.supplierSectionSubmissions DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(SupplierSectionSubmission)) where?: Where<SupplierSectionSubmission>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.supplierSectionSubmissions(id).delete(where);
  }
}
