import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewEfSubcategory3,
  NewEfSubcategory4,
} from '../models';
import {NewEfSubcategory3Repository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfSubcategory3NewEfSubcategory4Controller {
  constructor(
    @repository(NewEfSubcategory3Repository) protected newEfSubcategory3Repository: NewEfSubcategory3Repository,
  ) { }

  @get('/new-ef-subcategory3s/{id}/new-ef-subcategory4s', {
    responses: {
      '200': {
        description: 'Array of NewEfSubcategory3 has many NewEfSubcategory4',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfSubcategory4)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEfSubcategory4>,
  ): Promise<NewEfSubcategory4[]> {
    return this.newEfSubcategory3Repository.newEfSubcategory4s(id).find(filter);
  }

  @post('/new-ef-subcategory3s/{id}/new-ef-subcategory4s', {
    responses: {
      '200': {
        description: 'NewEfSubcategory3 model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEfSubcategory4)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEfSubcategory3.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory4, {
            title: 'NewNewEfSubcategory4InNewEfSubcategory3',
            exclude: ['id'],
            optional: ['newEfSubcategory3Id']
          }),
        },
      },
    }) newEfSubcategory4: Omit<NewEfSubcategory4, 'id'>,
  ): Promise<NewEfSubcategory4> {
    return this.newEfSubcategory3Repository.newEfSubcategory4s(id).create(newEfSubcategory4);
  }

  // @patch('/new-ef-subcategory3s/{id}/new-ef-subcategory4s', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfSubcategory3.NewEfSubcategory4 PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfSubcategory4, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfSubcategory4: Partial<NewEfSubcategory4>,
  //   @param.query.object('where', getWhereSchemaFor(NewEfSubcategory4)) where?: Where<NewEfSubcategory4>,
  // ): Promise<Count> {
  //   return this.newEfSubcategory3Repository.newEfSubcategory4s(id).patch(newEfSubcategory4, where);
  // }

  // @del('/new-ef-subcategory3s/{id}/new-ef-subcategory4s', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfSubcategory3.NewEfSubcategory4 DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewEfSubcategory4)) where?: Where<NewEfSubcategory4>,
  // ): Promise<Count> {
  //   return this.newEfSubcategory3Repository.newEfSubcategory4s(id).delete(where);
  // }
}
