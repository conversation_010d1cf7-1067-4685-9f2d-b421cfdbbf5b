import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ResponseFormCollection, ResponseFormCollectionRelations} from '../models';

export class ResponseFormCollectionRepository extends DefaultCrudRepository<
  ResponseFormCollection,
  typeof ResponseFormCollection.prototype.id,
  ResponseFormCollectionRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ResponseFormCollection, dataSource);
  }
}
