import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierAssessmentAssignment,
  UserProfile,
} from '../models';
import {SupplierAssessmentAssignmentRepository} from '../repositories';

export class SupplierAssessmentAssignmentUserProfileController {
  constructor(
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
  ) { }

  @get('/supplier-assessment-assignments/{id}/user-profile', {
    responses: {
      '200': {
        description: 'UserProfile belonging to SupplierAssessmentAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UserProfile)},
          },
        },
      },
    },
  })
  async getUserProfile(
    @param.path.string('id') id: typeof SupplierAssessmentAssignment.prototype.id,
  ): Promise<UserProfile> {
    return this.supplierAssessmentAssignmentRepository.supplier(id);
  }
}
