import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository, HasOneRepositoryFactory, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DealerAssessmentAssignment, DealerAssessmentAssignmentRelations, DealerResponseForm, UserProfile, DealerAuditorChecklistSubmission, VendorCode, Action} from '../models';
import {DealerResponseFormRepository} from './dealer-response-form.repository';
import {UserProfileRepository} from './user-profile.repository';
import {DealerAuditorChecklistSubmissionRepository} from './dealer-auditor-checklist-submission.repository';
import {VendorCodeRepository} from './vendor-code.repository';
import {ActionRepository} from './action.repository';

export class DealerAssessmentAssignmentRepository extends DefaultCrudRepository<
  DealerAssessmentAssignment,
  typeof DealerAssessmentAssignment.prototype.id,
  DealerAssessmentAssignmentRelations
> {

  public readonly dealer: BelongsToAccessor<UserProfile, typeof DealerAssessmentAssignment.prototype.id>;


  public readonly form: BelongsToAccessor<DealerResponseForm, typeof DealerAssessmentAssignment.prototype.id>;

  public readonly dealerAuditorChecklistSubmission: HasOneRepositoryFactory<DealerAuditorChecklistSubmission, typeof DealerAssessmentAssignment.prototype.id>;

  public readonly vendor: BelongsToAccessor<VendorCode, typeof DealerAssessmentAssignment.prototype.id>;

  public readonly actions: HasManyRepositoryFactory<Action, typeof DealerAssessmentAssignment.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('UserProfileRepository') protected userProfileRepositoryGetter: Getter<UserProfileRepository>, @repository.getter('DealerResponseFormRepository') protected dealerResponseFormRepositoryGetter: Getter<DealerResponseFormRepository>, @repository.getter('DealerAuditorChecklistSubmissionRepository') protected dealerAuditorChecklistSubmissionRepositoryGetter: Getter<DealerAuditorChecklistSubmissionRepository>, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>, @repository.getter('ActionRepository') protected actionRepositoryGetter: Getter<ActionRepository>,
  ) {
    super(DealerAssessmentAssignment, dataSource);
    this.actions = this.createHasManyRepositoryFactoryFor('actions', actionRepositoryGetter,);
    this.registerInclusionResolver('actions', this.actions.inclusionResolver);
    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
    this.dealerAuditorChecklistSubmission = this.createHasOneRepositoryFactoryFor('dealerAuditorChecklistSubmission', dealerAuditorChecklistSubmissionRepositoryGetter);
    this.registerInclusionResolver('dealerAuditorChecklistSubmission', this.dealerAuditorChecklistSubmission.inclusionResolver);
    this.form = this.createBelongsToAccessorFor('form', dealerResponseFormRepositoryGetter,);
    this.registerInclusionResolver('form', this.form.inclusionResolver);

    this.dealer = this.createBelongsToAccessorFor('dealer', userProfileRepositoryGetter,);
    this.registerInclusionResolver('dealer', this.dealer.inclusionResolver);
  }
}
