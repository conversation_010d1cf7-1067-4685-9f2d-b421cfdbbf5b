import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from "luxon";
import {NewMetric} from '../models';
import {NewDataPointRepository, NewMetricRepository} from '../repositories';



export class NewMetricController {
  constructor(
    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,
    @repository(NewDataPointRepository)
    public newDataPointRepository: NewDataPointRepository,
  ) { }

  @post('/new-metrics')
  @response(200, {
    description: 'NewMetric model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewMetric)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewMetric, {
            title: 'NewNewMetric',
            exclude: ['id'],
          }),
        },
      },
    })
    newMetric: Omit<NewMetric, 'id'>,
  ): Promise<NewMetric> {
    const Metric = await this.newMetricRepository.create(newMetric);
    const id = Metric.id;
    const suffix = newMetric.suffix + id;
    Metric.suffix = suffix;
    await this.newMetricRepository.updateById(id, {suffix: suffix});
    return Metric;
  }

  @post('/clone-metric-only')
  async cloneMetricOnly(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object', properties: {metric_id: {type: 'number'}, tag: {type: 'string'}}
        }
      },
    },
  })
  clone_id: {metric_id: number, tag: string}):
    Promise<object> {
    const {metric_id, tag} = clone_id
    let metric_data = await this.newMetricRepository.findById(metric_id)
    if (metric_data) {
      let new_metric = {...metric_data, extra: 1}
      delete new_metric.id
      delete new_metric.data2
      delete new_metric.order
      new_metric.tag = tag
      new_metric.cloneMetricId = metric_id
      new_metric.title = 'Cloned_' + new_metric.title
      new_metric.created = DateTime.utc().toString()
      let new_id = await this.newMetricRepository.create(new_metric)
      if (new_id) {
        return {result: true, data: new_id}
      } else {
        return {result: false}
      }
    } else {
      return {result: false}
    }

  }

  @post('/clone-with-datapoint')
  async cloneWithDataPoints(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object', properties: {metric_id: {type: 'number'}, tag: {type: 'number'}}
        }
      },
    },
  })
  clone_id: {metric_id: number, tag: number}):
    Promise<object> {
    const {metric_id, tag} = clone_id
    let metric_data = await this.newMetricRepository.findById(metric_id)
    if (metric_data) {
      let new_metric = {...metric_data, extra: 1}
      delete new_metric.id
      delete new_metric.data2
      delete new_metric.order
      new_metric.tag = tag
      new_metric.cloneMetricId = metric_id
      new_metric.title = 'Cloned_' + new_metric.title
      new_metric.created = DateTime.utc().toString()
      let new_id = await this.newMetricRepository.create(new_metric)
      if (new_id) {
        let ndp = await this.newDataPointRepository.find({where: {newMetricId: metric_id}})
        let ndp_array: any[] = []
        ndp.forEach(async (ndp_data) => {
          let newObj = {...ndp_data, extra: 1}
          newObj.newMetricId = new_id.id

          newObj.created = DateTime.utc().toString()
          newObj.cloneMetricId = metric_id
          newObj.cloneDataPointId = newObj.id
          delete newObj.id
          delete newObj.order
          delete newObj.data2
          ndp_array.push(newObj)
        })
        let create_ndp = await this.newDataPointRepository.createAll(ndp_array).then(() => {
          return true
        })
        console.log(create_ndp)
        if (create_ndp) {
          return {result: true, data: new_id}
        } else {
          return {result: false}
        }
      } else {
        return {result: false}
      }
    } else {
      return {result: false}
    }

  }
  @get('/new-metrics/count')
  @response(200, {
    description: 'NewMetric model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewMetric) where?: Where<NewMetric>,
  ): Promise<Count> {
    return this.newMetricRepository.count(where);
  }

  @get('/new-metrics')
  @response(200, {
    description: 'Array of NewMetric model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewMetric, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewMetric) filter?: Filter<NewMetric>,
  ): Promise<NewMetric[]> {
    return this.newMetricRepository.find(filter);
  }

  // @patch('/new-metrics')
  // @response(200, {
  //   description: 'NewMetric PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewMetric, {partial: true}),
  //       },
  //     },
  //   })
  //   newMetric: NewMetric,
  //   @param.where(NewMetric) where?: Where<NewMetric>,
  // ): Promise<Count> {
  //   return this.newMetricRepository.updateAll(newMetric, where);
  // }

  @patch('/new-metrics')
  @response(200, {
    description: 'NewTopic PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            properties: {
              newMetrics: {

                type: 'array',
                items: getModelSchemaRef(NewMetric, {partial: true}),
              },
            },
          },
        },
      },
    })
    newMetrics: NewMetric[],
    @param.where(NewMetric) where?: Where<NewMetric>,
  ): Promise<Count> {
    const updatePromises = newMetrics.map((newMetric) => {
      return this.newMetricRepository.updateById(newMetric.id, {order: newMetric.order});
    });

    await Promise.all(updatePromises);

    const totalCount = newMetrics.length;
    return {count: totalCount};
  }

  @get('/new-metrics/{id}')
  @response(200, {
    description: 'NewMetric model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewMetric, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewMetric, {exclude: 'where'}) filter?: FilterExcludingWhere<NewMetric>
  ): Promise<NewMetric> {
    return this.newMetricRepository.findById(id, filter);
  }

  @patch('/new-metrics/{id}')
  @response(204, {
    description: 'NewMetric PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewMetric, {partial: true}),
        },
      },
    })
    newMetric: NewMetric,
  ): Promise<void> {
    await this.newMetricRepository.updateById(id, newMetric);
  }

  @put('/new-metrics/{id}')
  @response(204, {
    description: 'NewMetric PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newMetric: NewMetric,
  ): Promise<void> {
    await this.newMetricRepository.replaceById(id, newMetric);
  }

  @del('/new-metrics/{id}')
  @response(204, {
    description: 'NewMetric DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newMetricRepository.deleteById(id);
  }
}
