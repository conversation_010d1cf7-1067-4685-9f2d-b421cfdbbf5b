import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {IndicatorSection} from '../models';
import {IndicatorSectionRepository} from '../repositories';

export class IndicatorSectionController {
  constructor(
    @repository(IndicatorSectionRepository)
    public indicatorSectionRepository: IndicatorSectionRepository,
  ) { }

  @post('/indicator-sections')
  @response(200, {
    description: 'IndicatorSection model instance',
    content: {'application/json': {schema: getModelSchemaRef(IndicatorSection)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorSection, {
            title: 'NewIndicatorSection',
            exclude: ['id'],
          }),
        },
      },
    })
    indicatorSection: Omit<IndicatorSection, 'id'>,
  ): Promise<IndicatorSection> {
    return this.indicatorSectionRepository.create(indicatorSection);
  }

  @get('/indicator-sections/count')
  @response(200, {
    description: 'IndicatorSection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(IndicatorSection) where?: Where<IndicatorSection>,
  ): Promise<Count> {
    return this.indicatorSectionRepository.count(where);
  }

  @get('/indicator-sections')
  @response(200, {
    description: 'Array of IndicatorSection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(IndicatorSection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(IndicatorSection) filter?: Filter<IndicatorSection>,
  ): Promise<IndicatorSection[]> {
    return this.indicatorSectionRepository.find(filter);
  }

  // @patch('/indicator-sections')
  // @response(200, {
  //   description: 'IndicatorSection PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(IndicatorSection, {partial: true}),
  //       },
  //     },
  //   })
  //   indicatorSection: IndicatorSection,
  //   @param.where(IndicatorSection) where?: Where<IndicatorSection>,
  // ): Promise<Count> {
  //   return this.indicatorSectionRepository.updateAll(indicatorSection, where);
  // }

  @get('/indicator-sections/{id}')
  @response(200, {
    description: 'IndicatorSection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(IndicatorSection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(IndicatorSection, {exclude: 'where'}) filter?: FilterExcludingWhere<IndicatorSection>
  ): Promise<IndicatorSection> {
    return this.indicatorSectionRepository.findById(id, filter);
  }

  @patch('/indicator-sections/{id}')
  @response(204, {
    description: 'IndicatorSection PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorSection, {partial: true}),
        },
      },
    })
    indicatorSection: IndicatorSection,
  ): Promise<void> {
    await this.indicatorSectionRepository.updateById(id, indicatorSection);
  }

  @put('/indicator-sections/{id}')
  @response(204, {
    description: 'IndicatorSection PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() indicatorSection: IndicatorSection,
  ): Promise<void> {
    await this.indicatorSectionRepository.replaceById(id, indicatorSection);
  }

  @del('/indicator-sections/{id}')
  @response(204, {
    description: 'IndicatorSection DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.indicatorSectionRepository.deleteById(id);
  }
}
