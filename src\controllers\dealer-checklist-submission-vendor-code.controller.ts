import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  DealerChecklistSubmission,
  VendorCode,
} from '../models';
import {DealerChecklistSubmissionRepository} from '../repositories';

export class DealerChecklistSubmissionVendorCodeController {
  constructor(
    @repository(DealerChecklistSubmissionRepository)
    public dealerChecklistSubmissionRepository: DealerChecklistSubmissionRepository,
  ) { }

  @get('/dealer-checklist-submissions/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to DealerChecklistSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof DealerChecklistSubmission.prototype.id,
  ): Promise<VendorCode> {
    return this.dealerChecklistSubmissionRepository.vendor(id);
  }
}
