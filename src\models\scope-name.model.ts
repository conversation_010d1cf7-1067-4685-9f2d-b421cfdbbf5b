import {Entity, model, property, hasMany} from '@loopback/repository';
import {TopicName} from './topic-name.model';

@model({settings: {strict: false}})
export class ScopeName extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];
  
  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'number',
  })
  moduleNameId?: number;

  @hasMany(() => TopicName)
  topicNames: TopicName[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<ScopeName>) {
    super(data);
  }
}

export interface ScopeNameRelations {
  // describe navigational properties here
}

export type ScopeNameWithRelations = ScopeName & ScopeNameRelations;
