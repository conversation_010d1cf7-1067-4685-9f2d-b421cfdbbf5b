import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AssessmentSubSection1,
  AssessmentSubSection2,
} from '../models';
import {AssessmentSubSection1Repository} from '../repositories';

export class AssessmentSubSection1AssessmentSubSection2Controller {
  constructor(
    @repository(AssessmentSubSection1Repository) protected assessmentSubSection1Repository: AssessmentSubSection1Repository,
  ) { }

  @get('/assessment-sub-section1s/{id}/assessment-sub-section2s', {
    responses: {
      '200': {
        description: 'Array of AssessmentSubSection1 has many AssessmentSubSection2',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssessmentSubSection2)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<AssessmentSubSection2>,
  ): Promise<AssessmentSubSection2[]> {
    return this.assessmentSubSection1Repository.assessmentSubSection2s(id).find(filter);
  }

  @post('/assessment-sub-section1s/{id}/assessment-sub-section2s', {
    responses: {
      '200': {
        description: 'AssessmentSubSection1 model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssessmentSubSection2)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof AssessmentSubSection1.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection2, {
            title: 'NewAssessmentSubSection2InAssessmentSubSection1',
            exclude: ['id'],
            optional: ['assessmentSubSection1Id']
          }),
        },
      },
    }) assessmentSubSection2: Omit<AssessmentSubSection2, 'id'>,
  ): Promise<AssessmentSubSection2> {
    return this.assessmentSubSection1Repository.assessmentSubSection2s(id).create(assessmentSubSection2);
  }

  @patch('/assessment-sub-section1s/{id}/assessment-sub-section2s', {
    responses: {
      '200': {
        description: 'AssessmentSubSection1.AssessmentSubSection2 PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection2, {partial: true}),
        },
      },
    })
    assessmentSubSection2: Partial<AssessmentSubSection2>,
    @param.query.object('where', getWhereSchemaFor(AssessmentSubSection2)) where?: Where<AssessmentSubSection2>,
  ): Promise<Count> {
    return this.assessmentSubSection1Repository.assessmentSubSection2s(id).patch(assessmentSubSection2, where);
  }

  @del('/assessment-sub-section1s/{id}/assessment-sub-section2s', {
    responses: {
      '200': {
        description: 'AssessmentSubSection1.AssessmentSubSection2 DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(AssessmentSubSection2)) where?: Where<AssessmentSubSection2>,
  ): Promise<Count> {
    return this.assessmentSubSection1Repository.assessmentSubSection2s(id).delete(where);
  }
}
