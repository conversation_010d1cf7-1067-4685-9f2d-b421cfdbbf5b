import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Certification,
  CertificationLevel,
} from '../models';
import {CertificationRepository} from '../repositories';

export class CertificationCertificationLevelController {
  constructor(
    @repository(CertificationRepository) protected certificationRepository: CertificationRepository,
  ) { }

  @get('/certifications/{id}/certification-levels', {
    responses: {
      '200': {
        description: 'Array of Certification has many CertificationLevel',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(CertificationLevel)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<CertificationLevel>,
  ): Promise<CertificationLevel[]> {
    return this.certificationRepository.certificationLevels(id).find(filter);
  }

  @post('/certifications/{id}/certification-levels', {
    responses: {
      '200': {
        description: 'Certification model instance',
        content: {'application/json': {schema: getModelSchemaRef(CertificationLevel)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Certification.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertificationLevel, {
            title: 'NewCertificationLevelInCertification',
            exclude: ['id'],
            optional: ['certificationId']
          }),
        },
      },
    }) certificationLevel: Omit<CertificationLevel, 'id'>,
  ): Promise<CertificationLevel> {
    return this.certificationRepository.certificationLevels(id).create(certificationLevel);
  }

  @patch('/certifications/{id}/certification-levels', {
    responses: {
      '200': {
        description: 'Certification.CertificationLevel PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertificationLevel, {partial: true}),
        },
      },
    })
    certificationLevel: Partial<CertificationLevel>,
    @param.query.object('where', getWhereSchemaFor(CertificationLevel)) where?: Where<CertificationLevel>,
  ): Promise<Count> {
    return this.certificationRepository.certificationLevels(id).patch(certificationLevel, where);
  }

  @del('/certifications/{id}/certification-levels', {
    responses: {
      '200': {
        description: 'Certification.CertificationLevel DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(CertificationLevel)) where?: Where<CertificationLevel>,
  ): Promise<Count> {
    return this.certificationRepository.certificationLevels(id).delete(where);
  }
}
