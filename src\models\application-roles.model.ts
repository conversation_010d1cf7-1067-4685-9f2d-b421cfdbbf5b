import {Entity, model, property} from '@loopback/repository';

@model()
export class ApplicationRoles extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'array',
    itemType: 'number',
  })
  client_ids?: number[];

  @property({
    type: 'boolean',
  })
  status?: boolean;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'any',
  })
  category?: any;

  @property({
    type: 'array',
    itemType: 'number'
  })
  visibilityLevel?: number[];

  @property({
    type: 'array',
    itemType: 'string'
  })
  applicableUserRole?: string[];

  @property({
    type: 'array',
    itemType: 'string'
  })
  applicableAccessRole?: string[];

  @property({
    type: 'any',
  })
  categoryOrder?: any;

  @property({
    type: 'any',
  })
  roleOrder?: any;

  @property({
    type: 'number',
  })
  applicationListId?: number;

  constructor(data?: Partial<ApplicationRoles>) {
    super(data);
  }
}

export interface ApplicationRolesRelations {
  // describe navigational properties here
}

export type ApplicationRolesWithRelations = ApplicationRoles & ApplicationRolesRelations;
