import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignQlEntityUser,
  QCategory,
} from '../models';
import {AssignQlEntityUserRepository} from '../repositories';

export class AssignQlEntityUserQCategoryController {
  constructor(
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
  ) { }

  @get('/assign-ql-entity-users/{id}/q-category', {
    responses: {
      '200': {
        description: 'QCategory belonging to AssignQlEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(QCategory),
          },
        },
      },
    },
  })
  async getQCategory(
    @param.path.number('id') id: typeof AssignQlEntityUser.prototype.id,
  ): Promise<QCategory> {
    return this.assignQlEntityUserRepository.qCategory(id);
  }
}
