import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  NewsCirculation,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileNewsCirculationController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/news-circulations', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many NewsCirculation',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewsCirculation)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewsCirculation>,
  ): Promise<NewsCirculation[]> {
    return this.userProfileRepository.newsCirculations(id).find(filter);
  }

  @post('/user-profiles/{id}/news-circulations', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewsCirculation)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewsCirculation, {
            title: 'NewNewsCirculationInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) newsCirculation: Omit<NewsCirculation, 'id'>,
  ): Promise<NewsCirculation> {
    return this.userProfileRepository.newsCirculations(id).create(newsCirculation);
  }

  @patch('/user-profiles/{id}/news-circulations', {
    responses: {
      '200': {
        description: 'UserProfile.NewsCirculation PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewsCirculation, {partial: true}),
        },
      },
    })
    newsCirculation: Partial<NewsCirculation>,
    @param.query.object('where', getWhereSchemaFor(NewsCirculation)) where?: Where<NewsCirculation>,
  ): Promise<Count> {
    return this.userProfileRepository.newsCirculations(id).patch(newsCirculation, where);
  }

  @del('/user-profiles/{id}/news-circulations', {
    responses: {
      '200': {
        description: 'UserProfile.NewsCirculation DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(NewsCirculation)) where?: Where<NewsCirculation>,
  ): Promise<Count> {
    return this.userProfileRepository.newsCirculations(id).delete(where);
  }
}
