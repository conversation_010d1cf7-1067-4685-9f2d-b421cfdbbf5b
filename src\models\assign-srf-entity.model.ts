import {Entity, belongsTo, model, property} from '@loopback/repository';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';

@model()
export class AssignSrfEntity extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'any',
  })
  comments?: any;

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier0_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier1_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier2_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier3_ids?: number[];


  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => ConsolidateFormCollection, {name: 'srf'})
  srfId: number;

  constructor(data?: Partial<AssignSrfEntity>) {
    super(data);
  }
}

export interface AssignSrfEntityRelations {
  // describe navigational properties here
}

export type AssignSrfEntityWithRelations = AssignSrfEntity & AssignSrfEntityRelations;
