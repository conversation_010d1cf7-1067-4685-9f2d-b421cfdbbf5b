import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {FormCollection, QuantitativeSubmission, QuantitativeSubmissionRelations} from '../models';
import {FormCollectionRepository} from './form-collection.repository';

export class QuantitativeSubmissionRepository extends DefaultCrudRepository<
  QuantitativeSubmission,
  typeof QuantitativeSubmission.prototype.id,
  QuantitativeSubmissionRelations
> {

  public readonly dcf: BelongsToAccessor<FormCollection, typeof QuantitativeSubmission.prototype.id>;


  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('FormCollectionRepository') protected formCollectionRepositoryGetter: Getter<FormCollectionRepository>,
  ) {
    super(QuantitativeSubmission, dataSource);

    this.dcf = this.createBelongsToAccessorFor('dcf', formCollectionRepositoryGetter,);
    this.registerInclusionResolver('dcf', this.dcf.inclusionResolver);
  }

}
