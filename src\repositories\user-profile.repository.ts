import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, repository, HasOneRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDcfClient, AssignDcfEntity, AssignDcfEntityUser, AssignDcfSuppliers, AssignDcfUser, AssignDcfUserNew, AssignDfEntity, AssignDfEntityUser, AssignDfUser, AssignRfEntity, AssignRfUsers, AssignSrfEntity, AssignSrfEntityUser, AssignSrfUser, ClientEfCategoryAssignment, ClientInitiative, DpReport, DpReportNew, Frequency, IndicatorApproverAssignment, LocationOne, NewClientCertification, NewGoals, PolicyProcedure, QlListingFilter, QualitativeApproval, QualitativeSubmission, QuantitativeDpReport, QuantitativeSubmission, SubmitCf, SubmitDcf, SubmitDcfNew, SubmitRf, SubmitRfNew, SupplierAssessmentAssignment, UserProfile, UserProfileRelations, QnIndicatorApproval, DealerResponseForm, Helper, DealerAssessmentAssignment, IndicatorSection, SapCollection, SapResponse, ClientEfCategoryMapping, StructuredResponse, DealerChecklistSubmission, VendorCode, ValueChainSubmission, Ticketing, NewsCirculation, AssignQlEntity, AssignQlEntityUser, DeleteUserLog, ComputedIndicator} from '../models';
import {AssignDcfClientRepository} from './assign-dcf-client.repository';
import {AssignDcfEntityUserRepository} from './assign-dcf-entity-user.repository';
import {AssignDcfEntityRepository} from './assign-dcf-entity.repository';
import {AssignDcfSuppliersRepository} from './assign-dcf-suppliers.repository';
import {AssignDcfUserNewRepository} from './assign-dcf-user-new.repository';
import {AssignDcfUserRepository} from './assign-dcf-user.repository';
import {AssignDfEntityUserRepository} from './assign-df-entity-user.repository';
import {AssignDfEntityRepository} from './assign-df-entity.repository';
import {AssignDfUserRepository} from './assign-df-user.repository';
import {AssignRfEntityRepository} from './assign-rf-entity.repository';
import {AssignRfUsersRepository} from './assign-rf-users.repository';
import {AssignSrfEntityUserRepository} from './assign-srf-entity-user.repository';
import {AssignSrfEntityRepository} from './assign-srf-entity.repository';
import {AssignSrfUserRepository} from './assign-srf-user.repository';
import {ClientEfCategoryAssignmentRepository} from './client-ef-category-assignment.repository';
import {ClientInitiativeRepository} from './client-initiative.repository';
import {DpReportNewRepository} from './dp-report-new.repository';
import {DpReportRepository} from './dp-report.repository';
import {FrequencyRepository} from './frequency.repository';
import {IndicatorApproverAssignmentRepository} from './indicator-approver-assignment.repository';
import {LocationOneRepository} from './location-one.repository';
import {NewClientCertificationRepository} from './new-client-certification.repository';
import {NewGoalsRepository} from './new-goals.repository';
import {PolicyProcedureRepository} from './policy-procedure.repository';
import {QlListingFilterRepository} from './ql-listing-filter.repository';
import {QualitativeApprovalRepository} from './qualitative-approval.repository';
import {QualitativeSubmissionRepository} from './qualitative-submission.repository';
import {QuantitativeDpReportRepository} from './quantitative-dp-report.repository';
import {QuantitativeSubmissionRepository} from './quantitative-submission.repository';
import {SubmitCfRepository} from './submit-cf.repository';
import {SubmitDcfNewRepository} from './submit-dcf-new.repository';
import {SubmitDcfRepository} from './submit-dcf.repository';
import {SubmitRfNewRepository} from './submit-rf-new.repository';
import {SubmitRfRepository} from './submit-rf.repository';
import {SupplierAssessmentAssignmentRepository} from './supplier-assessment-assignment.repository';
import {QnIndicatorApprovalRepository} from './qn-indicator-approval.repository';
import {DealerResponseFormRepository} from './dealer-response-form.repository';
import {HelperRepository} from './helper.repository';
import {DealerAssessmentAssignmentRepository} from './dealer-assessment-assignment.repository';
import {IndicatorSectionRepository} from './indicator-section.repository';
import {SapCollectionRepository} from './sap-collection.repository';
import {SapResponseRepository} from './sap-response.repository';
import {ClientEfCategoryMappingRepository} from './client-ef-category-mapping.repository';
import {StructuredResponseRepository} from './structured-response.repository';
import {DealerChecklistSubmissionRepository} from './dealer-checklist-submission.repository';
import {VendorCodeRepository} from './vendor-code.repository';
import {ValueChainSubmissionRepository} from './value-chain-submission.repository';
import {TicketingRepository} from './ticketing.repository';
import {NewsCirculationRepository} from './news-circulation.repository';
import {AssignQlEntityRepository} from './assign-ql-entity.repository';
import {AssignQlEntityUserRepository} from './assign-ql-entity-user.repository';
import {DeleteUserLogRepository} from './delete-user-log.repository';
import {ComputedIndicatorRepository} from './computed-indicator.repository';

export class UserProfileRepository extends DefaultCrudRepository<
  UserProfile,
  typeof UserProfile.prototype.id,
  UserProfileRelations
> {

  public readonly locationOnes: HasManyRepositoryFactory<LocationOne, typeof UserProfile.prototype.id>;

  public readonly frequencies: HasManyRepositoryFactory<Frequency, typeof UserProfile.prototype.id>;

  public readonly assignDcfClients: HasManyRepositoryFactory<AssignDcfClient, typeof UserProfile.prototype.id>;

  public readonly newGoals: HasManyRepositoryFactory<NewGoals, typeof UserProfile.prototype.id>;

  public readonly assignDcfUsers: HasManyRepositoryFactory<AssignDcfUser, typeof UserProfile.prototype.id>;

  public readonly assignDcfSuppliers: HasManyRepositoryFactory<AssignDcfSuppliers, typeof UserProfile.prototype.id>;

  public readonly submitDcfs: HasManyRepositoryFactory<SubmitDcf, typeof UserProfile.prototype.id>;

  public readonly dpReports: HasManyRepositoryFactory<DpReport, typeof UserProfile.prototype.id>;

  public readonly submitRfs: HasManyRepositoryFactory<SubmitRf, typeof UserProfile.prototype.id>;

  public readonly qlListingFilters: HasManyRepositoryFactory<QlListingFilter, typeof UserProfile.prototype.id>;

  public readonly assignRfUsers: HasManyRepositoryFactory<AssignRfUsers, typeof UserProfile.prototype.id>;

  public readonly submitCfs: HasManyRepositoryFactory<SubmitCf, typeof UserProfile.prototype.id>;

  public readonly assignDcfUserNews: HasManyRepositoryFactory<AssignDcfUserNew, typeof UserProfile.prototype.id>;

  public readonly submitRfNews: HasManyRepositoryFactory<SubmitRfNew, typeof UserProfile.prototype.id>;

  public readonly submitDcfNews: HasManyRepositoryFactory<SubmitDcfNew, typeof UserProfile.prototype.id>;

  public readonly dpReportNews: HasManyRepositoryFactory<DpReportNew, typeof UserProfile.prototype.id>;

  public readonly assignDfUsers: HasManyRepositoryFactory<AssignDfUser, typeof UserProfile.prototype.id>;

  public readonly assignSrfUsers: HasManyRepositoryFactory<AssignSrfUser, typeof UserProfile.prototype.id>;

  public readonly assignRfEntities: HasManyRepositoryFactory<AssignRfEntity, typeof UserProfile.prototype.id>;

  public readonly assignDcfEntities: HasManyRepositoryFactory<AssignDcfEntity, typeof UserProfile.prototype.id>;

  public readonly assignDfEntities: HasManyRepositoryFactory<AssignDfEntity, typeof UserProfile.prototype.id>;

  public readonly assignSrfEntities: HasManyRepositoryFactory<AssignSrfEntity, typeof UserProfile.prototype.id>;

  public readonly assignDcfEntityUsers: HasManyRepositoryFactory<AssignDcfEntityUser, typeof UserProfile.prototype.id>;

  public readonly assignDfEntityUsers: HasManyRepositoryFactory<AssignDfEntityUser, typeof UserProfile.prototype.id>;

  public readonly assignSrfEntityUsers: HasManyRepositoryFactory<AssignSrfEntityUser, typeof UserProfile.prototype.id>;

  public readonly quantitativeSubmissions: HasManyRepositoryFactory<QuantitativeSubmission, typeof UserProfile.prototype.id>;

  public readonly clientEfCategoryAssignments: HasManyRepositoryFactory<ClientEfCategoryAssignment, typeof UserProfile.prototype.id>;

  public readonly clientInitiatives: HasManyRepositoryFactory<ClientInitiative, typeof UserProfile.prototype.id>;

  public readonly quantitativeDpReports: HasManyRepositoryFactory<QuantitativeDpReport, typeof UserProfile.prototype.id>;

  public readonly newClientCertifications: HasManyRepositoryFactory<NewClientCertification, typeof UserProfile.prototype.id>;

  public readonly qualitativeSubmissions: HasManyRepositoryFactory<QualitativeSubmission, typeof UserProfile.prototype.id>;

  public readonly indicatorApproverAssignments: HasManyRepositoryFactory<IndicatorApproverAssignment, typeof UserProfile.prototype.id>;

  public readonly supplierAssessmentAssignments: HasManyRepositoryFactory<SupplierAssessmentAssignment, typeof UserProfile.prototype.id>;

  public readonly policyProcedures: HasManyRepositoryFactory<PolicyProcedure, typeof UserProfile.prototype.id>;

  public readonly qualitativeApprovals: HasManyRepositoryFactory<QualitativeApproval, typeof UserProfile.prototype.id>;

  public readonly qnIndicatorApprovals: HasManyRepositoryFactory<QnIndicatorApproval, typeof UserProfile.prototype.id>;

  public readonly dealerResponseForms: HasManyRepositoryFactory<DealerResponseForm, typeof UserProfile.prototype.id>;

  public readonly helper: HasOneRepositoryFactory<Helper, typeof UserProfile.prototype.id>;

  public readonly dealerAssessmentAssignments: HasManyRepositoryFactory<DealerAssessmentAssignment, typeof UserProfile.prototype.id>;

  public readonly indicatorSections: HasManyRepositoryFactory<IndicatorSection, typeof UserProfile.prototype.id>;

  public readonly sapCollections: HasManyRepositoryFactory<SapCollection, typeof UserProfile.prototype.id>;

  public readonly sapResponses: HasManyRepositoryFactory<SapResponse, typeof UserProfile.prototype.id>;

  public readonly clientEfCategoryMappings: HasManyRepositoryFactory<ClientEfCategoryMapping, typeof UserProfile.prototype.id>;

  public readonly structuredResponses: HasManyRepositoryFactory<StructuredResponse, typeof UserProfile.prototype.id>;

  public readonly dealerChecklistSubmissions: HasManyRepositoryFactory<DealerChecklistSubmission, typeof UserProfile.prototype.id>;

  public readonly vendorCodes: HasManyRepositoryFactory<VendorCode, typeof UserProfile.prototype.id>;

  public readonly valueChainSubmissions: HasManyRepositoryFactory<ValueChainSubmission, typeof UserProfile.prototype.id>;

  public readonly ticketings: HasManyRepositoryFactory<Ticketing, typeof UserProfile.prototype.id>;

  public readonly newsCirculations: HasManyRepositoryFactory<NewsCirculation, typeof UserProfile.prototype.id>;

  public readonly assignQlEntities: HasManyRepositoryFactory<AssignQlEntity, typeof UserProfile.prototype.id>;

  public readonly assignQlEntityUsers: HasManyRepositoryFactory<AssignQlEntityUser, typeof UserProfile.prototype.id>;

  public readonly deleteUserLogs: HasManyRepositoryFactory<DeleteUserLog, typeof UserProfile.prototype.id>;

  public readonly computedIndicators: HasManyRepositoryFactory<ComputedIndicator, typeof UserProfile.prototype.id>;
  // public readonly assignRfUsers: HasManyRepositoryFactory<AssignRfUsers, typeof UserProfile.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('FrequencyRepository') protected frequencyRepositoryGetter: Getter<FrequencyRepository>, @repository.getter('AssignDcfClientRepository') protected assignDcfClientRepositoryGetter: Getter<AssignDcfClientRepository>, @repository.getter('NewGoalsRepository') protected newGoalsRepositoryGetter: Getter<NewGoalsRepository>, @repository.getter('AssignDcfUserRepository') protected assignDcfUserRepositoryGetter: Getter<AssignDcfUserRepository>, @repository.getter('AssignDcfSuppliersRepository') protected assignDcfSuppliersRepositoryGetter: Getter<AssignDcfSuppliersRepository>, @repository.getter('SubmitDcfRepository') protected submitDcfRepositoryGetter: Getter<SubmitDcfRepository>, @repository.getter('DpReportRepository') protected dpReportRepositoryGetter: Getter<DpReportRepository>, @repository.getter('SubmitRfRepository') protected submitRfRepositoryGetter: Getter<SubmitRfRepository>, @repository.getter('QlListingFilterRepository') protected qlListingFilterRepositoryGetter: Getter<QlListingFilterRepository>, @repository.getter('AssignRfUsersRepository') protected assignRfUsersRepositoryGetter: Getter<AssignRfUsersRepository>, @repository.getter('SubmitCfRepository') protected submitCfRepositoryGetter: Getter<SubmitCfRepository>, @repository.getter('AssignDcfUserNewRepository') protected assignDcfUserNewRepositoryGetter: Getter<AssignDcfUserNewRepository>, @repository.getter('SubmitRfNewRepository') protected submitRfNewRepositoryGetter: Getter<SubmitRfNewRepository>, @repository.getter('SubmitDcfNewRepository') protected submitDcfNewRepositoryGetter: Getter<SubmitDcfNewRepository>, @repository.getter('DpReportNewRepository') protected dpReportNewRepositoryGetter: Getter<DpReportNewRepository>, @repository.getter('AssignDfUserRepository') protected assignDfUserRepositoryGetter: Getter<AssignDfUserRepository>, @repository.getter('AssignSrfUserRepository') protected assignSrfUserRepositoryGetter: Getter<AssignSrfUserRepository>, @repository.getter('AssignRfEntityRepository') protected assignRfEntityRepositoryGetter: Getter<AssignRfEntityRepository>, @repository.getter('AssignDcfEntityRepository') protected assignDcfEntityRepositoryGetter: Getter<AssignDcfEntityRepository>, @repository.getter('AssignDfEntityRepository') protected assignDfEntityRepositoryGetter: Getter<AssignDfEntityRepository>, @repository.getter('AssignSrfEntityRepository') protected assignSrfEntityRepositoryGetter: Getter<AssignSrfEntityRepository>, @repository.getter('AssignDcfEntityUserRepository') protected assignDcfEntityUserRepositoryGetter: Getter<AssignDcfEntityUserRepository>, @repository.getter('AssignDfEntityUserRepository') protected assignDfEntityUserRepositoryGetter: Getter<AssignDfEntityUserRepository>, @repository.getter('AssignSrfEntityUserRepository') protected assignSrfEntityUserRepositoryGetter: Getter<AssignSrfEntityUserRepository>, @repository.getter('QuantitativeSubmissionRepository') protected quantitativeSubmissionRepositoryGetter: Getter<QuantitativeSubmissionRepository>, @repository.getter('ClientEfCategoryAssignmentRepository') protected clientEfCategoryAssignmentRepositoryGetter: Getter<ClientEfCategoryAssignmentRepository>, @repository.getter('ClientInitiativeRepository') protected clientInitiativeRepositoryGetter: Getter<ClientInitiativeRepository>, @repository.getter('QuantitativeDpReportRepository') protected quantitativeDpReportRepositoryGetter: Getter<QuantitativeDpReportRepository>, @repository.getter('NewClientCertificationRepository') protected newClientCertificationRepositoryGetter: Getter<NewClientCertificationRepository>, @repository.getter('QualitativeSubmissionRepository') protected qualitativeSubmissionRepositoryGetter: Getter<QualitativeSubmissionRepository>, @repository.getter('IndicatorApproverAssignmentRepository') protected indicatorApproverAssignmentRepositoryGetter: Getter<IndicatorApproverAssignmentRepository>, @repository.getter('SupplierAssessmentAssignmentRepository') protected supplierAssessmentAssignmentRepositoryGetter: Getter<SupplierAssessmentAssignmentRepository>, @repository.getter('PolicyProcedureRepository') protected policyProcedureRepositoryGetter: Getter<PolicyProcedureRepository>, @repository.getter('QualitativeApprovalRepository') protected qualitativeApprovalRepositoryGetter: Getter<QualitativeApprovalRepository>, @repository.getter('QnIndicatorApprovalRepository') protected qnIndicatorApprovalRepositoryGetter: Getter<QnIndicatorApprovalRepository>, @repository.getter('DealerResponseFormRepository') protected dealerResponseFormRepositoryGetter: Getter<DealerResponseFormRepository>, @repository.getter('HelperRepository') protected helperRepositoryGetter: Getter<HelperRepository>, @repository.getter('DealerAssessmentAssignmentRepository') protected dealerAssessmentAssignmentRepositoryGetter: Getter<DealerAssessmentAssignmentRepository>, @repository.getter('IndicatorSectionRepository') protected indicatorSectionRepositoryGetter: Getter<IndicatorSectionRepository>, @repository.getter('SapCollectionRepository') protected sapCollectionRepositoryGetter: Getter<SapCollectionRepository>, @repository.getter('SapResponseRepository') protected sapResponseRepositoryGetter: Getter<SapResponseRepository>, @repository.getter('ClientEfCategoryMappingRepository') protected clientEfCategoryMappingRepositoryGetter: Getter<ClientEfCategoryMappingRepository>, @repository.getter('StructuredResponseRepository') protected structuredResponseRepositoryGetter: Getter<StructuredResponseRepository>, @repository.getter('DealerChecklistSubmissionRepository') protected dealerChecklistSubmissionRepositoryGetter: Getter<DealerChecklistSubmissionRepository>, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>, @repository.getter('ValueChainSubmissionRepository') protected valueChainSubmissionRepositoryGetter: Getter<ValueChainSubmissionRepository>, @repository.getter('TicketingRepository') protected ticketingRepositoryGetter: Getter<TicketingRepository>, @repository.getter('NewsCirculationRepository') protected newsCirculationRepositoryGetter: Getter<NewsCirculationRepository>, @repository.getter('AssignQlEntityRepository') protected assignQlEntityRepositoryGetter: Getter<AssignQlEntityRepository>, @repository.getter('AssignQlEntityUserRepository') protected assignQlEntityUserRepositoryGetter: Getter<AssignQlEntityUserRepository>, @repository.getter('DeleteUserLogRepository') protected deleteUserLogRepositoryGetter: Getter<DeleteUserLogRepository>, @repository.getter('ComputedIndicatorRepository') protected computedIndicatorRepositoryGetter: Getter<ComputedIndicatorRepository>,
  ) {
    super(UserProfile, dataSource);
    this.computedIndicators = this.createHasManyRepositoryFactoryFor('computedIndicators', computedIndicatorRepositoryGetter,);
    this.registerInclusionResolver('computedIndicators', this.computedIndicators.inclusionResolver);
    this.deleteUserLogs = this.createHasManyRepositoryFactoryFor('deleteUserLogs', deleteUserLogRepositoryGetter,);
    this.registerInclusionResolver('deleteUserLogs', this.deleteUserLogs.inclusionResolver);
    this.assignQlEntityUsers = this.createHasManyRepositoryFactoryFor('assignQlEntityUsers', assignQlEntityUserRepositoryGetter,);
    this.registerInclusionResolver('assignQlEntityUsers', this.assignQlEntityUsers.inclusionResolver);
    this.assignQlEntities = this.createHasManyRepositoryFactoryFor('assignQlEntities', assignQlEntityRepositoryGetter,);
    this.registerInclusionResolver('assignQlEntities', this.assignQlEntities.inclusionResolver);
    this.newsCirculations = this.createHasManyRepositoryFactoryFor('newsCirculations', newsCirculationRepositoryGetter,);
    this.registerInclusionResolver('newsCirculations', this.newsCirculations.inclusionResolver);
    this.ticketings = this.createHasManyRepositoryFactoryFor('ticketings', ticketingRepositoryGetter,);
    this.registerInclusionResolver('ticketings', this.ticketings.inclusionResolver);
    this.valueChainSubmissions = this.createHasManyRepositoryFactoryFor('valueChainSubmissions', valueChainSubmissionRepositoryGetter,);
    this.registerInclusionResolver('valueChainSubmissions', this.valueChainSubmissions.inclusionResolver);
    this.vendorCodes = this.createHasManyRepositoryFactoryFor('vendorCodes', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendorCodes', this.vendorCodes.inclusionResolver);
    this.dealerChecklistSubmissions = this.createHasManyRepositoryFactoryFor('dealerChecklistSubmissions', dealerChecklistSubmissionRepositoryGetter,);
    this.registerInclusionResolver('dealerChecklistSubmissions', this.dealerChecklistSubmissions.inclusionResolver);
    this.structuredResponses = this.createHasManyRepositoryFactoryFor('structuredResponses', structuredResponseRepositoryGetter,);
    this.registerInclusionResolver('structuredResponses', this.structuredResponses.inclusionResolver);
    this.clientEfCategoryMappings = this.createHasManyRepositoryFactoryFor('clientEfCategoryMappings', clientEfCategoryMappingRepositoryGetter,);
    this.registerInclusionResolver('clientEfCategoryMappings', this.clientEfCategoryMappings.inclusionResolver);
    this.sapResponses = this.createHasManyRepositoryFactoryFor('sapResponses', sapResponseRepositoryGetter,);
    this.registerInclusionResolver('sapResponses', this.sapResponses.inclusionResolver);
    this.sapCollections = this.createHasManyRepositoryFactoryFor('sapCollections', sapCollectionRepositoryGetter,);
    this.registerInclusionResolver('sapCollections', this.sapCollections.inclusionResolver);
    this.indicatorSections = this.createHasManyRepositoryFactoryFor('indicatorSections', indicatorSectionRepositoryGetter,);
    this.registerInclusionResolver('indicatorSections', this.indicatorSections.inclusionResolver);
    this.dealerAssessmentAssignments = this.createHasManyRepositoryFactoryFor('dealerAssessmentAssignments', dealerAssessmentAssignmentRepositoryGetter,);
    this.registerInclusionResolver('dealerAssessmentAssignments', this.dealerAssessmentAssignments.inclusionResolver);
    this.helper = this.createHasOneRepositoryFactoryFor('helper', helperRepositoryGetter);
    this.registerInclusionResolver('helper', this.helper.inclusionResolver);
    this.dealerResponseForms = this.createHasManyRepositoryFactoryFor('dealerResponseForms', dealerResponseFormRepositoryGetter,);
    this.registerInclusionResolver('dealerResponseForms', this.dealerResponseForms.inclusionResolver);
    this.qnIndicatorApprovals = this.createHasManyRepositoryFactoryFor('qnIndicatorApprovals', qnIndicatorApprovalRepositoryGetter,);
    this.registerInclusionResolver('qnIndicatorApprovals', this.qnIndicatorApprovals.inclusionResolver);

    this.qualitativeApprovals = this.createHasManyRepositoryFactoryFor('qualitativeApprovals', qualitativeApprovalRepositoryGetter,);
    this.registerInclusionResolver('qualitativeApprovals', this.qualitativeApprovals.inclusionResolver);
    this.policyProcedures = this.createHasManyRepositoryFactoryFor('policyProcedures', policyProcedureRepositoryGetter,);
    this.registerInclusionResolver('policyProcedures', this.policyProcedures.inclusionResolver);
    this.supplierAssessmentAssignments = this.createHasManyRepositoryFactoryFor('supplierAssessmentAssignments', supplierAssessmentAssignmentRepositoryGetter,);
    this.registerInclusionResolver('supplierAssessmentAssignments', this.supplierAssessmentAssignments.inclusionResolver);
    this.indicatorApproverAssignments = this.createHasManyRepositoryFactoryFor('indicatorApproverAssignments', indicatorApproverAssignmentRepositoryGetter,);
    this.registerInclusionResolver('indicatorApproverAssignments', this.indicatorApproverAssignments.inclusionResolver);
    this.qualitativeSubmissions = this.createHasManyRepositoryFactoryFor('qualitativeSubmissions', qualitativeSubmissionRepositoryGetter,);
    this.registerInclusionResolver('qualitativeSubmissions', this.qualitativeSubmissions.inclusionResolver);
    this.newClientCertifications = this.createHasManyRepositoryFactoryFor('newClientCertifications', newClientCertificationRepositoryGetter,);
    this.registerInclusionResolver('newClientCertifications', this.newClientCertifications.inclusionResolver);
    this.quantitativeDpReports = this.createHasManyRepositoryFactoryFor('quantitativeDpReports', quantitativeDpReportRepositoryGetter,);
    this.registerInclusionResolver('quantitativeDpReports', this.quantitativeDpReports.inclusionResolver);
    this.clientInitiatives = this.createHasManyRepositoryFactoryFor('clientInitiatives', clientInitiativeRepositoryGetter,);
    this.registerInclusionResolver('clientInitiatives', this.clientInitiatives.inclusionResolver);
    this.clientEfCategoryAssignments = this.createHasManyRepositoryFactoryFor('clientEfCategoryAssignments', clientEfCategoryAssignmentRepositoryGetter,);
    this.registerInclusionResolver('clientEfCategoryAssignments', this.clientEfCategoryAssignments.inclusionResolver);
    this.quantitativeSubmissions = this.createHasManyRepositoryFactoryFor('quantitativeSubmissions', quantitativeSubmissionRepositoryGetter,);
    this.registerInclusionResolver('quantitativeSubmissions', this.quantitativeSubmissions.inclusionResolver);
    this.assignSrfEntityUsers = this.createHasManyRepositoryFactoryFor('assignSrfEntityUsers', assignSrfEntityUserRepositoryGetter,);
    this.registerInclusionResolver('assignSrfEntityUsers', this.assignSrfEntityUsers.inclusionResolver);
    this.assignDfEntityUsers = this.createHasManyRepositoryFactoryFor('assignDfEntityUsers', assignDfEntityUserRepositoryGetter,);
    this.registerInclusionResolver('assignDfEntityUsers', this.assignDfEntityUsers.inclusionResolver);
    this.assignDcfEntityUsers = this.createHasManyRepositoryFactoryFor('assignDcfEntityUsers', assignDcfEntityUserRepositoryGetter,);
    this.registerInclusionResolver('assignDcfEntityUsers', this.assignDcfEntityUsers.inclusionResolver);
    this.assignSrfEntities = this.createHasManyRepositoryFactoryFor('assignSrfEntities', assignSrfEntityRepositoryGetter,);
    this.registerInclusionResolver('assignSrfEntities', this.assignSrfEntities.inclusionResolver);
    this.assignDfEntities = this.createHasManyRepositoryFactoryFor('assignDfEntities', assignDfEntityRepositoryGetter,);
    this.registerInclusionResolver('assignDfEntities', this.assignDfEntities.inclusionResolver);
    this.assignDcfEntities = this.createHasManyRepositoryFactoryFor('assignDcfEntities', assignDcfEntityRepositoryGetter,);
    this.registerInclusionResolver('assignDcfEntities', this.assignDcfEntities.inclusionResolver);
    this.assignRfEntities = this.createHasManyRepositoryFactoryFor('assignRfEntities', assignRfEntityRepositoryGetter,);
    this.registerInclusionResolver('assignRfEntities', this.assignRfEntities.inclusionResolver);
    this.assignSrfUsers = this.createHasManyRepositoryFactoryFor('assignSrfUsers', assignSrfUserRepositoryGetter,);
    this.registerInclusionResolver('assignSrfUsers', this.assignSrfUsers.inclusionResolver);
    this.assignDfUsers = this.createHasManyRepositoryFactoryFor('assignDfUsers', assignDfUserRepositoryGetter,);
    this.registerInclusionResolver('assignDfUsers', this.assignDfUsers.inclusionResolver);
    this.dpReportNews = this.createHasManyRepositoryFactoryFor('dpReportNews', dpReportNewRepositoryGetter,);
    this.registerInclusionResolver('dpReportNews', this.dpReportNews.inclusionResolver);
    this.submitDcfNews = this.createHasManyRepositoryFactoryFor('submitDcfNews', submitDcfNewRepositoryGetter,);
    this.registerInclusionResolver('submitDcfNews', this.submitDcfNews.inclusionResolver);
    this.submitRfNews = this.createHasManyRepositoryFactoryFor('submitRfNews', submitRfNewRepositoryGetter,);
    this.registerInclusionResolver('submitRfNews', this.submitRfNews.inclusionResolver);
    this.assignDcfUserNews = this.createHasManyRepositoryFactoryFor('assignDcfUserNews', assignDcfUserNewRepositoryGetter,);
    this.registerInclusionResolver('assignDcfUserNews', this.assignDcfUserNews.inclusionResolver);
    this.submitCfs = this.createHasManyRepositoryFactoryFor('submitCfs', submitCfRepositoryGetter,);
    this.registerInclusionResolver('submitCfs', this.submitCfs.inclusionResolver);
    this.assignRfUsers = this.createHasManyRepositoryFactoryFor('assignRfUsers', assignRfUsersRepositoryGetter,);
    this.registerInclusionResolver('assignRfUsers', this.assignRfUsers.inclusionResolver);

    this.qlListingFilters = this.createHasManyRepositoryFactoryFor('qlListingFilters', qlListingFilterRepositoryGetter,);
    this.registerInclusionResolver('qlListingFilters', this.qlListingFilters.inclusionResolver);
    this.submitRfs = this.createHasManyRepositoryFactoryFor('submitRfs', submitRfRepositoryGetter,);
    this.registerInclusionResolver('submitRfs', this.submitRfs.inclusionResolver);
    this.dpReports = this.createHasManyRepositoryFactoryFor('dpReports', dpReportRepositoryGetter,);
    this.registerInclusionResolver('dpReports', this.dpReports.inclusionResolver);
    this.submitDcfs = this.createHasManyRepositoryFactoryFor('submitDcfs', submitDcfRepositoryGetter,);
    this.registerInclusionResolver('submitDcfs', this.submitDcfs.inclusionResolver);
    this.assignDcfSuppliers = this.createHasManyRepositoryFactoryFor('assignDcfSuppliers', assignDcfSuppliersRepositoryGetter,);
    this.registerInclusionResolver('assignDcfSuppliers', this.assignDcfSuppliers.inclusionResolver);
    this.assignDcfUsers = this.createHasManyRepositoryFactoryFor('assignDcfUsers', assignDcfUserRepositoryGetter,);
    this.registerInclusionResolver('assignDcfUsers', this.assignDcfUsers.inclusionResolver);

    this.newGoals = this.createHasManyRepositoryFactoryFor('newGoals', newGoalsRepositoryGetter,);
    this.registerInclusionResolver('newGoals', this.newGoals.inclusionResolver);
    this.assignDcfClients = this.createHasManyRepositoryFactoryFor('assignDcfClients', assignDcfClientRepositoryGetter,);
    this.registerInclusionResolver('assignDcfClients', this.assignDcfClients.inclusionResolver);
    this.frequencies = this.createHasManyRepositoryFactoryFor('frequencies', frequencyRepositoryGetter,);
    this.registerInclusionResolver('frequencies', this.frequencies.inclusionResolver);
    this.locationOnes = this.createHasManyRepositoryFactoryFor('locationOnes', locationOneRepositoryGetter,);
    this.registerInclusionResolver('locationOnes', this.locationOnes.inclusionResolver);
  }
}
