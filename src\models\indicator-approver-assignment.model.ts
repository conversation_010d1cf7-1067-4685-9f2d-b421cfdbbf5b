import {Entity, model, property} from '@loopback/repository';

@model()
export class IndicatorApproverAssignment extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  levelOfApproval?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  locations?: any[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  responsibility?: number[];
  @property({
    type: 'any',
  })
  accessType?: any;
  @property({
    type: 'boolean',
  })
  threshold?: boolean;

  @property({
    type: 'any',
  })
  tvalue1?: any;

  @property({
    type: 'any',
  })
  tvalue2?: any;

  @property({
    type: 'number',
  })
  indicatorId?: number;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
  })
  created_on?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
  })
  modified_on?: string | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    },
  })
  created_by?: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    },
  })
  modified_by?: number | null;



  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<IndicatorApproverAssignment>) {
    super(data);
  }
}

export interface IndicatorApproverAssignmentRelations {
  // describe navigational properties here
}

export type IndicatorApproverAssignmentWithRelations = IndicatorApproverAssignment & IndicatorApproverAssignmentRelations;
