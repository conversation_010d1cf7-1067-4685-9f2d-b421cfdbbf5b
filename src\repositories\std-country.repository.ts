import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {StdCountry, StdCountryRelations, StdYear} from '../models';
import {StdYearRepository} from './std-year.repository';

export class StdCountryRepository extends DefaultCrudRepository<
  StdCountry,
  typeof StdCountry.prototype.id,
  StdCountryRelations
> {

  public readonly stdYears: HasManyRepositoryFactory<StdYear, typeof StdCountry.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('StdYearRepository') protected stdYearRepositoryGetter: Getter<StdYearRepository>,
  ) {
    super(StdCountry, dataSource);
    this.stdYears = this.createHasManyRepositoryFactoryFor('stdYears', stdYearRepositoryGetter,);
    this.registerInclusionResolver('stdYears', this.stdYears.inclusionResolver);
  }
}
