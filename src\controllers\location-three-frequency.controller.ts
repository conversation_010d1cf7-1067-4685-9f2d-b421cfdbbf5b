import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationThree,
  Frequency,
} from '../models';
import {LocationThreeRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class LocationThreeFrequencyController {
  constructor(
    @repository(LocationThreeRepository) protected locationThreeRepository: LocationThreeRepository,
  ) { }

  @get('/location-threes/{id}/frequencies', {
    responses: {
      '200': {
        description: 'Array of LocationThree has many Frequency',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Frequency)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Frequency>,
  ): Promise<Frequency[]> {
    return this.locationThreeRepository.frequencies(id).find(filter);
  }

  @post('/location-threes/{id}/frequencies', {
    responses: {
      '200': {
        description: 'LocationThree model instance',
        content: {'application/json': {schema: getModelSchemaRef(Frequency)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof LocationThree.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Frequency, {
            title: 'NewFrequencyInLocationThree',
            exclude: ['id'],
            optional: ['locationThreeId']
          }),
        },
      },
    }) frequency: Omit<Frequency, 'id'>,
  ): Promise<Frequency> {
    return this.locationThreeRepository.frequencies(id).create(frequency);
  }

  @patch('/location-threes/{id}/frequencies', {
    responses: {
      '200': {
        description: 'LocationThree.Frequency PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Frequency, {partial: true}),
        },
      },
    })
    frequency: Partial<Frequency>,
    @param.query.object('where', getWhereSchemaFor(Frequency)) where?: Where<Frequency>,
  ): Promise<Count> {
    return this.locationThreeRepository.frequencies(id).patch(frequency, where);
  }

  @del('/location-threes/{id}/frequencies', {
    responses: {
      '200': {
        description: 'LocationThree.Frequency DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Frequency)) where?: Where<Frequency>,
  ): Promise<Count> {
    return this.locationThreeRepository.frequencies(id).delete(where);
  }
}
