import {Entity, model, property} from '@loopback/repository';

@model()
export class AdUser extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  email?: string;

  @property({
    type: 'boolean',
    default: false,
  })
  submitter?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  approver?: boolean;

  @property({
    type: 'boolean',
    default: false,
  })
  viewer?: boolean;


  constructor(data?: Partial<AdUser>) {
    super(data);
  }
}

export interface AdUserRelations {
  // describe navigational properties here
}

export type AdUserWithRelations = AdUser & AdUserRelations;
