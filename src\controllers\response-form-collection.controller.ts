import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {ResponseFormCollection} from '../models';
import {ResponseFormCollectionRepository} from '../repositories';

import {DateTime} from 'luxon';



export class ResponseFormCollectionController {
  constructor(
    @repository(ResponseFormCollectionRepository)
    public responseFormCollectionRepository: ResponseFormCollectionRepository,
  ) { }

  @post('/response-form-collections')
  @response(200, {
    description: 'ResponseFormCollection model instance',
    content: {'application/json': {schema: getModelSchemaRef(ResponseFormCollection)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ResponseFormCollection, {
            title: 'NewResponseFormCollection',
            exclude: ['id'],
          }),
        },
      },
    })
    responseFormCollection: Omit<ResponseFormCollection, 'id'>,
  ): Promise<ResponseFormCollection> {
    responseFormCollection.updated = DateTime.utc().toString()
    responseFormCollection.created = DateTime.utc().toString()
    return this.responseFormCollectionRepository.create(responseFormCollection);
  }

  @get('/response-form-collections/count')
  @response(200, {
    description: 'ResponseFormCollection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ResponseFormCollection) where?: Where<ResponseFormCollection>,
  ): Promise<Count> {
    return this.responseFormCollectionRepository.count(where);
  }

  @get('/response-form-collections')
  @response(200, {
    description: 'Array of ResponseFormCollection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ResponseFormCollection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ResponseFormCollection) filter?: Filter<ResponseFormCollection>,
  ): Promise<ResponseFormCollection[]> {
    return this.responseFormCollectionRepository.find(filter);
  }

  // @patch('/response-form-collections')
  // @response(200, {
  //   description: 'ResponseFormCollection PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ResponseFormCollection, {partial: true}),
  //       },
  //     },
  //   })
  //   responseFormCollection: ResponseFormCollection,
  //   @param.where(ResponseFormCollection) where?: Where<ResponseFormCollection>,
  // ): Promise<Count> {
  //   return this.responseFormCollectionRepository.updateAll(responseFormCollection, where);
  // }

  @get('/response-form-collections/{id}')
  @response(200, {
    description: 'ResponseFormCollection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ResponseFormCollection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ResponseFormCollection, {exclude: 'where'}) filter?: FilterExcludingWhere<ResponseFormCollection>
  ): Promise<ResponseFormCollection> {
    return this.responseFormCollectionRepository.findById(id, filter);
  }

  @patch('/response-form-collections/{id}')
  @response(204, {
    description: 'ResponseFormCollection PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ResponseFormCollection, {partial: true}),
        },
      },
    })
    responseFormCollection: ResponseFormCollection,
  ): Promise<void> {
    responseFormCollection.updated = DateTime.utc().toString()
    await this.responseFormCollectionRepository.updateById(id, responseFormCollection);
  }

  @put('/response-form-collections/{id}')
  @response(204, {
    description: 'ResponseFormCollection PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() responseFormCollection: ResponseFormCollection,
  ): Promise<void> {
    await this.responseFormCollectionRepository.replaceById(id, responseFormCollection);
  }

  @del('/response-form-collections/{id}')
  @response(204, {
    description: 'ResponseFormCollection DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.responseFormCollectionRepository.deleteById(id);
  }
}
