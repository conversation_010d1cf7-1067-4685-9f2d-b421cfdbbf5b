import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SupplierAudit, SupplierAuditRelations} from '../models';

export class SupplierAuditRepository extends DefaultCrudRepository<
  SupplierAudit,
  typeof SupplierAudit.prototype.id,
  SupplierAuditRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SupplierAudit, dataSource);
  }
}
