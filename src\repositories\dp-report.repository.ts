import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DpReport, DpReportRelations} from '../models';

export class DpReportRepository extends DefaultCrudRepository<
  DpReport,
  typeof DpReport.prototype.id,
  DpReportRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(DpReport, dataSource);
  }
}
