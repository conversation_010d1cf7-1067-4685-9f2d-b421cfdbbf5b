import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {QuantitativeDpReport} from '../models';
import {QuantitativeDpReportRepository} from '../repositories';

export class QuantitativeDpReportController {
  constructor(
    @repository(QuantitativeDpReportRepository)
    public quantitativeDpReportRepository: QuantitativeDpReportRepository,
  ) { }

  @post('/quantitative-dp-reports')
  @response(200, {
    description: 'QuantitativeDpReport model instance',
    content: {'application/json': {schema: getModelSchemaRef(QuantitativeDpReport)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeDpReport, {
            title: 'NewQuantitativeDpReport',
            exclude: ['id'],
          }),
        },
      },
    })
    quantitativeDpReport: Omit<QuantitativeDpReport, 'id'>,
  ): Promise<QuantitativeDpReport> {
    return this.quantitativeDpReportRepository.create(quantitativeDpReport);
  }

  @get('/quantitative-dp-reports/count')
  @response(200, {
    description: 'QuantitativeDpReport model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QuantitativeDpReport) where?: Where<QuantitativeDpReport>,
  ): Promise<Count> {
    return this.quantitativeDpReportRepository.count(where);
  }

  @get('/quantitative-dp-reports')
  @response(200, {
    description: 'Array of QuantitativeDpReport model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QuantitativeDpReport, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QuantitativeDpReport) filter?: Filter<QuantitativeDpReport>,
  ): Promise<QuantitativeDpReport[]> {
    return this.quantitativeDpReportRepository.find(filter);
  }

  @patch('/quantitative-dp-reports')
  @response(200, {
    description: 'QuantitativeDpReport PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeDpReport, {partial: true}),
        },
      },
    })
    quantitativeDpReport: QuantitativeDpReport,
    @param.where(QuantitativeDpReport) where?: Where<QuantitativeDpReport>,
  ): Promise<Count> {
    return this.quantitativeDpReportRepository.updateAll(quantitativeDpReport, where);
  }

  @get('/quantitative-dp-reports/{id}')
  @response(200, {
    description: 'QuantitativeDpReport model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QuantitativeDpReport, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QuantitativeDpReport, {exclude: 'where'}) filter?: FilterExcludingWhere<QuantitativeDpReport>
  ): Promise<QuantitativeDpReport> {
    return this.quantitativeDpReportRepository.findById(id, filter);
  }

  @patch('/quantitative-dp-reports/{id}')
  @response(204, {
    description: 'QuantitativeDpReport PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeDpReport, {partial: true}),
        },
      },
    })
    quantitativeDpReport: QuantitativeDpReport,
  ): Promise<void> {
    await this.quantitativeDpReportRepository.updateById(id, quantitativeDpReport);
  }

  @put('/quantitative-dp-reports/{id}')
  @response(204, {
    description: 'QuantitativeDpReport PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() quantitativeDpReport: QuantitativeDpReport,
  ): Promise<void> {
    await this.quantitativeDpReportRepository.replaceById(id, quantitativeDpReport);
  }

  @del('/quantitative-dp-reports/{id}')
  @response(204, {
    description: 'QuantitativeDpReport DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.quantitativeDpReportRepository.deleteById(id);
  }

  @post('/delete-dpreport-by-submitId')
  async deleteUsersByuserId(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              submitId: {type: 'number'},
              userProfileId: {type: 'number'}

            },
            required: ['submitId', 'userProfileId'],
          },
        },
      },
    })
    requestBody: {submitId: number, userProfileId: number},
  ): Promise<any> {
    const {submitId, userProfileId} = requestBody;

    try {
      const deleteResult = await this.quantitativeDpReportRepository.deleteAll({
        submitId
      });
      return deleteResult;

    } catch (e) {
      throw new HttpErrors.BadRequest(e);
    }

  }
}
