import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';

import {AssignDcfClient} from '../models';
import {AssignDcfClientRepository,AssignDcfUserRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class AssignDcfClientController {


  constructor(
    @repository(AssignDcfClientRepository)
    public assignDcfClientRepository : AssignDcfClientRepository,
    @repository(AssignDcfUserRepository)
    public assignDcfUserRepository : AssignDcfUserRepository,
  ) {}

  @post('/assign-dcf-clients')
  @response(200, {
    description: 'AssignDcfClient model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDcfClient)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfClient, {
            title: 'NewAssignDcfClient',
            exclude: ['id'],
          }),
        },
      },
    })
    assignDcfClient: Omit<AssignDcfClient, 'id'>,
  ): Promise<AssignDcfClient> {
    
    return this.assignDcfClientRepository.create(assignDcfClient);
  }

  @get('/assign-dcf-clients/count')
  @response(200, {
    description: 'AssignDcfClient model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDcfClient) where?: Where<AssignDcfClient>,
  ): Promise<Count> {
    return this.assignDcfClientRepository.count(where);
  }

  @get('/assign-dcf-clients')
  @response(200, {
    description: 'Array of AssignDcfClient model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDcfClient, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDcfClient) filter?: Filter<AssignDcfClient>,
  ): Promise<AssignDcfClient[]> {
    return this.assignDcfClientRepository.find(filter);
  }

  // @patch('/assign-dcf-clients')
  // @response(200, {
  //   description: 'AssignDcfClient PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfClient, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfClient: AssignDcfClient,
  //   @param.where(AssignDcfClient) where?: Where<AssignDcfClient>,
  // ): Promise<Count> {
  //   return this.assignDcfClientRepository.updateAll(assignDcfClient, where);
  // }

  @get('/assign-dcf-clients/{id}')
  @response(200, {
    description: 'AssignDcfClient model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDcfClient, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDcfClient, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDcfClient>
  ): Promise<AssignDcfClient> {
    return this.assignDcfClientRepository.findById(id, filter);
  }

  // @patch('/assign-dcf-clients/{id}')
  // @response(204, {
  //   description: 'AssignDcfClient PATCH success',
  // })
  // async updateById(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfClient, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfClient: AssignDcfClient,
  // ): Promise<void> {
  //   const assignDcfClientData = await this.assignDcfClientRepository.findById(id)
  //   const assignDcfUserData = await this.assignDcfUserRepository.find({where:{userProfileId: assignDcfClientData.userProfileId} })
  //   const dcfIdsArray = assignDcfClientData.dcf_ids || [];
  //   const idsToDelete = assignDcfUserData .filter((user) => !dcfIdsArray.includes(user.dcfid)).map((user) => user.id);

  // Delete the items from assignDcfUserRepository based on their IDs
  // for (const idToDelete of idsToDelete) {
  //   await this.assignDcfUserRepository.deleteById(idToDelete);
  // }
  //   await this.assignDcfClientRepository.updateById(id, assignDcfClient);
  // }


  @patch('/assign-dcf-clients-delete-users-assign/{id}')
  @response(204, {
    description: 'AssignDcfClient PATCH success',
  })
  async updateById_(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfClient, {partial: true}),
        },
      },
    })
    assignDcfClient: AssignDcfClient,
  ): Promise<void> {
    const assignDcfClientData = await this.assignDcfClientRepository.findById(id)
    const assignDcfUserData = await this.assignDcfUserRepository.find({where: {userProfileId: assignDcfClientData.userProfileId}})
    const dcfIdsArray = assignDcfClientData.dcf_ids || [];
    const idsToDelete = assignDcfUserData.filter((user) => !dcfIdsArray.includes(user.dcfid)).map((user) => user.id);

    // Delete the items from assignDcfUserRepository based on their IDs
    for (const idToDelete of idsToDelete) {
      await this.assignDcfUserRepository.deleteById(idToDelete);
    }
    await this.assignDcfClientRepository.updateById(id, assignDcfClient);
  }
  @patch('/assign-dcf-clients/{id}')
  @response(204, {
    description: 'AssignDcfClient PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfClient, {partial: true}),
        },
      },
    })
    assignDcfClient: AssignDcfClient,
  ): Promise<void> {

    await this.assignDcfClientRepository.updateById(id, assignDcfClient);
  }

  @put('/assign-dcf-clients/{id}')
  @response(204, {
    description: 'AssignDcfClient PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignDcfClient: AssignDcfClient,
  ): Promise<void> {
    await this.assignDcfClientRepository.replaceById(id, assignDcfClient);
  }

  @del('/assign-dcf-clients/{id}')
  @response(204, {
    description: 'AssignDcfClient DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDcfClientRepository.deleteById(id);
  }
}
