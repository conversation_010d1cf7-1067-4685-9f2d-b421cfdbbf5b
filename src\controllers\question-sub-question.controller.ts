import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Question,
  SubQuestion,
} from '../models';
import {QuestionRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';


export class QuestionSubQuestionController {
  constructor(
    @repository(QuestionRepository) protected questionRepository: QuestionRepository,
  ) { }

  @get('/questions/{id}/sub-questions', {
    responses: {
      '200': {
        description: 'Array of Question has many SubQuestion',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SubQuestion)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SubQuestion>,
  ): Promise<SubQuestion[]> {
    return this.questionRepository.subQuestions(id).find(filter);
  }

  @post('/questions/{id}/sub-questions', {
    responses: {
      '200': {
        description: 'Question model instance',
        content: {'application/json': {schema: getModelSchemaRef(SubQuestion)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Question.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubQuestion, {
            title: 'NewSubQuestionInQuestion',
            exclude: ['id'],
            optional: ['questionId']
          }),
        },
      },
    }) subQuestion: Omit<SubQuestion, 'id'>,
  ): Promise<SubQuestion> {
    return this.questionRepository.subQuestions(id).create(subQuestion);
  }

  @patch('/questions/{id}/sub-questions', {
    responses: {
      '200': {
        description: 'Question.SubQuestion PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubQuestion, {partial: true}),
        },
      },
    })
    subQuestion: Partial<SubQuestion>,
    @param.query.object('where', getWhereSchemaFor(SubQuestion)) where?: Where<SubQuestion>,
  ): Promise<Count> {
    return this.questionRepository.subQuestions(id).patch(subQuestion, where);
  }

  @del('/questions/{id}/sub-questions', {
    responses: {
      '200': {
        description: 'Question.SubQuestion DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SubQuestion)) where?: Where<SubQuestion>,
  ): Promise<Count> {
    return this.questionRepository.subQuestions(id).delete(where);
  }
}
