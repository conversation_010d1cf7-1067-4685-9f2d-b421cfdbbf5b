import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SupplierAction,
  SupplierActionHistory,
} from '../models';
import {SupplierActionRepository} from '../repositories';

export class SupplierActionSupplierActionHistoryController {
  constructor(
    @repository(SupplierActionRepository) protected supplierActionRepository: SupplierActionRepository,
  ) { }

  @get('/supplier-actions/{id}/supplier-action-histories', {
    responses: {
      '200': {
        description: 'Array of SupplierAction has many SupplierActionHistory',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplierActionHistory)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SupplierActionHistory>,
  ): Promise<SupplierActionHistory[]> {
    return this.supplierActionRepository.supplierActionHistories(id).find(filter);
  }

  @post('/supplier-actions/{id}/supplier-action-histories', {
    responses: {
      '200': {
        description: 'SupplierAction model instance',
        content: {'application/json': {schema: getModelSchemaRef(SupplierActionHistory)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof SupplierAction.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierActionHistory, {
            title: 'NewSupplierActionHistoryInSupplierAction',
            exclude: ['id'],
            optional: ['supplierActionId']
          }),
        },
      },
    }) supplierActionHistory: Omit<SupplierActionHistory, 'id'>,
  ): Promise<SupplierActionHistory> {
    return this.supplierActionRepository.supplierActionHistories(id).create(supplierActionHistory);
  }

  @patch('/supplier-actions/{id}/supplier-action-histories', {
    responses: {
      '200': {
        description: 'SupplierAction.SupplierActionHistory PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierActionHistory, {partial: true}),
        },
      },
    })
    supplierActionHistory: Partial<SupplierActionHistory>,
    @param.query.object('where', getWhereSchemaFor(SupplierActionHistory)) where?: Where<SupplierActionHistory>,
  ): Promise<Count> {
    return this.supplierActionRepository.supplierActionHistories(id).patch(supplierActionHistory, where);
  }

  @del('/supplier-actions/{id}/supplier-action-histories', {
    responses: {
      '200': {
        description: 'SupplierAction.SupplierActionHistory DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SupplierActionHistory)) where?: Where<SupplierActionHistory>,
  ): Promise<Count> {
    return this.supplierActionRepository.supplierActionHistories(id).delete(where);
  }
}
