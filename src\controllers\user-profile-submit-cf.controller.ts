import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  SubmitCf,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class UserProfileSubmitCfController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/submit-cfs', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SubmitCf',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SubmitCf)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SubmitCf>,
  ): Promise<SubmitCf[]> {
    return this.userProfileRepository.submitCfs(id).find(filter);
  }

  @post('/user-profiles/{id}/submit-cfs', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SubmitCf)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitCf, {
            title: 'NewSubmitCfInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) submitCf: Omit<SubmitCf, 'id'>,
  ): Promise<SubmitCf> {
    return this.userProfileRepository.submitCfs(id).create(submitCf);
  }

  // @patch('/user-profiles/{id}/submit-cfs', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitCf PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitCf, {partial: true}),
  //       },
  //     },
  //   })
  //   submitCf: Partial<SubmitCf>,
  //   @param.query.object('where', getWhereSchemaFor(SubmitCf)) where?: Where<SubmitCf>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitCfs(id).patch(submitCf, where);
  // }

  // @del('/user-profiles/{id}/submit-cfs', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitCf DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(SubmitCf)) where?: Where<SubmitCf>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitCfs(id).delete(where);
  // }
}
