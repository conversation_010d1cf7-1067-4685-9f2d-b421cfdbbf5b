import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewEfCategory,
  NewEfSubcategory1,
} from '../models';
import {NewEfCategoryRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfCategoryNewEfSubcategory1Controller {
  constructor(
    @repository(NewEfCategoryRepository) protected newEfCategoryRepository: NewEfCategoryRepository,
  ) { }

  @get('/new-ef-categories/{id}/new-ef-subcategory1s', {
    responses: {
      '200': {
        description: 'Array of NewEfCategory has many NewEfSubcategory1',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfSubcategory1)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEfSubcategory1>,
  ): Promise<NewEfSubcategory1[]> {
    return this.newEfCategoryRepository.newEfSubcategory1s(id).find(filter);
  }

  @post('/new-ef-categories/{id}/new-ef-subcategory1s', {
    responses: {
      '200': {
        description: 'NewEfCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEfSubcategory1)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEfCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory1, {
            title: 'NewNewEfSubcategory1InNewEfCategory',
            exclude: ['id'],
            optional: ['newEfCategoryId']
          }),
        },
      },
    }) newEfSubcategory1: Omit<NewEfSubcategory1, 'id'>,
  ): Promise<NewEfSubcategory1> {
    return this.newEfCategoryRepository.newEfSubcategory1s(id).create(newEfSubcategory1);
  }

  // @patch('/new-ef-categories/{id}/new-ef-subcategory1s', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfCategory.NewEfSubcategory1 PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfSubcategory1, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfSubcategory1: Partial<NewEfSubcategory1>,
  //   @param.query.object('where', getWhereSchemaFor(NewEfSubcategory1)) where?: Where<NewEfSubcategory1>,
  // ): Promise<Count> {
  //   return this.newEfCategoryRepository.newEfSubcategory1s(id).patch(newEfSubcategory1, where);
  // }

  // @del('/new-ef-categories/{id}/new-ef-subcategory1s', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfCategory.NewEfSubcategory1 DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewEfSubcategory1)) where?: Where<NewEfSubcategory1>,
  // ): Promise<Count> {
  //   return this.newEfCategoryRepository.newEfSubcategory1s(id).delete(where);
  // }
}
