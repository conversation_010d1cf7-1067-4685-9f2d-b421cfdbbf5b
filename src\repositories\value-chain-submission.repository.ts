import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ValueChainSubmission, ValueChainSubmissionWithRelations, ConsolidateFormCollection, VendorCode} from '../models';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';
import {VendorCodeRepository} from './vendor-code.repository';

export class ValueChainSubmissionRepository extends DefaultCrudRepository<
  ValueChainSubmission,
  typeof ValueChainSubmission.prototype.id,
  ValueChainSubmissionWithRelations
> {

  public readonly srf: BelongsToAccessor<ConsolidateFormCollection, typeof ValueChainSubmission.prototype.id>;

  public readonly vendor: BelongsToAccessor<VendorCode, typeof ValueChainSubmission.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>,
  ) {
    super(ValueChainSubmission, dataSource);
    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
    this.srf = this.createBelongsToAccessorFor('srf', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('srf', this.srf.inclusionResolver);
  }
}
