import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewIndicatorTwo} from '../models';
import {NewIndicatorTwoRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewIndicatorTwoController {
  constructor(
    @repository(NewIndicatorTwoRepository)
    public newIndicatorTwoRepository : NewIndicatorTwoRepository,
  ) {}

  @post('/new-indicator-twos')
  @response(200, {
    description: 'NewIndicatorTwo model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewIndicatorTwo)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewIndicatorTwo, {
            title: 'NewNewIndicatorTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    newIndicatorTwo: Omit<NewIndicatorTwo, 'id'>,
  ): Promise<NewIndicatorTwo> {
    return this.newIndicatorTwoRepository.create(newIndicatorTwo);
  }

  @get('/new-indicator-twos/count')
  @response(200, {
    description: 'NewIndicatorTwo model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewIndicatorTwo) where?: Where<NewIndicatorTwo>,
  ): Promise<Count> {
    return this.newIndicatorTwoRepository.count(where);
  }

  @get('/new-indicator-twos')
  @response(200, {
    description: 'Array of NewIndicatorTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewIndicatorTwo, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewIndicatorTwo) filter?: Filter<NewIndicatorTwo>,
  ): Promise<NewIndicatorTwo[]> {
    return this.newIndicatorTwoRepository.find(filter);
  }

  // @patch('/new-indicator-twos')
  // @response(200, {
  //   description: 'NewIndicatorTwo PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewIndicatorTwo, {partial: true}),
  //       },
  //     },
  //   })
  //   newIndicatorTwo: NewIndicatorTwo,
  //   @param.where(NewIndicatorTwo) where?: Where<NewIndicatorTwo>,
  // ): Promise<Count> {
  //   return this.newIndicatorTwoRepository.updateAll(newIndicatorTwo, where);
  // }

  @get('/new-indicator-twos/{id}')
  @response(200, {
    description: 'NewIndicatorTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewIndicatorTwo, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewIndicatorTwo, {exclude: 'where'}) filter?: FilterExcludingWhere<NewIndicatorTwo>
  ): Promise<NewIndicatorTwo> {
    return this.newIndicatorTwoRepository.findById(id, filter);
  }

  @patch('/new-indicator-twos/{id}')
  @response(204, {
    description: 'NewIndicatorTwo PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewIndicatorTwo, {partial: true}),
        },
      },
    })
    newIndicatorTwo: NewIndicatorTwo,
  ): Promise<void> {
    await this.newIndicatorTwoRepository.updateById(id, newIndicatorTwo);
  }

  @put('/new-indicator-twos/{id}')
  @response(204, {
    description: 'NewIndicatorTwo PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newIndicatorTwo: NewIndicatorTwo,
  ): Promise<void> {
    await this.newIndicatorTwoRepository.replaceById(id, newIndicatorTwo);
  }

  @del('/new-indicator-twos/{id}')
  @response(204, {
    description: 'NewIndicatorTwo DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newIndicatorTwoRepository.deleteById(id);
  }
}
