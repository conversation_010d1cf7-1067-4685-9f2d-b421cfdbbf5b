import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignRfEntity, AssignRfEntityRelations} from '../models';

export class AssignRfEntityRepository extends DefaultCrudRepository<
  AssignRfEntity,
  typeof AssignRfEntity.prototype.id,
  AssignRfEntityRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AssignRfEntity, dataSource);
  }
}
