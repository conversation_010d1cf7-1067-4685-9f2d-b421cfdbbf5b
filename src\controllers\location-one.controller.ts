import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {LocationOne} from '../models';
import {LocationOneRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class LocationOneController {
  constructor(
    @repository(LocationOneRepository)
    public locationOneRepository : LocationOneRepository,
  ) {}

  @post('/location-ones')
  @response(200, {
    description: 'LocationOne model instance',
    content: {'application/json': {schema: getModelSchemaRef(LocationOne)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationOne, {
            title: 'NewLocationOne',
            exclude: ['id'],
          }),
        },
      },
    })
    locationOne: Omit<LocationOne, 'id'>,
  ): Promise<LocationOne> {
    return this.locationOneRepository.create(locationOne);
  }

  @get('/location-ones/count')
  @response(200, {
    description: 'LocationOne model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(LocationOne) where?: Where<LocationOne>,
  ): Promise<Count> {
    return this.locationOneRepository.count(where);
  }

  @get('/location-ones')
  @response(200, {
    description: 'Array of LocationOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(LocationOne, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(LocationOne) filter?: Filter<LocationOne>,
  ): Promise<LocationOne[]> {
    return this.locationOneRepository.find(filter);
  }

  // @patch('/location-ones')
  // @response(200, {
  //   description: 'LocationOne PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(LocationOne, {partial: true}),
  //       },
  //     },
  //   })
  //   locationOne: LocationOne,
  //   @param.where(LocationOne) where?: Where<LocationOne>,
  // ): Promise<Count> {
  //   return this.locationOneRepository.updateAll(locationOne, where);
  // }

  @get('/location-ones/{id}')
  @response(200, {
    description: 'LocationOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(LocationOne, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(LocationOne, {exclude: 'where'}) filter?: FilterExcludingWhere<LocationOne>
  ): Promise<LocationOne> {
    return this.locationOneRepository.findById(id, filter);
  }

  @patch('/location-ones/{id}')
  @response(204, {
    description: 'LocationOne PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationOne, {partial: true}),
        },
      },
    })
    locationOne: LocationOne,
  ): Promise<void> {
    await this.locationOneRepository.updateById(id, locationOne);
  }

  @put('/location-ones/{id}')
  @response(204, {
    description: 'LocationOne PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() locationOne: LocationOne,
  ): Promise<void> {
    await this.locationOneRepository.replaceById(id, locationOne);
  }

  @del('/location-ones/{id}')
  @response(204, {
    description: 'LocationOne DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.locationOneRepository.deleteById(id);
  }
}
