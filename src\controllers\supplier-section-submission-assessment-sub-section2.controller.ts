import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierSectionSubmission,
  AssessmentSubSection2,
} from '../models';
import {SupplierSectionSubmissionRepository} from '../repositories';

export class SupplierSectionSubmissionAssessmentSubSection2Controller {
  constructor(
    @repository(SupplierSectionSubmissionRepository)
    public supplierSectionSubmissionRepository: SupplierSectionSubmissionRepository,
  ) { }

  @get('/supplier-section-submissions/{id}/assessment-sub-section2', {
    responses: {
      '200': {
        description: 'AssessmentSubSection2 belonging to SupplierSectionSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssessmentSubSection2)},
          },
        },
      },
    },
  })
  async getAssessmentSubSection2(
    @param.path.string('id') id: typeof SupplierSectionSubmission.prototype.id,
  ): Promise<AssessmentSubSection2> {
    return this.supplierSectionSubmissionRepository.assessmentSubSection2(id);
  }
}
