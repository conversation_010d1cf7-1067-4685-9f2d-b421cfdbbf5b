import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {LcaDataSubmission, VendorCode, VendorCodeRelations} from '../models';
import {LcaDataSubmissionRepository} from './lca-data-submission.repository';

export class VendorCodeRepository extends DefaultCrudRepository<
  VendorCode,
  typeof VendorCode.prototype.id,
  VendorCodeRelations
> {
  public readonly lcaDataSubmissions: HasManyRepositoryFactory<LcaDataSubmission, typeof VendorCode.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
    @repository.getter('LcaDataSubmissionRepository')
    protected lcaDataSubmissionRepositoryGetter: Getter<LcaDataSubmissionRepository>,
  ) {
    super(VendorCode, dataSource);
    this.lcaDataSubmissions = this.createHasManyRepositoryFactoryFor('lcaDataSubmissions', lcaDataSubmissionRepositoryGetter);
    this.registerInclusionResolver('lcaDataSubmissions', this.lcaDataSubmissions.inclusionResolver);
  }

  // Find vendor with LCA data submissions
  async findWithLcaDataSubmissions(vendorId: number): Promise<VendorCode | null> {
    return this.findById(vendorId, {
      include: [
        {
          relation: 'lcaDataSubmissions',
          scope: {
            order: ['created_on DESC'],
          },
        },
      ],
    });
  }

  // Find vendor by code with LCA data submissions
  async findByCodeWithLcaDataSubmissions(code: string): Promise<VendorCode | null> {
    return this.findOne({
      where: {code: code},
      include: [
        {
          relation: 'lcaDataSubmissions',
          scope: {
            order: ['created_on DESC'],
          },
        },
      ],
    });
  }
}
