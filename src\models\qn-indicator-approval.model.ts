import {belongsTo, Entity, model, property} from '@loopback/repository';
import {NewMetric} from './new-metric.model';

@model()
export class QnIndicatorApproval extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;



  @property({
    type: 'number',
  })
  year?: number;
  @property({
    type: 'array',
    itemType: 'any',
  })
  performanceCommentary?: any[];


  @property({
    type: 'any',
  })
  tier1_id?: any;
  @property({
    type: 'any',
  })
  tier2_id?: any;
  @property({
    type: 'any',
  })
  tier3_id?: any;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @belongsTo(() => NewMetric)
  indicatorId: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<QnIndicatorApproval>) {
    super(data);
  }
}

export interface QnIndicatorApprovalRelations {
  // describe navigational properties here
}

export type QnIndicatorApprovalWithRelations = QnIndicatorApproval & QnIndicatorApprovalRelations;
