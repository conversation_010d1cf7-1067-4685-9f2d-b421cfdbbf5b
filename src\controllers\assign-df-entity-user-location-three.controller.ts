import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignDfEntityUser,
  LocationThree,
} from '../models';
import {AssignDfEntityUserRepository} from '../repositories';

export class AssignDfEntityUserLocationThreeController {
  constructor(
    @repository(AssignDfEntityUserRepository)
    public assignDfEntityUserRepository: AssignDfEntityUserRepository,
  ) { }

  @get('/assign-df-entity-users/{id}/location-three', {
    responses: {
      '200': {
        description: 'LocationThree belonging to AssignDfEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationThree),
          },
        },
      },
    },
  })
  async getLocationThree(
    @param.path.number('id') id: typeof AssignDfEntityUser.prototype.id,
  ): Promise<LocationThree> {
    return this.assignDfEntityUserRepository.lthree(id);
  }
}
