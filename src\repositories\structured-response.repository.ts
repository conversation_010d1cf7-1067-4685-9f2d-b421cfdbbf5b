import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {StructuredResponse, StructuredResponseRelations, FormCollection, QuantitativeSubmission} from '../models';
import {FormCollectionRepository} from './form-collection.repository';
import {QuantitativeSubmissionRepository} from './quantitative-submission.repository';

export class StructuredResponseRepository extends DefaultCrudRepository<
  StructuredResponse,
  typeof StructuredResponse.prototype.id,
  StructuredResponseRelations
> {

  public readonly dcf: BelongsToAccessor<FormCollection, typeof StructuredResponse.prototype.id>;

  public readonly submitDcf: BelongsToAccessor<QuantitativeSubmission, typeof StructuredResponse.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('FormCollectionRepository') protected formCollectionRepositoryGetter: Getter<FormCollectionRepository>, @repository.getter('QuantitativeSubmissionRepository') protected quantitativeSubmissionRepositoryGetter: Getter<QuantitativeSubmissionRepository>,
  ) {
    super(StructuredResponse, dataSource);
    this.submitDcf = this.createBelongsToAccessorFor('submitDcf', quantitativeSubmissionRepositoryGetter,);
    this.registerInclusionResolver('submitDcf', this.submitDcf.inclusionResolver);
    this.dcf = this.createBelongsToAccessorFor('dcf', formCollectionRepositoryGetter,);
    this.registerInclusionResolver('dcf', this.dcf.inclusionResolver);
  }
}
