import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {AssessmentSupplierList} from '../models';
import {AssessmentSupplierListRepository, ConsolidateFormCollectionRepository, SupplierAssessmentAssignmentRepository, SupplierAssignmentSubmissionRepository} from '../repositories';

export class AssessmentSupplierListController {
  constructor(
    @repository(AssessmentSupplierListRepository)
    public assessmentSupplierListRepository: AssessmentSupplierListRepository,
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
    @repository(ConsolidateFormCollectionRepository)
    public consolidateFormCollectionRepository: ConsolidateFormCollectionRepository,
    @repository(SupplierAssignmentSubmissionRepository)
    public supplierAssignmentSubmissionRepository: SupplierAssignmentSubmissionRepository,
  ) { }

  @post('/assessment-supplier-lists')
  @response(200, {
    description: 'AssessmentSupplierList model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssessmentSupplierList)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSupplierList, {
            title: 'NewAssessmentSupplierList',
            exclude: ['id'],
          }),
        },
      },
    })
    assessmentSupplierList: Omit<AssessmentSupplierList, 'id'>,
  ): Promise<AssessmentSupplierList> {
    return this.assessmentSupplierListRepository.create(assessmentSupplierList);
  }

  @get('/assessment-supplier-lists/count')
  @response(200, {
    description: 'AssessmentSupplierList model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssessmentSupplierList) where?: Where<AssessmentSupplierList>,
  ): Promise<Count> {
    return this.assessmentSupplierListRepository.count(where);
  }

  @get('/assessment-supplier-lists')
  @response(200, {
    description: 'Array of AssessmentSupplierList model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssessmentSupplierList, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssessmentSupplierList) filter?: Filter<AssessmentSupplierList>,
  ): Promise<AssessmentSupplierList[]> {
    return this.assessmentSupplierListRepository.find(filter);
  }

  @patch('/assessment-supplier-lists')
  @response(200, {
    description: 'AssessmentSupplierList PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSupplierList, {partial: true}),
        },
      },
    })
    assessmentSupplierList: AssessmentSupplierList,
    @param.where(AssessmentSupplierList) where?: Where<AssessmentSupplierList>,
  ): Promise<Count> {
    return this.assessmentSupplierListRepository.updateAll(assessmentSupplierList, where);
  }

  @get('/assessment-supplier-lists/{id}')
  @response(200, {
    description: 'AssessmentSupplierList model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssessmentSupplierList, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssessmentSupplierList, {exclude: 'where'}) filter?: FilterExcludingWhere<AssessmentSupplierList>
  ): Promise<AssessmentSupplierList> {
    return this.assessmentSupplierListRepository.findById(id, filter);
  }

  @patch('/assessment-supplier-lists/{id}')
  @response(204, {
    description: 'AssessmentSupplierList PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSupplierList, {partial: true}),
        },
      },
    })
    assessmentSupplierList: AssessmentSupplierList,
  ): Promise<void> {
    await this.assessmentSupplierListRepository.updateById(id, assessmentSupplierList);
  }

  @put('/assessment-supplier-lists/{id}')
  @response(204, {
    description: 'AssessmentSupplierList PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assessmentSupplierList: AssessmentSupplierList,
  ): Promise<void> {
    await this.assessmentSupplierListRepository.replaceById(id, assessmentSupplierList);
  }

  @del('/assessment-supplier-lists/{id}')
  @response(204, {
    description: 'AssessmentSupplierList DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assessmentSupplierListRepository.deleteById(id);
  }

  // @post('/check-supplier-assessment-assignment/{id}')
  // @response(200, {
  //   description: 'AssessmentSupplierList PATCH success',
  // })
  // async checkSupplierAssessmentAssignment(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     description: 'Empty Body',
  //     content: {
  //       'application/json': {
  //         schema: {type: 'object', additionalProperties: false},  // Empty object schema
  //       },
  //     },
  //     required: false,
  //   })
  //   body: object = {},

  // ): Promise<any> {

  //   let assessmentSupplierList = await this.assessmentSupplierListRepository.findById(id);
  //   if (assessmentSupplierList) {
  //     let supplierAssessmentAssignment = await this.supplierAssessmentAssignmentRepository.findById(assessmentSupplierList.supplierAssessmentAssignmentId)
  //     if (supplierAssessmentAssignment) {
  //       let srf = await this.consolidateFormCollectionRepository.findById(supplierAssessmentAssignment.srfId)
  //       if (srf) {
  //         let assignmentSubmission = await this.supplierAssignmentSubmissionRepository.find({where: {srfId: srf.id, supplierId: assessmentSupplierList.supplierId}})
  //         console.log(assignmentSubmission)
  //         if (assignmentSubmission.length > 0) {
  //           const response: any = assignmentSubmission?.[0]?.response || []; // response is an array
  //           const parsedResponse = Array.isArray(response) ? response : [];
  //           srf['data1'] = JSON.stringify(parsedResponse)
  //           return {
  //             "status": true,
  //             data: srf, assignment: supplierAssessmentAssignment, supplierAssignment: assessmentSupplierList, submitted: assignmentSubmission[0].type === 0 ? false : true, submission: assignmentSubmission[0]


  //           }
  //         } else {
  //           return {
  //             "status": true,
  //             data: srf, assignment: supplierAssessmentAssignment, supplierAssignment: assessmentSupplierList, submitted: false, submission: null


  //           }
  //         }


  //       } else {
  //         return {
  //           "status": false,
  //           "message": "Questionary has not been Found"
  //         }
  //       }

  //     } else {
  //       return {
  //         "status": false,
  //         "message": "It seems assignment has been removed"
  //       }
  //     }
  //   } else {
  //     return {
  //       "status": false,
  //       "message": "It seems you have removed from assignment"
  //     }
  //   }

  // }
}
