import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {PolicyProcedure} from '../models';
import {PolicyProcedureRepository} from '../repositories';

export class PolicyProcedureController {
  constructor(
    @repository(PolicyProcedureRepository)
    public policyProcedureRepository : PolicyProcedureRepository,
  ) {}

  @post('/policy-procedures')
  @response(200, {
    description: 'PolicyProcedure model instance',
    content: {'application/json': {schema: getModelSchemaRef(PolicyProcedure)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PolicyProcedure, {
            title: 'NewPolicyProcedure',
            exclude: ['id'],
          }),
        },
      },
    })
    policyProcedure: Omit<PolicyProcedure, 'id'>,
  ): Promise<PolicyProcedure> {
    return this.policyProcedureRepository.create(policyProcedure);
  }

  @get('/policy-procedures/count')
  @response(200, {
    description: 'PolicyProcedure model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(PolicyProcedure) where?: Where<PolicyProcedure>,
  ): Promise<Count> {
    return this.policyProcedureRepository.count(where);
  }

  @get('/policy-procedures')
  @response(200, {
    description: 'Array of PolicyProcedure model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(PolicyProcedure, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(PolicyProcedure) filter?: Filter<PolicyProcedure>,
  ): Promise<PolicyProcedure[]> {
    return this.policyProcedureRepository.find(filter);
  }

  @patch('/policy-procedures')
  @response(200, {
    description: 'PolicyProcedure PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PolicyProcedure, {partial: true}),
        },
      },
    })
    policyProcedure: PolicyProcedure,
    @param.where(PolicyProcedure) where?: Where<PolicyProcedure>,
  ): Promise<Count> {
    return this.policyProcedureRepository.updateAll(policyProcedure, where);
  }

  @get('/policy-procedures/{id}')
  @response(200, {
    description: 'PolicyProcedure model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(PolicyProcedure, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(PolicyProcedure, {exclude: 'where'}) filter?: FilterExcludingWhere<PolicyProcedure>
  ): Promise<PolicyProcedure> {
    return this.policyProcedureRepository.findById(id, filter);
  }

  @patch('/policy-procedures/{id}')
  @response(204, {
    description: 'PolicyProcedure PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PolicyProcedure, {partial: true}),
        },
      },
    })
    policyProcedure: PolicyProcedure,
  ): Promise<void> {
    await this.policyProcedureRepository.updateById(id, policyProcedure);
  }

  @put('/policy-procedures/{id}')
  @response(204, {
    description: 'PolicyProcedure PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() policyProcedure: PolicyProcedure,
  ): Promise<void> {
    await this.policyProcedureRepository.replaceById(id, policyProcedure);
  }

  @del('/policy-procedures/{id}')
  @response(204, {
    description: 'PolicyProcedure DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.policyProcedureRepository.deleteById(id);
  }
}
