import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SubTopic} from '../models';
import {SubTopicRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SubTopicController {
  constructor(
    @repository(SubTopicRepository)
    public subTopicRepository : SubTopicRepository,
  ) {}

  @post('/sub-topics')
  @response(200, {
    description: 'SubTopic model instance',
    content: {'application/json': {schema: getModelSchemaRef(SubTopic)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubTopic, {
            title: 'NewSubTopic',
            exclude: ['id'],
          }),
        },
      },
    })
    subTopic: Omit<SubTopic, 'id'>,
  ): Promise<SubTopic> {
    return this.subTopicRepository.create(subTopic);
  }

  @get('/sub-topics/count')
  @response(200, {
    description: 'SubTopic model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SubTopic) where?: Where<SubTopic>,
  ): Promise<Count> {
    return this.subTopicRepository.count(where);
  }

  @get('/sub-topics')
  @response(200, {
    description: 'Array of SubTopic model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SubTopic, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SubTopic) filter?: Filter<SubTopic>,
  ): Promise<SubTopic[]> {
    return this.subTopicRepository.find(filter);
  }

  // @patch('/sub-topics')
  // @response(200, {
  //   description: 'SubTopic PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubTopic, {partial: true}),
  //       },
  //     },
  //   })
  //   subTopic: SubTopic,
  //   @param.where(SubTopic) where?: Where<SubTopic>,
  // ): Promise<Count> {
  //   return this.subTopicRepository.updateAll(subTopic, where);
  // }

  @get('/sub-topics/{id}')
  @response(200, {
    description: 'SubTopic model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SubTopic, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SubTopic, {exclude: 'where'}) filter?: FilterExcludingWhere<SubTopic>
  ): Promise<SubTopic> {
    return this.subTopicRepository.findById(id, filter);
  }

  @patch('/sub-topics/{id}')
  @response(204, {
    description: 'SubTopic PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubTopic, {partial: true}),
        },
      },
    })
    subTopic: SubTopic,
  ): Promise<void> {
    await this.subTopicRepository.updateById(id, subTopic);
  }

  @put('/sub-topics/{id}')
  @response(204, {
    description: 'SubTopic PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() subTopic: SubTopic,
  ): Promise<void> {
    await this.subTopicRepository.replaceById(id, subTopic);
  }

  @del('/sub-topics/{id}')
  @response(204, {
    description: 'SubTopic DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.subTopicRepository.deleteById(id);
  }
}
