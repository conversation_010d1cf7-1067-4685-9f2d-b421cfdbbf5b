import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierSectionSubmission,
  AssessmentSection,
} from '../models';
import {SupplierSectionSubmissionRepository} from '../repositories';

export class SupplierSectionSubmissionAssessmentSectionController {
  constructor(
    @repository(SupplierSectionSubmissionRepository)
    public supplierSectionSubmissionRepository: SupplierSectionSubmissionRepository,
  ) { }

  @get('/supplier-section-submissions/{id}/assessment-section', {
    responses: {
      '200': {
        description: 'AssessmentSection belonging to SupplierSectionSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssessmentSection)},
          },
        },
      },
    },
  })
  async getAssessmentSection(
    @param.path.string('id') id: typeof SupplierSectionSubmission.prototype.id,
  ): Promise<AssessmentSection> {
    return this.supplierSectionSubmissionRepository.assessmentSection(id);
  }
}
