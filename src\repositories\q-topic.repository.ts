import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {QTopic, QTopicRelations, QSection} from '../models';
import {QSectionRepository} from './q-section.repository';

export class QTopicRepository extends DefaultCrudRepository<
  QTopic,
  typeof QTopic.prototype.id,
  QTopicRelations
> {

  public readonly qSections: HasManyRepositoryFactory<QSection, typeof QTopic.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('QSectionRepository') protected qSectionRepositoryGetter: Getter<QSectionRepository>,
  ) {
    super(QTopic, dataSource);
    this.qSections = this.createHasManyRepositoryFactoryFor('qSections', qSectionRepositoryGetter,);
    this.registerInclusionResolver('qSections', this.qSections.inclusionResolver);
  }
}
