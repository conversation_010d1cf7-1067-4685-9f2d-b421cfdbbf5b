import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignQlEntity,
  ConsolidateFormCollection,
} from '../models';
import {AssignQlEntityRepository} from '../repositories';

export class AssignQlEntityConsolidateFormCollectionController {
  constructor(
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
  ) { }

  @get('/assign-ql-entities/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to AssignQlEntity',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ConsolidateFormCollection),
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.number('id') id: typeof AssignQlEntity.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.assignQlEntityRepository.srf(id);
  }
}
