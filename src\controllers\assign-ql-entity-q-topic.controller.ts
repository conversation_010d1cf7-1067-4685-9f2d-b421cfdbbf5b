import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignQlEntity,
  QTopic,
} from '../models';
import {AssignQlEntityRepository} from '../repositories';

export class AssignQlEntityQTopicController {
  constructor(
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
  ) { }

  @get('/assign-ql-entities/{id}/q-topic', {
    responses: {
      '200': {
        description: 'QTopic belonging to AssignQlEntity',
        content: {
          'application/json': {
            schema: getModelSchemaRef(QTopic),
          },
        },
      },
    },
  })
  async getQTopic(
    @param.path.number('id') id: typeof AssignQlEntity.prototype.id,
  ): Promise<QTopic> {
    return this.assignQlEntityRepository.qTopic(id);
  }
}
