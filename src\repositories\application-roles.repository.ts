import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ApplicationRoles, ApplicationRolesRelations} from '../models';

export class ApplicationRolesRepository extends DefaultCrudRepository<
  ApplicationRoles,
  typeof ApplicationRoles.prototype.id,
  ApplicationRolesRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ApplicationRoles, dataSource);
  }
}
