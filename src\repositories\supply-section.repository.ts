import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SupplySection, SupplySectionRelations, SupplyChecklist} from '../models';
import {SupplyChecklistRepository} from './supply-checklist.repository';

export class SupplySectionRepository extends DefaultCrudRepository<
  SupplySection,
  typeof SupplySection.prototype.id,
  SupplySectionRelations
> {

  public readonly supplyChecklists: HasManyRepositoryFactory<SupplyChecklist, typeof SupplySection.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('SupplyChecklistRepository') protected supplyChecklistRepositoryGetter: Getter<SupplyChecklistRepository>,
  ) {
    super(SupplySection, dataSource);
    this.supplyChecklists = this.createHasManyRepositoryFactoryFor('supplyChecklists', supplyChecklistRepositoryGetter,);
    this.registerInclusionResolver('supplyChecklists', this.supplyChecklists.inclusionResolver);
  }
}
