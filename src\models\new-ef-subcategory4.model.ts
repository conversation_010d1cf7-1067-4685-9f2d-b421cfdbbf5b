import {Entity, model, property} from '@loopback/repository';

@model()
export class NewEfSubcategory4 extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  extra?: string;

  @property({
    type: 'number',
  })
  newEfSubcategory3Id?: number;

  constructor(data?: Partial<NewEfSubcategory4>) {
    super(data);
  }
}

export interface NewEfSubcategory4Relations {
  // describe navigational properties here
}

export type NewEfSubcategory4WithRelations = NewEfSubcategory4 & NewEfSubcategory4Relations;
