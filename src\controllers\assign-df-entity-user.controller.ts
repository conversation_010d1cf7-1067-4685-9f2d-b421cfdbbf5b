import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignDfEntityUser} from '../models';
import {AssignDfEntityUserRepository} from '../repositories';

export class AssignDfEntityUserController {
  constructor(
    @repository(AssignDfEntityUserRepository)
    public assignDfEntityUserRepository : AssignDfEntityUserRepository,
  ) {}

  @post('/assign-df-entity-users')
  @response(200, {
    description: 'AssignDfEntityUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDfEntityUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntityUser, {
            title: 'NewAssignDfEntityUser',
            exclude: ['id'],
          }),
        },
      },
    })
    assignDfEntityUser: Omit<AssignDfEntityUser, 'id'>,
  ): Promise<AssignDfEntityUser> {
    return this.assignDfEntityUserRepository.create(assignDfEntityUser);
  }

  @get('/assign-df-entity-users/count')
  @response(200, {
    description: 'AssignDfEntityUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDfEntityUser) where?: Where<AssignDfEntityUser>,
  ): Promise<Count> {
    return this.assignDfEntityUserRepository.count(where);
  }

  @get('/assign-df-entity-users')
  @response(200, {
    description: 'Array of AssignDfEntityUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDfEntityUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDfEntityUser) filter?: Filter<AssignDfEntityUser>,
  ): Promise<AssignDfEntityUser[]> {
    return this.assignDfEntityUserRepository.find(filter);
  }

  @patch('/assign-df-entity-users')
  @response(200, {
    description: 'AssignDfEntityUser PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntityUser, {partial: true}),
        },
      },
    })
    assignDfEntityUser: AssignDfEntityUser,
    @param.where(AssignDfEntityUser) where?: Where<AssignDfEntityUser>,
  ): Promise<Count> {
    return this.assignDfEntityUserRepository.updateAll(assignDfEntityUser, where);
  }

  @get('/assign-df-entity-users/{id}')
  @response(200, {
    description: 'AssignDfEntityUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDfEntityUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDfEntityUser, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDfEntityUser>
  ): Promise<AssignDfEntityUser> {
    return this.assignDfEntityUserRepository.findById(id, filter);
  }

  @patch('/assign-df-entity-users/{id}')
  @response(204, {
    description: 'AssignDfEntityUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntityUser, {partial: true}),
        },
      },
    })
    assignDfEntityUser: AssignDfEntityUser,
  ): Promise<void> {
    await this.assignDfEntityUserRepository.updateById(id, assignDfEntityUser);
  }

  @put('/assign-df-entity-users/{id}')
  @response(204, {
    description: 'AssignDfEntityUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignDfEntityUser: AssignDfEntityUser,
  ): Promise<void> {
    await this.assignDfEntityUserRepository.replaceById(id, assignDfEntityUser);
  }

  @del('/assign-df-entity-users/{id}')
  @response(204, {
    description: 'AssignDfEntityUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDfEntityUserRepository.deleteById(id);
  }
}
