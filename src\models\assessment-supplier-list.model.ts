import {Entity, model, property} from '@loopback/repository';

@model()
export class AssessmentSupplierList extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  supplierId?: number;
  @property({
    type: 'array',
    itemType: 'number'
  })
  auditor_ids?: any[];

  @property({
    type: 'number',
  })
  srfId?: number;

  @property({
    type: 'boolean',
    jsonSchema: {
      nullable: true,
    }
  })
  status?: boolean | null;

  @property({
    type: 'number',
  })
  mailSent?: number;

  @property({
    type: 'string',
  })
  supplierAssessmentAssignmentId?: string;


  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  created_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  created_by?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_by?: number | null;


  constructor(data?: Partial<AssessmentSupplierList>) {
    super(data);
  }
}

export interface AssessmentSupplierListRelations {
  // describe navigational properties here
}

export type AssessmentSupplierListWithRelations = AssessmentSupplierList & AssessmentSupplierListRelations;
