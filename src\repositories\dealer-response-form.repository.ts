import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DealerResponseForm, DealerResponseFormRelations} from '../models';

export class DealerResponseFormRepository extends DefaultCrudRepository<
  DealerResponseForm,
  typeof DealerResponseForm.prototype.id,
  DealerResponseFormRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(DealerResponseForm, dataSource);
  }
}
