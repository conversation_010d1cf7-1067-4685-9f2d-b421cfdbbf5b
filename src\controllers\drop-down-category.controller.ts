import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DropDownCategory} from '../models';
import {DropDownCategoryRepository} from '../repositories';

export class DropDownCategoryController {
  constructor(
    @repository(DropDownCategoryRepository)
    public DropDownCategoryRepository: DropDownCategoryRepository,
  ) { }

  @post('/drop-down-categories')
  @response(200, {
    description: 'DropDownCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(DropDownCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DropDownCategory, {
            title: 'NewDropDownCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    DropDownCategory: Omit<DropDownCategory, 'id'>,
  ): Promise<DropDownCategory> {
    return this.DropDownCategoryRepository.create(DropDownCategory);
  }

  @get('/drop-down-categories/count')
  @response(200, {
    description: 'DropDownCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DropDownCategory) where?: Where<DropDownCategory>,
  ): Promise<Count> {
    return this.DropDownCategoryRepository.count(where);
  }

  @get('/drop-down-categories')
  @response(200, {
    description: 'Array of DropDownCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DropDownCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DropDownCategory) filter?: Filter<DropDownCategory>,
  ): Promise<DropDownCategory[]> {
    return this.DropDownCategoryRepository.find(filter);
  }

  @patch('/drop-down-categories')
  @response(200, {
    description: 'DropDownCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DropDownCategory, {partial: true}),
        },
      },
    })
    DropDownCategory: DropDownCategory,
    @param.where(DropDownCategory) where?: Where<DropDownCategory>,
  ): Promise<Count> {
    return this.DropDownCategoryRepository.updateAll(DropDownCategory, where);
  }

  @get('/drop-down-categories/{id}')
  @response(200, {
    description: 'DropDownCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DropDownCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DropDownCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<DropDownCategory>
  ): Promise<DropDownCategory> {
    return this.DropDownCategoryRepository.findById(id, filter);
  }

  @patch('/drop-down-categories/{id}')
  @response(204, {
    description: 'DropDownCategory PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DropDownCategory, {partial: true}),
        },
      },
    })
    DropDownCategory: DropDownCategory,
  ): Promise<void> {
    await this.DropDownCategoryRepository.updateById(id, DropDownCategory);
  }

  @put('/drop-down-categories/{id}')
  @response(204, {
    description: 'DropDownCategory PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() DropDownCategory: DropDownCategory,
  ): Promise<void> {
    await this.DropDownCategoryRepository.replaceById(id, DropDownCategory);
  }

  @del('/drop-down-categories/{id}')
  @response(204, {
    description: 'DropDownCategory DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.DropDownCategoryRepository.deleteById(id);
  }
}
