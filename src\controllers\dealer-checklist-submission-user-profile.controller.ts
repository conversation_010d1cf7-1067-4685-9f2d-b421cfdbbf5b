import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  DealerChecklistSubmission,
  UserProfile,
} from '../models';
import {DealerChecklistSubmissionRepository} from '../repositories';

export class DealerChecklistSubmissionUserProfileController {
  constructor(
    @repository(DealerChecklistSubmissionRepository)
    public dealerChecklistSubmissionRepository: DealerChecklistSubmissionRepository,
  ) { }

  @get('/dealer-checklist-submissions/{id}/user-profile', {
    responses: {
      '200': {
        description: 'UserProfile belonging to DealerChecklistSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(UserProfile),
          },
        },
      },
    },
  })
  async getUserProfile(
    @param.path.number('id') id: typeof DealerChecklistSubmission.prototype.id,
  ): Promise<UserProfile> {
    return this.dealerChecklistSubmissionRepository.dealer(id);
  }
}
