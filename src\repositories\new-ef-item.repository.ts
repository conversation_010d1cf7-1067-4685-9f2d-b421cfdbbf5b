import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEfItem, NewEfItemRelations, NewEfSubcategory1, NewEfSubcategory2, NewEfSubcategory3, NewEfSubcategory4} from '../models';
import {NewEfSubcategory1Repository} from './new-ef-subcategory1.repository';
import {NewEfSubcategory2Repository} from './new-ef-subcategory2.repository';
import {NewEfSubcategory3Repository} from './new-ef-subcategory3.repository';
import {NewEfSubcategory4Repository} from './new-ef-subcategory4.repository';

export class NewEfItemRepository extends DefaultCrudRepository<
  NewEfItem,
  typeof NewEfItem.prototype.id,
  NewEfItemRelations
> {

  public readonly subcat1: BelongsToAccessor<NewEfSubcategory1, typeof NewEfItem.prototype.id>;

  public readonly subcat2: BelongsToAccessor<NewEfSubcategory2, typeof NewEfItem.prototype.id>;

  public readonly subcat3: BelongsToAccessor<NewEfSubcategory3, typeof NewEfItem.prototype.id>;

  public readonly subcat4: BelongsToAccessor<NewEfSubcategory4, typeof NewEfItem.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfSubcategory1Repository') protected newEfSubcategory1RepositoryGetter: Getter<NewEfSubcategory1Repository>, @repository.getter('NewEfSubcategory2Repository') protected newEfSubcategory2RepositoryGetter: Getter<NewEfSubcategory2Repository>, @repository.getter('NewEfSubcategory3Repository') protected newEfSubcategory3RepositoryGetter: Getter<NewEfSubcategory3Repository>, @repository.getter('NewEfSubcategory4Repository') protected newEfSubcategory4RepositoryGetter: Getter<NewEfSubcategory4Repository>,
  ) {
    super(NewEfItem, dataSource);
    this.subcat4 = this.createBelongsToAccessorFor('subcat4', newEfSubcategory4RepositoryGetter,);
    this.registerInclusionResolver('subcat4', this.subcat4.inclusionResolver);
    this.subcat3 = this.createBelongsToAccessorFor('subcat3', newEfSubcategory3RepositoryGetter,);
    this.registerInclusionResolver('subcat3', this.subcat3.inclusionResolver);
    this.subcat2 = this.createBelongsToAccessorFor('subcat2', newEfSubcategory2RepositoryGetter,);
    this.registerInclusionResolver('subcat2', this.subcat2.inclusionResolver);
    this.subcat1 = this.createBelongsToAccessorFor('subcat1', newEfSubcategory1RepositoryGetter,);
    this.registerInclusionResolver('subcat1', this.subcat1.inclusionResolver);
  }
}
