import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ClientEfCategoryMapping,
  GhgCategory,
} from '../models';
import {ClientEfCategoryMappingRepository} from '../repositories';

export class ClientEfCategoryMappingGhgCategoryController {
  constructor(
    @repository(ClientEfCategoryMappingRepository)
    public clientEfCategoryMappingRepository: ClientEfCategoryMappingRepository,
  ) { }

  @get('/client-ef-category-mappings/{id}/ghg-category', {
    responses: {
      '200': {
        description: 'GhgCategory belonging to ClientEfCategoryMapping',
        content: {
          'application/json': {
            schema: getModelSchemaRef(GhgCategory),
          },
        },
      },
    },
  })
  async getGhgCategory(
    @param.path.number('id') id: typeof ClientEfCategoryMapping.prototype.id,
  ): Promise<GhgCategory> {
    return this.clientEfCategoryMappingRepository.efGhgCat(id);
  }
}
