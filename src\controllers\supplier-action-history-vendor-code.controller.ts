import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierActionHistory,
  VendorCode,
} from '../models';
import {SupplierActionHistoryRepository} from '../repositories';

export class SupplierActionHistoryVendorCodeController {
  constructor(
    @repository(SupplierActionHistoryRepository)
    public supplierActionHistoryRepository: SupplierActionHistoryRepository,
  ) { }

  @get('/supplier-action-histories/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to SupplierActionHistory',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof SupplierActionHistory.prototype.id,
  ): Promise<VendorCode> {
    return this.supplierActionHistoryRepository.vendor(id);
  }
}
