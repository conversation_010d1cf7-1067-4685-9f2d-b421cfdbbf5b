import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SubQuestion} from '../models';
import {SubQuestionRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SubQuestionController {
  constructor(
    @repository(SubQuestionRepository)
    public subQuestionRepository : SubQuestionRepository,
  ) {}

  @post('/sub-questions')
  @response(200, {
    description: 'SubQuestion model instance',
    content: {'application/json': {schema: getModelSchemaRef(SubQuestion)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubQuestion, {
            title: 'NewSubQuestion',
            exclude: ['id'],
          }),
        },
      },
    })
    subQuestion: Omit<SubQuestion, 'id'>,
  ): Promise<SubQuestion> {
    return this.subQuestionRepository.create(subQuestion);
  }

  @get('/sub-questions/count')
  @response(200, {
    description: 'SubQuestion model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SubQuestion) where?: Where<SubQuestion>,
  ): Promise<Count> {
    return this.subQuestionRepository.count(where);
  }

  @get('/sub-questions')
  @response(200, {
    description: 'Array of SubQuestion model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SubQuestion, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SubQuestion) filter?: Filter<SubQuestion>,
  ): Promise<SubQuestion[]> {
    return this.subQuestionRepository.find(filter);
  }

  // @patch('/sub-questions')
  // @response(200, {
  //   description: 'SubQuestion PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubQuestion, {partial: true}),
  //       },
  //     },
  //   })
  //   subQuestion: SubQuestion,
  //   @param.where(SubQuestion) where?: Where<SubQuestion>,
  // ): Promise<Count> {
  //   return this.subQuestionRepository.updateAll(subQuestion, where);
  // }

  @get('/sub-questions/{id}')
  @response(200, {
    description: 'SubQuestion model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SubQuestion, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SubQuestion, {exclude: 'where'}) filter?: FilterExcludingWhere<SubQuestion>
  ): Promise<SubQuestion> {
    return this.subQuestionRepository.findById(id, filter);
  }

  @patch('/sub-questions/{id}')
  @response(204, {
    description: 'SubQuestion PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubQuestion, {partial: true}),
        },
      },
    })
    subQuestion: SubQuestion,
  ): Promise<void> {
    await this.subQuestionRepository.updateById(id, subQuestion);
  }

  @put('/sub-questions/{id}')
  @response(204, {
    description: 'SubQuestion PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() subQuestion: SubQuestion,
  ): Promise<void> {
    await this.subQuestionRepository.replaceById(id, subQuestion);
  }

  @del('/sub-questions/{id}')
  @response(204, {
    description: 'SubQuestion DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.subQuestionRepository.deleteById(id);
  }
}
