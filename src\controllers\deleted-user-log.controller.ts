import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DeleteUserLog} from '../models';
import {DeleteUserLogRepository} from '../repositories';

export class DeletedUserLogController {
  constructor(
    @repository(DeleteUserLogRepository)
    public deleteUserLogRepository : DeleteUserLogRepository,
  ) {}

  @post('/delete-user-logs')
  @response(200, {
    description: 'DeleteUserLog model instance',
    content: {'application/json': {schema: getModelSchemaRef(DeleteUserLog)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DeleteUserLog, {
            title: 'NewDeleteUserLog',
            exclude: ['id'],
          }),
        },
      },
    })
    deleteUserLog: Omit<DeleteUserLog, 'id'>,
  ): Promise<DeleteUserLog> {
    return this.deleteUserLogRepository.create(deleteUserLog);
  }

  @get('/delete-user-logs/count')
  @response(200, {
    description: 'DeleteUserLog model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DeleteUserLog) where?: Where<DeleteUserLog>,
  ): Promise<Count> {
    return this.deleteUserLogRepository.count(where);
  }

  @get('/delete-user-logs')
  @response(200, {
    description: 'Array of DeleteUserLog model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DeleteUserLog, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DeleteUserLog) filter?: Filter<DeleteUserLog>,
  ): Promise<DeleteUserLog[]> {
    return this.deleteUserLogRepository.find(filter);
  }

  @patch('/delete-user-logs')
  @response(200, {
    description: 'DeleteUserLog PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DeleteUserLog, {partial: true}),
        },
      },
    })
    deleteUserLog: DeleteUserLog,
    @param.where(DeleteUserLog) where?: Where<DeleteUserLog>,
  ): Promise<Count> {
    return this.deleteUserLogRepository.updateAll(deleteUserLog, where);
  }

  @get('/delete-user-logs/{id}')
  @response(200, {
    description: 'DeleteUserLog model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DeleteUserLog, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DeleteUserLog, {exclude: 'where'}) filter?: FilterExcludingWhere<DeleteUserLog>
  ): Promise<DeleteUserLog> {
    return this.deleteUserLogRepository.findById(id, filter);
  }

  @patch('/delete-user-logs/{id}')
  @response(204, {
    description: 'DeleteUserLog PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DeleteUserLog, {partial: true}),
        },
      },
    })
    deleteUserLog: DeleteUserLog,
  ): Promise<void> {
    await this.deleteUserLogRepository.updateById(id, deleteUserLog);
  }

  @put('/delete-user-logs/{id}')
  @response(204, {
    description: 'DeleteUserLog PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() deleteUserLog: DeleteUserLog,
  ): Promise<void> {
    await this.deleteUserLogRepository.replaceById(id, deleteUserLog);
  }

  @del('/delete-user-logs/{id}')
  @response(204, {
    description: 'DeleteUserLog DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.deleteUserLogRepository.deleteById(id);
  }
}
