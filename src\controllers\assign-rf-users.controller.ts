import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignRfUsers} from '../models';
import {AssignRfUsersRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class AssignRfUsersController {
  constructor(
    @repository(AssignRfUsersRepository)
    public assignRfUsersRepository : AssignRfUsersRepository,
  ) {}

  @post('/assign-rf-users')
  @response(200, {
    description: 'AssignRfUsers model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignRfUsers)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignRfUsers, {
            title: 'NewAssignRfUsers',
            exclude: ['id'],
          }),
        },
      },
    })
    assignRfUsers: Omit<AssignRfUsers, 'id'>,
  ): Promise<AssignRfUsers> {
    return this.assignRfUsersRepository.create(assignRfUsers);
  }

  @get('/assign-rf-users/count')
  @response(200, {
    description: 'AssignRfUsers model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignRfUsers) where?: Where<AssignRfUsers>,
  ): Promise<Count> {
    return this.assignRfUsersRepository.count(where);
  }

  @get('/assign-rf-users')
  @response(200, {
    description: 'Array of AssignRfUsers model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignRfUsers, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignRfUsers) filter?: Filter<AssignRfUsers>,
  ): Promise<AssignRfUsers[]> {
    return this.assignRfUsersRepository.find(filter);
  }

  // @patch('/assign-rf-users')
  // @response(200, {
  //   description: 'AssignRfUsers PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignRfUsers, {partial: true}),
  //       },
  //     },
  //   })
  //   assignRfUsers: AssignRfUsers,
  //   @param.where(AssignRfUsers) where?: Where<AssignRfUsers>,
  // ): Promise<Count> {
  //   return this.assignRfUsersRepository.updateAll(assignRfUsers, where);
  // }

  @get('/assign-rf-users/{id}')
  @response(200, {
    description: 'AssignRfUsers model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignRfUsers, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignRfUsers, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignRfUsers>
  ): Promise<AssignRfUsers> {
    return this.assignRfUsersRepository.findById(id, filter);
  }

  @patch('/assign-rf-users/{id}')
  @response(204, {
    description: 'AssignRfUsers PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignRfUsers, {partial: true}),
        },
      },
    })
    assignRfUsers: AssignRfUsers,
  ): Promise<void> {
    await this.assignRfUsersRepository.updateById(id, assignRfUsers);
  }

  @put('/assign-rf-users/{id}')
  @response(204, {
    description: 'AssignRfUsers PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignRfUsers: AssignRfUsers,
  ): Promise<void> {
    await this.assignRfUsersRepository.replaceById(id, assignRfUsers);
  }

  @del('/assign-rf-users/{id}')
  @response(204, {
    description: 'AssignRfUsers DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignRfUsersRepository.deleteById(id);
  }
}
