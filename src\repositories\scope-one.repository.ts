import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ScopeOne, ScopeOneRelations} from '../models';

export class ScopeOneRepository extends DefaultCrudRepository<
  ScopeOne,
  typeof ScopeOne.prototype.id,
  ScopeOneRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(ScopeOne, dataSource);
  }
}
