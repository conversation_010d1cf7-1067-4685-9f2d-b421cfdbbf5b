import {Entity, hasMany, model, property} from '@loopback/repository';
import {DdCategoryOne} from './dd-category-one.model';

@model()
export class DropDownCategory extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @hasMany(() => DdCategoryOne)
  ddCategoryOnes: DdCategoryOne[];

  constructor(data?: Partial<DropDownCategory>) {
    super(data);
  }
}

export interface DropDownCategoryRelations {
  // describe navigational properties here
}

export type DropDownCategoryWithRelations = DropDownCategory & DropDownCategoryRelations;
