import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ApplicationList} from '../models';
import {ApplicationListRepository} from '../repositories';

export class ApplicationListController {
  constructor(
    @repository(ApplicationListRepository)
    public applicationListRepository : ApplicationListRepository,
  ) {}

  @post('/application-lists')
  @response(200, {
    description: 'ApplicationList model instance',
    content: {'application/json': {schema: getModelSchemaRef(ApplicationList)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ApplicationList, {
            title: 'NewApplicationList',
            exclude: ['id'],
          }),
        },
      },
    })
    applicationList: Omit<ApplicationList, 'id'>,
  ): Promise<ApplicationList> {
    return this.applicationListRepository.create(applicationList);
  }

  @get('/application-lists/count')
  @response(200, {
    description: 'ApplicationList model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ApplicationList) where?: Where<ApplicationList>,
  ): Promise<Count> {
    return this.applicationListRepository.count(where);
  }

  @get('/application-lists')
  @response(200, {
    description: 'Array of ApplicationList model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ApplicationList, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ApplicationList) filter?: Filter<ApplicationList>,
  ): Promise<ApplicationList[]> {
    return this.applicationListRepository.find(filter);
  }

  @patch('/application-lists')
  @response(200, {
    description: 'ApplicationList PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ApplicationList, {partial: true}),
        },
      },
    })
    applicationList: ApplicationList,
    @param.where(ApplicationList) where?: Where<ApplicationList>,
  ): Promise<Count> {
    return this.applicationListRepository.updateAll(applicationList, where);
  }

  @get('/application-lists/{id}')
  @response(200, {
    description: 'ApplicationList model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ApplicationList, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ApplicationList, {exclude: 'where'}) filter?: FilterExcludingWhere<ApplicationList>
  ): Promise<ApplicationList> {
    return this.applicationListRepository.findById(id, filter);
  }

  @patch('/application-lists/{id}')
  @response(204, {
    description: 'ApplicationList PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ApplicationList, {partial: true}),
        },
      },
    })
    applicationList: ApplicationList,
  ): Promise<void> {
    await this.applicationListRepository.updateById(id, applicationList);
  }

  @put('/application-lists/{id}')
  @response(204, {
    description: 'ApplicationList PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() applicationList: ApplicationList,
  ): Promise<void> {
    await this.applicationListRepository.replaceById(id, applicationList);
  }

  @del('/application-lists/{id}')
  @response(204, {
    description: 'ApplicationList DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.applicationListRepository.deleteById(id);
  }
}
