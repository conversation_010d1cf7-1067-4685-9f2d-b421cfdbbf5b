import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {QuantitativeDpReport, QuantitativeDpReportRelations} from '../models';

export class QuantitativeDpReportRepository extends DefaultCrudRepository<
  QuantitativeDpReport,
  typeof QuantitativeDpReport.prototype.id,
  QuantitativeDpReportRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(QuantitativeDpReport, dataSource);
  }
}
