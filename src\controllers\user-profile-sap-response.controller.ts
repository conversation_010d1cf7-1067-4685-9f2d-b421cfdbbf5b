import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  SapResponse,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileSapResponseController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/sap-responses', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SapResponse',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SapResponse)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SapResponse>,
  ): Promise<SapResponse[]> {
    return this.userProfileRepository.sapResponses(id).find(filter);
  }

  @post('/user-profiles/{id}/sap-responses', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SapResponse)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapResponse, {
            title: 'NewSapResponseInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) sapResponse: Omit<SapResponse, 'id'>,
  ): Promise<SapResponse> {
    return this.userProfileRepository.sapResponses(id).create(sapResponse);
  }

  @patch('/user-profiles/{id}/sap-responses', {
    responses: {
      '200': {
        description: 'UserProfile.SapResponse PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapResponse, {partial: true}),
        },
      },
    })
    sapResponse: Partial<SapResponse>,
    @param.query.object('where', getWhereSchemaFor(SapResponse)) where?: Where<SapResponse>,
  ): Promise<Count> {
    return this.userProfileRepository.sapResponses(id).patch(sapResponse, where);
  }

  @del('/user-profiles/{id}/sap-responses', {
    responses: {
      '200': {
        description: 'UserProfile.SapResponse DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SapResponse)) where?: Where<SapResponse>,
  ): Promise<Count> {
    return this.userProfileRepository.sapResponses(id).delete(where);
  }
}
