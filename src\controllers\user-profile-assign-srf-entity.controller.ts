import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  AssignSrfEntity,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileAssignSrfEntityController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-srf-entities', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignSrfEntity',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignSrfEntity)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignSrfEntity>,
  ): Promise<AssignSrfEntity[]> {
    return this.userProfileRepository.assignSrfEntities(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-srf-entities', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignSrfEntity)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntity, {
            title: 'NewAssignSrfEntityInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignSrfEntity: Omit<AssignSrfEntity, 'id'>,
  ): Promise<AssignSrfEntity> {
    return this.userProfileRepository.assignSrfEntities(id).create(assignSrfEntity);
  }

  @patch('/user-profiles/{id}/assign-srf-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignSrfEntity PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntity, {partial: true}),
        },
      },
    })
    assignSrfEntity: Partial<AssignSrfEntity>,
    @param.query.object('where', getWhereSchemaFor(AssignSrfEntity)) where?: Where<AssignSrfEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignSrfEntities(id).patch(assignSrfEntity, where);
  }

  @del('/user-profiles/{id}/assign-srf-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignSrfEntity DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignSrfEntity)) where?: Where<AssignSrfEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignSrfEntities(id).delete(where);
  }
}
