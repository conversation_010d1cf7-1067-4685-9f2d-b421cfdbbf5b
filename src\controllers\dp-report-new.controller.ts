import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DpReportNew} from '../models';
import {DpReportNewRepository} from '../repositories';

export class DpReportNewController {
  constructor(
    @repository(DpReportNewRepository)
    public dpReportNewRepository: DpReportNewRepository,
  ) { }

  @post('/dp-report-news')
  @response(200, {
    description: 'DpReportNew model instance',
    content: {'application/json': {schema: getModelSchemaRef(DpReportNew)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DpReportNew, {
            title: 'NewDpReportNew',
            exclude: ['id'],
          }),
        },
      },
    })
    dpReportNew: Omit<DpReportNew, 'id'>,
  ): Promise<DpReportNew> {
    return this.dpReportNewRepository.create(dpReportNew);
  }

  @get('/dp-report-news/count')
  @response(200, {
    description: 'DpReportNew model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DpReportNew) where?: Where<DpReportNew>,
  ): Promise<Count> {
    return this.dpReportNewRepository.count(where);
  }

  @get('/dp-report-news')
  @response(200, {
    description: 'Array of DpReportNew model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DpReportNew, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DpReportNew) filter?: Filter<DpReportNew>,
  ): Promise<DpReportNew[]> {
    return this.dpReportNewRepository.find(filter);
  }

  // @patch('/dp-report-news')
  // @response(200, {
  //   description: 'DpReportNew PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(DpReportNew, {partial: true}),
  //       },
  //     },
  //   })
  //   dpReportNew: DpReportNew,
  //   @param.where(DpReportNew) where?: Where<DpReportNew>,
  // ): Promise<Count> {
  //   return this.dpReportNewRepository.updateAll(dpReportNew, where);
  // }

  @get('/dp-report-news/{id}')
  @response(200, {
    description: 'DpReportNew model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DpReportNew, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DpReportNew, {exclude: 'where'}) filter?: FilterExcludingWhere<DpReportNew>
  ): Promise<DpReportNew> {
    return this.dpReportNewRepository.findById(id, filter);
  }

  @patch('/dp-report-news/{id}')
  @response(204, {
    description: 'DpReportNew PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DpReportNew, {partial: true}),
        },
      },
    })
    dpReportNew: DpReportNew,
  ): Promise<void> {
    await this.dpReportNewRepository.updateById(id, dpReportNew);
  }

  @put('/dp-report-news/{id}')
  @response(204, {
    description: 'DpReportNew PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() dpReportNew: DpReportNew,
  ): Promise<void> {
    await this.dpReportNewRepository.replaceById(id, dpReportNew);
  }

  @del('/dp-report-news/{id}')
  @response(204, {
    description: 'DpReportNew DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.dpReportNewRepository.deleteById(id);
  }

  @del('/dp-reports/by/submitId/{id}')
  @response(204, {
    description: 'DpReport DELETE success',
  })
  async deleteBySubmitId(@param.path.number('id') id: number): Promise<void> {
    await this.dpReportNewRepository.deleteAll({submitId: id})
  }
}
