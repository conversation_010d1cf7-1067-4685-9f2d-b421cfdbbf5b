import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {QlListingFilter, QlListingFilterRelations} from '../models';

export class QlListingFilterRepository extends DefaultCrudRepository<
  QlListingFilter,
  typeof QlListingFilter.prototype.id,
  QlListingFilterRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(QlListingFilter, dataSource);
  }
}
