import {Entity, model, property} from '@loopback/repository';

@model()
export class PolicyProcedure extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  section?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  documents?: any[];

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  date?: string;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  dateOfRenewal: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  dateOfRevision: string | null;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<PolicyProcedure>) {
    super(data);
  }
}

export interface PolicyProcedureRelations {
  // describe navigational properties here
}

export type PolicyProcedureWithRelations = PolicyProcedure & PolicyProcedureRelations;
