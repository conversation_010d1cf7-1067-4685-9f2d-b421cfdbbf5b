import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssessmentSubSection3} from '../models';
import {AssessmentSubSection3Repository} from '../repositories';

export class AssessmentSubSection3Controller {
  constructor(
    @repository(AssessmentSubSection3Repository)
    public assessmentSubSection3Repository : AssessmentSubSection3Repository,
  ) {}

  @post('/assessment-sub-section3s')
  @response(200, {
    description: 'AssessmentSubSection3 model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssessmentSubSection3)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection3, {
            title: 'NewAssessmentSubSection3',
            exclude: ['id'],
          }),
        },
      },
    })
    assessmentSubSection3: Omit<AssessmentSubSection3, 'id'>,
  ): Promise<AssessmentSubSection3> {
    return this.assessmentSubSection3Repository.create(assessmentSubSection3);
  }

  @get('/assessment-sub-section3s/count')
  @response(200, {
    description: 'AssessmentSubSection3 model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssessmentSubSection3) where?: Where<AssessmentSubSection3>,
  ): Promise<Count> {
    return this.assessmentSubSection3Repository.count(where);
  }

  @get('/assessment-sub-section3s')
  @response(200, {
    description: 'Array of AssessmentSubSection3 model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssessmentSubSection3, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssessmentSubSection3) filter?: Filter<AssessmentSubSection3>,
  ): Promise<AssessmentSubSection3[]> {
    return this.assessmentSubSection3Repository.find(filter);
  }

  @patch('/assessment-sub-section3s')
  @response(200, {
    description: 'AssessmentSubSection3 PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection3, {partial: true}),
        },
      },
    })
    assessmentSubSection3: AssessmentSubSection3,
    @param.where(AssessmentSubSection3) where?: Where<AssessmentSubSection3>,
  ): Promise<Count> {
    return this.assessmentSubSection3Repository.updateAll(assessmentSubSection3, where);
  }

  @get('/assessment-sub-section3s/{id}')
  @response(200, {
    description: 'AssessmentSubSection3 model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssessmentSubSection3, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AssessmentSubSection3, {exclude: 'where'}) filter?: FilterExcludingWhere<AssessmentSubSection3>
  ): Promise<AssessmentSubSection3> {
    return this.assessmentSubSection3Repository.findById(id, filter);
  }

  @patch('/assessment-sub-section3s/{id}')
  @response(204, {
    description: 'AssessmentSubSection3 PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection3, {partial: true}),
        },
      },
    })
    assessmentSubSection3: AssessmentSubSection3,
  ): Promise<void> {
    await this.assessmentSubSection3Repository.updateById(id, assessmentSubSection3);
  }

  @put('/assessment-sub-section3s/{id}')
  @response(204, {
    description: 'AssessmentSubSection3 PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() assessmentSubSection3: AssessmentSubSection3,
  ): Promise<void> {
    await this.assessmentSubSection3Repository.replaceById(id, assessmentSubSection3);
  }

  @del('/assessment-sub-section3s/{id}')
  @response(204, {
    description: 'AssessmentSubSection3 DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.assessmentSubSection3Repository.deleteById(id);
  }
}
