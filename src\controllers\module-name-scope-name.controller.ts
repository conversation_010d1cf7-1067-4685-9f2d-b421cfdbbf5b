import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  ModuleName,
  ScopeName,
} from '../models';
import {ModuleNameRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ModuleNameScopeNameController {
  constructor(
    @repository(ModuleNameRepository) protected moduleNameRepository: ModuleNameRepository,
  ) { }

  @get('/module-names/{id}/scope-names', {
    responses: {
      '200': {
        description: 'Array of ModuleName has many ScopeName',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ScopeName)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ScopeName>,
  ): Promise<ScopeName[]> {
    return this.moduleNameRepository.scopeNames(id).find(filter);
  }

  @post('/module-names/{id}/scope-names', {
    responses: {
      '200': {
        description: 'ModuleName model instance',
        content: {'application/json': {schema: getModelSchemaRef(ScopeName)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof ModuleName.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeName, {
            title: 'NewScopeNameInModuleName',
            exclude: ['id'],
            optional: ['moduleNameId']
          }),
        },
      },
    }) scopeName: Omit<ScopeName, 'id'>,
  ): Promise<ScopeName> {
    return this.moduleNameRepository.scopeNames(id).create(scopeName);
  }

  // @patch('/module-names/{id}/scope-names', {
  //   responses: {
  //     '200': {
  //       description: 'ModuleName.ScopeName PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ScopeName, {partial: true}),
  //       },
  //     },
  //   })
  //   scopeName: Partial<ScopeName>,
  //   @param.query.object('where', getWhereSchemaFor(ScopeName)) where?: Where<ScopeName>,
  // ): Promise<Count> {
  //   return this.moduleNameRepository.scopeNames(id).patch(scopeName, where);
  // }

  // @del('/module-names/{id}/scope-names', {
  //   responses: {
  //     '200': {
  //       description: 'ModuleName.ScopeName DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(ScopeName)) where?: Where<ScopeName>,
  // ): Promise<Count> {
  //   return this.moduleNameRepository.scopeNames(id).delete(where);
  // }
  
}
