import {Entity, model, property} from '@loopback/repository';

@model()
export class ManagementQuestion extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'any',
  })
  values?: any;

  @property({
    type: 'string',
  })
  created?: string;


  constructor(data?: Partial<ManagementQuestion>) {
    super(data);
  }
}

export interface ManagementQuestionRelations {
  // describe navigational properties here
}

export type ManagementQuestionWithRelations = ManagementQuestion & ManagementQuestionRelations;
