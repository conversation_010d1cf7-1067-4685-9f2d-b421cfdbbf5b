import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SubSurvey,
  SaveSurvey,
} from '../models';
import {SubSurveyRepository} from '../repositories';

export class SubSurveySaveSurveyController {
  constructor(
    @repository(SubSurveyRepository) protected subSurveyRepository: SubSurveyRepository,
  ) { }

  @get('/sub-surveys/{id}/save-survey', {
    responses: {
      '200': {
        description: 'SubSurvey has one SaveSurvey',
        content: {
          'application/json': {
            schema: getModelSchemaRef(SaveSurvey),
          },
        },
      },
    },
  })
  async get(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SaveSurvey>,
  ): Promise<SaveSurvey> {
    return this.subSurveyRepository.saveSurvey(id).get(filter);
  }

  @post('/sub-surveys/{id}/save-survey', {
    responses: {
      '200': {
        description: 'SubSurvey model instance',
        content: {'application/json': {schema: getModelSchemaRef(SaveSurvey)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof SubSurvey.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SaveSurvey, {
            title: 'NewSaveSurveyInSubSurvey',
            exclude: ['id'],
            optional: ['subSurveyId']
          }),
        },
      },
    }) saveSurvey: Omit<SaveSurvey, 'id'>,
  ): Promise<SaveSurvey> {
    return this.subSurveyRepository.saveSurvey(id).create(saveSurvey);
  }

  @patch('/sub-surveys/{id}/save-survey', {
    responses: {
      '200': {
        description: 'SubSurvey.SaveSurvey PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SaveSurvey, {partial: true}),
        },
      },
    })
    saveSurvey: Partial<SaveSurvey>,
    @param.query.object('where', getWhereSchemaFor(SaveSurvey)) where?: Where<SaveSurvey>,
  ): Promise<Count> {
    return this.subSurveyRepository.saveSurvey(id).patch(saveSurvey, where);
  }

  @del('/sub-surveys/{id}/save-survey', {
    responses: {
      '200': {
        description: 'SubSurvey.SaveSurvey DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SaveSurvey)) where?: Where<SaveSurvey>,
  ): Promise<Count> {
    return this.subSurveyRepository.saveSurvey(id).delete(where);
  }
}
