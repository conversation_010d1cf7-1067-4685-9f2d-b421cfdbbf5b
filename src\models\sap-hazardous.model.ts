import {Entity, model, property} from '@loopback/repository';

@model()
export class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier0_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier1_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier2_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier3_id?: number | null;

  @property({
    type: 'number',
  })
  level?: number; // Stores the mapped value of Qty

  @property({
    type: 'number',
  })
  locationId?: number; // Stores the mapped value of Qty

  // @property({
  //   type: 'string',
  // })
  // StorageLocation?: string;

  @property({
    type: 'string',
  })
  Location?: string;

  @property({
    type: 'string',
  })
  Date?: string;

  @property({
    type: 'string',
  })
  Category?: string;

  // @property({
  //   type: 'string',
  // })
  // MaterialNumber?: string;

  @property({
    type: 'string',
  })
  DPAN0041?: string; // Added this property

  @property({
    type: 'string',
  })
  UOM?: string; // Added this property

  @property({
    type: 'string',
  })
  DPAN0500?: string;

  @property({
    type: 'string',
  })
  Type?: string;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  fetched_on?: string | null;

  @property({
    type: 'number',
  })
  userProfileId?: number; // Stores the mapped value of Qty

  constructor(data?: Partial<SapHazardous>) {
    super(data);
  }
}

export interface SapHazardousRelations {
  // describe navigational properties here
}

export type SapHazardousWithRelations = SapHazardous & SapHazardousRelations;
