// Copyright IBM Corp. 2019,2020. All Rights Reserved.
// Node module: loopback4-example-shopping
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT


import isemail from 'isemail';
import { HttpErrors } from '@loopback/rest';


export type Credentials = {
    email: string;
    password: string;
};

export function validateCredentials(credentials: Credentials) {
    // Validate Email
    if (!isemail.validate(credentials.email)) {
        throw new HttpErrors.UnprocessableEntity('invalid email');
    }

    // Validate Password Length
    if (!credentials.password || credentials.password.length < 8) {
        throw new HttpErrors.UnprocessableEntity(
            'password must be minimum 8 characters',
        );
    }
}

