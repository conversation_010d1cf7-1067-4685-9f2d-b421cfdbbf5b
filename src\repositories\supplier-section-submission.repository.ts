import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, Options, repository, BelongsToAccessor} from '@loopback/repository';
import {v4 as uuidv4} from 'uuid';
import {MysqlDataSource} from '../datasources';
import {SupplierSectionSubmission, SupplierSectionSubmissionRelations, AssessmentSection, AssessmentSubSection1, AssessmentSubSection2, DealerResponseForm} from '../models';
import {AssessmentSectionRepository} from './assessment-section.repository';
import {AssessmentSubSection1Repository} from './assessment-sub-section1.repository';
import {AssessmentSubSection2Repository} from './assessment-sub-section2.repository';
import {DealerResponseFormRepository} from './dealer-response-form.repository';

export class SupplierSectionSubmissionRepository extends DefaultCrudRepository<
  SupplierSectionSubmission,
  typeof SupplierSectionSubmission.prototype.id,
  SupplierSectionSubmissionRelations
> {

  public readonly assessmentSection: BelongsToAccessor<AssessmentSection, typeof SupplierSectionSubmission.prototype.id>;

  public readonly assessmentSubSection1: BelongsToAccessor<AssessmentSubSection1, typeof SupplierSectionSubmission.prototype.id>;

  public readonly assessmentSubSection2: BelongsToAccessor<AssessmentSubSection2, typeof SupplierSectionSubmission.prototype.id>;

  public readonly dealerResponseForm: BelongsToAccessor<DealerResponseForm, typeof SupplierSectionSubmission.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('AssessmentSectionRepository') protected assessmentSectionRepositoryGetter: Getter<AssessmentSectionRepository>, @repository.getter('AssessmentSubSection1Repository') protected assessmentSubSection1RepositoryGetter: Getter<AssessmentSubSection1Repository>, @repository.getter('AssessmentSubSection2Repository') protected assessmentSubSection2RepositoryGetter: Getter<AssessmentSubSection2Repository>, @repository.getter('DealerResponseFormRepository') protected dealerResponseFormRepositoryGetter: Getter<DealerResponseFormRepository>,
  ) {
    super(SupplierSectionSubmission, dataSource);
    this.dealerResponseForm = this.createBelongsToAccessorFor('dealerResponseForm', dealerResponseFormRepositoryGetter,);
    this.registerInclusionResolver('dealerResponseForm', this.dealerResponseForm.inclusionResolver);
    this.assessmentSubSection2 = this.createBelongsToAccessorFor('assessmentSubSection2', assessmentSubSection2RepositoryGetter,);
    this.registerInclusionResolver('assessmentSubSection2', this.assessmentSubSection2.inclusionResolver);
    this.assessmentSubSection1 = this.createBelongsToAccessorFor('assessmentSubSection1', assessmentSubSection1RepositoryGetter,);
    this.registerInclusionResolver('assessmentSubSection1', this.assessmentSubSection1.inclusionResolver);
    this.assessmentSection = this.createBelongsToAccessorFor('assessmentSection', assessmentSectionRepositoryGetter,);
    this.registerInclusionResolver('assessmentSection', this.assessmentSection.inclusionResolver);
  }
  async create(entity: Partial<SupplierSectionSubmission>, options?: Options): Promise<SupplierSectionSubmission> {

    entity.id = uuidv4(); // Generate UUID before saving if `id` is missing

    return super.create(entity, options);
  }
}
