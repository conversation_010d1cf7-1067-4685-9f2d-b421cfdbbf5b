import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDfEntity, AssignDfEntityRelations, ResponseFormCollection} from '../models';
import {ResponseFormCollectionRepository} from './response-form-collection.repository';

export class AssignDfEntityRepository extends DefaultCrudRepository<
  AssignDfEntity,
  typeof AssignDfEntity.prototype.id,
  AssignDfEntityRelations
> {

  public readonly df: BelongsToAccessor<ResponseFormCollection, typeof AssignDfEntity.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ResponseFormCollectionRepository') protected responseFormCollectionRepositoryGetter: Getter<ResponseFormCollectionRepository>,
  ) {
    super(AssignDfEntity, dataSource);
    this.df = this.createBelongsToAccessorFor('df', responseFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('df', this.df.inclusionResolver);
  }
}
