import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierAction,
  VendorCode,
} from '../models';
import {SupplierActionRepository} from '../repositories';

export class SupplierActionVendorCodeController {
  constructor(
    @repository(SupplierActionRepository)
    public supplierActionRepository: SupplierActionRepository,
  ) { }

  @get('/supplier-actions/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to SupplierAction',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof SupplierAction.prototype.id,
  ): Promise<VendorCode> {
    return this.supplierActionRepository.vendorCode(id);
  }
}
