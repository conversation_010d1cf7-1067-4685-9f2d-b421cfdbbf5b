import {Entity, model, property, hasMany} from '@loopback/repository';
import {LocationTwo} from './location-two.model';

@model({settings: {strict: false}})
export class LocationOne extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];
  
  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @hasMany(() => LocationTwo)
  locationTwos: LocationTwo[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<LocationOne>) {
    super(data);
  }
}

export interface LocationOneRelations {
  // describe navigational properties here
}

export type LocationOneWithRelations = LocationOne & LocationOneRelations;
