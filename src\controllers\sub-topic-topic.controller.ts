import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SubTopic,
  Topic,
} from '../models';
import {SubTopicRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';


export class SubTopicTopicController {
  constructor(
    @repository(SubTopicRepository)
    public subTopicRepository: SubTopicRepository,
  ) { }

  @get('/sub-topics/{id}/topic', {
    responses: {
      '200': {
        description: 'Topic belonging to SubTopic',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Topic)},
          },
        },
      },
    },
  })
  async getTopic(
    @param.path.number('id') id: typeof SubTopic.prototype.id,
  ): Promise<Topic> {
    return this.subTopicRepository.topic(id);
  }
}
