import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDfEntityUser, AssignDfEntityUserRelations, ResponseFormCollection, LocationThree} from '../models';
import {ResponseFormCollectionRepository} from './response-form-collection.repository';
import {LocationThreeRepository} from './location-three.repository';

export class AssignDfEntityUserRepository extends DefaultCrudRepository<
  AssignDfEntityUser,
  typeof AssignDfEntityUser.prototype.id,
  AssignDfEntityUserRelations
> {

  public readonly df: BelongsToAccessor<ResponseFormCollection, typeof AssignDfEntityUser.prototype.id>;

  public readonly lthree: BelongsToAccessor<LocationThree, typeof AssignDfEntityUser.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ResponseFormCollectionRepository') protected responseFormCollectionRepositoryGetter: Getter<ResponseFormCollectionRepository>, @repository.getter('LocationThreeRepository') protected locationThreeRepositoryGetter: Getter<LocationThreeRepository>,
  ) {
    super(AssignDfEntityUser, dataSource);
    this.lthree = this.createBelongsToAccessorFor('lthree', locationThreeRepositoryGetter,);
    this.registerInclusionResolver('lthree', this.lthree.inclusionResolver);
    this.df = this.createBelongsToAccessorFor('df', responseFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('df', this.df.inclusionResolver);
  }
}
