import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignDfEntity,
  ResponseFormCollection,
} from '../models';
import {AssignDfEntityRepository} from '../repositories';

export class AssignDfEntityResponseFormCollectionController {
  constructor(
    @repository(AssignDfEntityRepository)
    public assignDfEntityRepository: AssignDfEntityRepository,
  ) { }

  @get('/assign-df-entities/{id}/response-form-collection', {
    responses: {
      '200': {
        description: 'ResponseFormCollection belonging to AssignDfEntity',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ResponseFormCollection),
          },
        },
      },
    },
  })
  async getResponseFormCollection(
    @param.path.number('id') id: typeof AssignDfEntity.prototype.id,
  ): Promise<ResponseFormCollection> {
    return this.assignDfEntityRepository.df(id);
  }
}
