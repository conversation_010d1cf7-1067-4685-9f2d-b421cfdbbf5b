import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {IndicatorSection, IndicatorSectionRelations} from '../models';


export class IndicatorSectionRepository extends DefaultCrudRepository<
  IndicatorSection,
  typeof IndicatorSection.prototype.id,
  IndicatorSectionRelations
> {



  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(IndicatorSection, dataSource);

  }
}
