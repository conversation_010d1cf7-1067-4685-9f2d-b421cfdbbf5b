import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  StdName,
  StdScope,
} from '../models';
import {StdNameRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdNameStdScopeController {
  constructor(
    @repository(StdNameRepository) protected stdNameRepository: StdNameRepository,
  ) { }

  @get('/std-names/{id}/std-scopes', {
    responses: {
      '200': {
        description: 'Array of StdName has many StdScope',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(StdScope)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<StdScope>,
  ): Promise<StdScope[]> {
    return this.stdNameRepository.stdScopes(id).find(filter);
  }

  @post('/std-names/{id}/std-scopes', {
    responses: {
      '200': {
        description: 'StdName model instance',
        content: {'application/json': {schema: getModelSchemaRef(StdScope)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof StdName.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdScope, {
            title: 'NewStdScopeInStdName',
            exclude: ['id'],
            optional: ['stdNameId']
          }),
        },
      },
    }) stdScope: Omit<StdScope, 'id'>,
  ): Promise<StdScope> {
    return this.stdNameRepository.stdScopes(id).create(stdScope);
  }

  @patch('/std-names/{id}/std-scopes', {
    responses: {
      '200': {
        description: 'StdName.StdScope PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdScope, {partial: true}),
        },
      },
    })
    stdScope: Partial<StdScope>,
    @param.query.object('where', getWhereSchemaFor(StdScope)) where?: Where<StdScope>,
  ): Promise<Count> {
    return this.stdNameRepository.stdScopes(id).patch(stdScope, where);
  }

  @del('/std-names/{id}/std-scopes', {
    responses: {
      '200': {
        description: 'StdName.StdScope DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(StdScope)) where?: Where<StdScope>,
  ): Promise<Count> {
    return this.stdNameRepository.stdScopes(id).delete(where);
  }
}
