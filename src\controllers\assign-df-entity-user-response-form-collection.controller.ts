import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignDfEntityUser,
  ResponseFormCollection,
} from '../models';
import {AssignDfEntityUserRepository} from '../repositories';

export class AssignDfEntityUserResponseFormCollectionController {
  constructor(
    @repository(AssignDfEntityUserRepository)
    public assignDfEntityUserRepository: AssignDfEntityUserRepository,
  ) { }

  @get('/assign-df-entity-users/{id}/response-form-collection', {
    responses: {
      '200': {
        description: 'ResponseFormCollection belonging to AssignDfEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ResponseFormCollection),
          },
        },
      },
    },
  })
  async getResponseFormCollection(
    @param.path.number('id') id: typeof AssignDfEntityUser.prototype.id,
  ): Promise<ResponseFormCollection> {
    return this.assignDfEntityUserRepository.df(id);
  }
}
