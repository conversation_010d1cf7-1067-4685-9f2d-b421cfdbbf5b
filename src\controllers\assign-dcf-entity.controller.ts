import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignDcfEntity} from '../models';
import {AssignDcfEntityRepository} from '../repositories';

export class AssignDcfEntityController {
  constructor(
    @repository(AssignDcfEntityRepository)
    public assignDcfEntityRepository : AssignDcfEntityRepository,
  ) {}

  @post('/assign-dcf-entities')
  @response(200, {
    description: 'AssignDcfEntity model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDcfEntity)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntity, {
            title: 'NewAssignDcfEntity',
            exclude: ['id'],
          }),
        },
      },
    })
    assignDcfEntity: Omit<AssignDcfEntity, 'id'>,
  ): Promise<AssignDcfEntity> {
    return this.assignDcfEntityRepository.create(assignDcfEntity);
  }

  @get('/assign-dcf-entities/count')
  @response(200, {
    description: 'AssignDcfEntity model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDcfEntity) where?: Where<AssignDcfEntity>,
  ): Promise<Count> {
    return this.assignDcfEntityRepository.count(where);
  }

  @get('/assign-dcf-entities')
  @response(200, {
    description: 'Array of AssignDcfEntity model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDcfEntity, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDcfEntity) filter?: Filter<AssignDcfEntity>,
  ): Promise<AssignDcfEntity[]> {
    return this.assignDcfEntityRepository.find(filter);
  }

  @patch('/assign-dcf-entities')
  @response(200, {
    description: 'AssignDcfEntity PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntity, {partial: true}),
        },
      },
    })
    assignDcfEntity: AssignDcfEntity,
    @param.where(AssignDcfEntity) where?: Where<AssignDcfEntity>,
  ): Promise<Count> {
    return this.assignDcfEntityRepository.updateAll(assignDcfEntity, where);
  }

  @get('/assign-dcf-entities/{id}')
  @response(200, {
    description: 'AssignDcfEntity model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDcfEntity, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDcfEntity, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDcfEntity>
  ): Promise<AssignDcfEntity> {
    return this.assignDcfEntityRepository.findById(id, filter);
  }

  @patch('/assign-dcf-entities/{id}')
  @response(204, {
    description: 'AssignDcfEntity PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntity, {partial: true}),
        },
      },
    })
    assignDcfEntity: AssignDcfEntity,
  ): Promise<void> {
    await this.assignDcfEntityRepository.updateById(id, assignDcfEntity);
  }

  @put('/assign-dcf-entities/{id}')
  @response(204, {
    description: 'AssignDcfEntity PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignDcfEntity: AssignDcfEntity,
  ): Promise<void> {
    await this.assignDcfEntityRepository.replaceById(id, assignDcfEntity);
  }

  @del('/assign-dcf-entities/{id}')
  @response(204, {
    description: 'AssignDcfEntity DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDcfEntityRepository.deleteById(id);
  }
}
