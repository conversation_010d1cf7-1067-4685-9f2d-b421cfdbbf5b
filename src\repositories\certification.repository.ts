import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {Certification, CertificationRelations, CertIssueAuthority, CertificationLevel} from '../models';
import {CertIssueAuthorityRepository} from './cert-issue-authority.repository';
import {CertificationLevelRepository} from './certification-level.repository';

export class CertificationRepository extends DefaultCrudRepository<
  Certification,
  typeof Certification.prototype.id,
  CertificationRelations
> {

  public readonly certIssueAuthorities: HasManyRepositoryFactory<CertIssueAuthority, typeof Certification.prototype.id>;

  public readonly certificationLevels: HasManyRepositoryFactory<CertificationLevel, typeof Certification.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('CertIssueAuthorityRepository') protected certIssueAuthorityRepositoryGetter: Getter<CertIssueAuthorityRepository>, @repository.getter('CertificationLevelRepository') protected certificationLevelRepositoryGetter: Getter<CertificationLevelRepository>,
  ) {
    super(Certification, dataSource);
    this.certificationLevels = this.createHasManyRepositoryFactoryFor('certificationLevels', certificationLevelRepositoryGetter,);
    this.registerInclusionResolver('certificationLevels', this.certificationLevels.inclusionResolver);
    this.certIssueAuthorities = this.createHasManyRepositoryFactoryFor('certIssueAuthorities', certIssueAuthorityRepositoryGetter,);
    this.registerInclusionResolver('certIssueAuthorities', this.certIssueAuthorities.inclusionResolver);
  }
}
