import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {IndicatorApproverAssignment, IndicatorApproverAssignmentRelations} from '../models';

export class IndicatorApproverAssignmentRepository extends DefaultCrudRepository<
  IndicatorApproverAssignment,
  typeof IndicatorApproverAssignment.prototype.id,
  IndicatorApproverAssignmentRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(IndicatorApproverAssignment, dataSource);
  }
}
