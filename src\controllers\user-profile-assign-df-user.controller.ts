import {
  Filter,
  repository
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody
} from '@loopback/rest';
import {
  AssignDfUser,
  UserProfile,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileAssignDfUserController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-df-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDfUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDfUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDfUser>,
  ): Promise<AssignDfUser[]> {
    return this.userProfileRepository.assignDfUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-df-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDfUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfUser, {
            title: 'NewAssignDfUserInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDfUser: Omit<AssignDfUser, 'id'>,
  ): Promise<AssignDfUser> {
    return this.userProfileRepository.assignDfUsers(id).create(assignDfUser);
  }

  // @patch('/user-profiles/{id}/assign-df-users', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDfUser PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDfUser, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDfUser: Partial<AssignDfUser>,
  //   @param.query.object('where', getWhereSchemaFor(AssignDfUser)) where?: Where<AssignDfUser>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDfUsers(id).patch(assignDfUser, where);
  // }

  // @del('/user-profiles/{id}/assign-df-users', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDfUser DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignDfUser)) where?: Where<AssignDfUser>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDfUsers(id).delete(where);
  // }
}
