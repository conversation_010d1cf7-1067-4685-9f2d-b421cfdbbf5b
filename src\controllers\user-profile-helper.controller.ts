import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  Helper,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileHelperController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/helper', {
    responses: {
      '200': {
        description: 'UserProfile has one Helper',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Helper),
          },
        },
      },
    },
  })
  async get(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Helper>,
  ): Promise<Helper> {
    return this.userProfileRepository.helper(id).get(filter);
  }

  @post('/user-profiles/{id}/helper', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(Helper)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Helper, {
            title: 'NewHelperInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) helper: Omit<Helper, 'id'>,
  ): Promise<Helper> {
    return this.userProfileRepository.helper(id).create(helper);
  }

  @patch('/user-profiles/{id}/helper', {
    responses: {
      '200': {
        description: 'UserProfile.Helper PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Helper, {partial: true}),
        },
      },
    })
    helper: Partial<Helper>,
    @param.query.object('where', getWhereSchemaFor(Helper)) where?: Where<Helper>,
  ): Promise<Count> {
    return this.userProfileRepository.helper(id).patch(helper, where);
  }

  @del('/user-profiles/{id}/helper', {
    responses: {
      '200': {
        description: 'UserProfile.Helper DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Helper)) where?: Where<Helper>,
  ): Promise<Count> {
    return this.userProfileRepository.helper(id).delete(where);
  }
}
