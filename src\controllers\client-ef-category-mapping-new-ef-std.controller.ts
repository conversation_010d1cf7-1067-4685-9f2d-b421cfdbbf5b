import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ClientEfCategoryMapping,
  NewEfStd,
} from '../models';
import {ClientEfCategoryMappingRepository} from '../repositories';

export class ClientEfCategoryMappingNewEfStdController {
  constructor(
    @repository(ClientEfCategoryMappingRepository)
    public clientEfCategoryMappingRepository: ClientEfCategoryMappingRepository,
  ) { }

  @get('/client-ef-category-mappings/{id}/new-ef-std', {
    responses: {
      '200': {
        description: 'NewEfStd belonging to ClientEfCategoryMapping',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfStd)},
          },
        },
      },
    },
  })
  async getNewEfStd(
    @param.path.number('id') id: typeof ClientEfCategoryMapping.prototype.id,
  ): Promise<NewEfStd> {
    return this.clientEfCategoryMappingRepository.efStandard(id);
  }
}
