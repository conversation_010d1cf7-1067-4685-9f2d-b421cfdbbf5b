import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SubSurvey} from '../models';
import {SubSurveyRepository} from '../repositories';

export class SubSurveyController {
  constructor(
    @repository(SubSurveyRepository)
    public subSurveyRepository : SubSurveyRepository,
  ) {}

  @post('/sub-surveys')
  @response(200, {
    description: 'SubSurvey model instance',
    content: {'application/json': {schema: getModelSchemaRef(SubSurvey)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubSurvey, {
            title: 'NewSubSurvey',
            exclude: ['id'],
          }),
        },
      },
    })
    subSurvey: Omit<SubSurvey, 'id'>,
  ): Promise<SubSurvey> {
    return this.subSurveyRepository.create(subSurvey);
  }

  @get('/sub-surveys/count')
  @response(200, {
    description: 'SubSurvey model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SubSurvey) where?: Where<SubSurvey>,
  ): Promise<Count> {
    return this.subSurveyRepository.count(where);
  }

  @get('/sub-surveys')
  @response(200, {
    description: 'Array of SubSurvey model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SubSurvey, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SubSurvey) filter?: Filter<SubSurvey>,
  ): Promise<SubSurvey[]> {
    return this.subSurveyRepository.find(filter);
  }

  @patch('/sub-surveys')
  @response(200, {
    description: 'SubSurvey PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubSurvey, {partial: true}),
        },
      },
    })
    subSurvey: SubSurvey,
    @param.where(SubSurvey) where?: Where<SubSurvey>,
  ): Promise<Count> {
    return this.subSurveyRepository.updateAll(subSurvey, where);
  }

  @get('/sub-surveys/{id}')
  @response(200, {
    description: 'SubSurvey model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SubSurvey, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SubSurvey, {exclude: 'where'}) filter?: FilterExcludingWhere<SubSurvey>
  ): Promise<SubSurvey> {
    return this.subSurveyRepository.findById(id, filter);
  }

  @patch('/sub-surveys/{id}')
  @response(204, {
    description: 'SubSurvey PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubSurvey, {partial: true}),
        },
      },
    })
    subSurvey: SubSurvey,
  ): Promise<void> {
    await this.subSurveyRepository.updateById(id, subSurvey);
  }

  @put('/sub-surveys/{id}')
  @response(204, {
    description: 'SubSurvey PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() subSurvey: SubSurvey,
  ): Promise<void> {
    await this.subSurveyRepository.replaceById(id, subSurvey);
  }

  @del('/sub-surveys/{id}')
  @response(204, {
    description: 'SubSurvey DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.subSurveyRepository.deleteById(id);
  }
}
