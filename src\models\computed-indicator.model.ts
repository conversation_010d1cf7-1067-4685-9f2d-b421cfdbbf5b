import {Entity, model, property} from '@loopback/repository';
import {DateTime} from 'luxon';

@model()
export class ComputedIndicator extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({type: 'number'})
  locationId?: number;

  @property({type: 'number'})
  reportingYear?: number;

  @property({type: 'number'})
  level?: number;

  @property({
    type: 'array',
    itemType: 'string',
  })
  reporting_period?: string[];

  @property({
    type: 'number',
    jsonSchema: {nullable: true},
  })
  computedValue?: number | null;

  @property({
    type: 'number',
    jsonSchema: {nullable: true},
  })
  emissionFactorValue?: number | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  emissionFactorName?: string | null;

  @property({
    type: 'number'
  })
  emissionFactorCo2Value?: number

  @property({
    type: 'number'
  })
  emissionFactorCh4Value?: number

  @property({
    type: 'number',
  })
  emissionFactorN2oValue?: number

  @property({type: 'string', jsonSchema: {nullable: true}})
  efkey?: string | null;

  @property({
    type: 'number',
    jsonSchema: {nullable: true},
  })
  efValue?: number | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  unitOfMeasure?: string | null;

  @property({type: 'number'})
  dcfId?: number;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  sapId?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  methodology?: string | null;

  @property({type: 'number'})
  submitId?: number;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  actualTitle?: string | null;

  @property({
    type: 'number',
    jsonSchema: {nullable: true},
  })
  formCategory?: number | null;

  @property({
    type: 'number',
    jsonSchema: {nullable: true},
  })
  value?: number | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  title?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  approverComments?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  dateOfApproval?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  entity?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  periodFrom?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  periodTo?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  uniqueId?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  reporter?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  reportedDate?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  reviewedDate?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  reporterComments?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  reviewer?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  reviewerComments?: string | null;

  @property({
    type: 'number',
    jsonSchema: {nullable: true},
  })
  computedCo2Value?: number | null;

  @property({
    type: 'number',
    jsonSchema: {nullable: true},
  })
  computedCh4Value?: number | null;

  @property({
    type: 'number',
    jsonSchema: {nullable: true},
  })
  computedN2oValue?: number | null;

  @property({type: 'number'})
  indicatorId?: number;

  @property({
    type: 'number'
  })
  indicatorType?: number

  @property({
    type: 'string'
  })
  indicatorTitle?: string

  @property({
    type: 'array',
    itemType: 'string',
    jsonSchema: {nullable: true},
  })
  frameworkTags?: string[] | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  indicatorUnit?: string | null;

  @property({
    type: 'string',
    jsonSchema: {nullable: true},
  })
  approver?: string | null;

  @property({
    type: 'string',
    default: () => DateTime.utc().toString(),
  })
  lastUpdatedOn?: string;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<ComputedIndicator>) {
    super(data);
  }
}


export interface ComputedIndicatorRelations {
  // describe navigational properties here
}

export type ComputedIndicatorWithRelations = ComputedIndicator & ComputedIndicatorRelations;
