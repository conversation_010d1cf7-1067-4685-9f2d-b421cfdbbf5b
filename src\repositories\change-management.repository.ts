import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ChangeManagement, ChangeManagementRelations} from '../models';

export class ChangeManagementRepository extends DefaultCrudRepository<
  ChangeManagement,
  typeof ChangeManagement.prototype.id,
  ChangeManagementRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ChangeManagement, dataSource);
  }
}
