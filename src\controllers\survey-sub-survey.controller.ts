import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Survey,
  SubSurvey,
} from '../models';
import {SurveyRepository} from '../repositories';

export class SurveySubSurveyController {
  constructor(
    @repository(SurveyRepository) protected surveyRepository: SurveyRepository,
  ) { }

  @get('/surveys/{id}/sub-surveys', {
    responses: {
      '200': {
        description: 'Array of Survey has many SubSurvey',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SubSurvey)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SubSurvey>,
  ): Promise<SubSurvey[]> {
    return this.surveyRepository.subSurveys(id).find(filter);
  }

  @post('/surveys/{id}/sub-surveys', {
    responses: {
      '200': {
        description: 'Survey model instance',
        content: {'application/json': {schema: getModelSchemaRef(SubSurvey)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Survey.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubSurvey, {
            title: 'NewSubSurveyInSurvey',
            exclude: ['id'],
            optional: ['surveyId']
          }),
        },
      },
    }) subSurvey: Omit<SubSurvey, 'id'>,
  ): Promise<SubSurvey> {
    return this.surveyRepository.subSurveys(id).create(subSurvey);
  }

  @patch('/surveys/{id}/sub-surveys', {
    responses: {
      '200': {
        description: 'Survey.SubSurvey PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubSurvey, {partial: true}),
        },
      },
    })
    subSurvey: Partial<SubSurvey>,
    @param.query.object('where', getWhereSchemaFor(SubSurvey)) where?: Where<SubSurvey>,
  ): Promise<Count> {
    return this.surveyRepository.subSurveys(id).patch(subSurvey, where);
  }

  @del('/surveys/{id}/sub-surveys', {
    responses: {
      '200': {
        description: 'Survey.SubSurvey DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SubSurvey)) where?: Where<SubSurvey>,
  ): Promise<Count> {
    return this.surveyRepository.subSurveys(id).delete(where);
  }
}
