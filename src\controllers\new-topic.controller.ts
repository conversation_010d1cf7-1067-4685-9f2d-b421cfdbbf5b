import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {NewTopic} from '../models';
import {NewDataPointRepository, NewMetricRepository, NewTopicRepository} from '../repositories';

import {DateTime} from 'luxon';



export class NewTopicController {
  constructor(
    @repository(NewTopicRepository)
    public newTopicRepository: NewTopicRepository,

    @repository(NewMetricRepository)
    public newMetricRepository: NewMetricRepository,

    @repository(NewDataPointRepository)
    public newDataPointRepository: NewDataPointRepository,
  ) { }

  @post('/new-topics')
  @response(200, {
    description: 'NewTopic model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewTopic)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTopic, {
            title: 'NewNewTopic',
            exclude: ['id'],
          }),
        },
      },
    })
    newTopic: Omit<NewTopic, 'id'>,
  ): Promise<NewTopic> {
    const Topic = await this.newTopicRepository.create(newTopic);
    const id = Topic.id;
    const suffix = newTopic.suffix + id;
    Topic.suffix = suffix;
    await this.newTopicRepository.updateById(id, {suffix: suffix});
    return Topic;
  }

  @get('/new-topics/count')
  @response(200, {
    description: 'NewTopic model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewTopic) where?: Where<NewTopic>,
  ): Promise<Count> {
    return this.newTopicRepository.count(where);
  }

  @get('/new-topics')
  @response(200, {
    description: 'Array of NewTopic model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewTopic, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewTopic) filter?: Filter<NewTopic>,
  ): Promise<NewTopic[]> {
    return this.newTopicRepository.find(filter);
  }

  // @patch('/new-topics')
  // @response(200, {
  //   description: 'NewTopic PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewTopic, {partial: true}),
  //       },
  //     },
  //   })
  //   newTopic: NewTopic,
  //   @param.where(NewTopic) where?: Where<NewTopic>,
  // ): Promise<Count> {
  //   return this.newTopicRepository.updateAll(newTopic, where);
  // }

  @patch('/new-topics')
  @response(200, {
    description: 'NewTopic PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            properties: {
              newTopics: {

                type: 'array',
                items: getModelSchemaRef(NewTopic, {partial: true}),
              },
            },
          },
        },
      },
    })
    newTopics: NewTopic[],
    @param.where(NewTopic) where?: Where<NewTopic>,
  ): Promise<Count> {
    const updatePromises = newTopics.map((newTopic) => {
      return this.newTopicRepository.updateById(newTopic.id, {order: newTopic.order});
    });

    await Promise.all(updatePromises);

    const totalCount = newTopics.length;
    return {count: totalCount};
  }


  @get('/new-topics/{id}')
  @response(200, {
    description: 'NewTopic model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewTopic, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewTopic, {exclude: 'where'}) filter?: FilterExcludingWhere<NewTopic>
  ): Promise<NewTopic> {
    return this.newTopicRepository.findById(id, filter);
  }

  @patch('/new-topics/{id}')
  @response(204, {
    description: 'NewTopic PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTopic, {partial: true}),
        },
      },
    })
    newTopic: NewTopic,
  ): Promise<void> {
    await this.newTopicRepository.updateById(id, newTopic);
  }

  @put('/new-topics/{id}')
  @response(204, {
    description: 'NewTopic PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newTopic: NewTopic,
  ): Promise<void> {
    await this.newTopicRepository.replaceById(id, newTopic);
  }

  @del('/new-topics/{id}')
  @response(204, {
    description: 'NewTopic DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newTopicRepository.deleteById(id);
  }

  // @post('/clone-with-metric')
  // async cloneWithMetrics(@requestBody({
  //   content: {
  //     'application/json': {
  //       schema: {
  //         type: 'object', properties: {topic_id: {type: 'number'}, tag: {type: 'number'}}
  //       }
  //     },
  //   },
  // })
  // clone_id: {topic_id: number, tag: number}):
  //   Promise<object> {
  //   const {topic_id, tag} = clone_id
  //   let topic_data = await this.newTopicRepository.findById(topic_id)
  //   if (topic_data) {
  //     let new_topic = {...topic_data, extra: 1}
  //     delete new_topic.id
  //     delete new_topic.data2
  //     delete new_topic.order
  //     new_topic.tag = tag
  //     new_topic.cloneTopicId = topic_id
  //     new_topic.title = 'Cloned_' + new_topic.title
  //     new_topic.created = DateTime.utc().toString()
  //     let new_id = await this.newTopicRepository.create(new_topic)
  //     if (new_id) {
  //       let ndp = await this.newMetricRepository.find({where: {newTopicId: topic_id}})
  //       let ndp_array: any[] = []
  //       try {
  //         const info = await new Promise((resolve, reject) => {
  //           ndp.forEach(async (ndp_data: any) => {
  //             let newObj = {...ndp_data, extra: 1}
  //             newObj.newTopicId = new_id.id
  //             newObj.tag = tag
  //             newObj.created = DateTime.utc().toString()
  //             newObj.cloneTopicId = topic_id
  //             newObj.cloneMetricId = newObj.id
  //             delete newObj.id
  //             delete newObj.order
  //             delete newObj.data2
  //             let create_ndp = await this.newMetricRepository.create(newObj)
  //             if (create_ndp) {
  //               let ndp = await this.newDataPointRepository.find({where: {newMetricId: newObj.id}})
  //               let ndp_array: any[] = []
  //               ndp.forEach(async (ndp_data) => {
  //                 let newObj1 = {...ndp_data, extra: 1}
  //                 newObj1.newMetricId = new_id.id

  //                 newObj1.created = DateTime.utc().toString()
  //                 newObj1.cloneMetricId = newObj.id
  //                 newObj1.cloneDataPointId = newObj1.id
  //                 delete newObj1.id
  //                 delete newObj1.order
  //                 delete newObj1.data2
  //                 ndp_array.push(newObj1)
  //               })
  //               let create_ndp = await this.newDataPointRepository.createAll(ndp_array).catch((ea) => {

  //                 reject({result: false})

  //               })


  //             }
  //             else {
  //               reject({result: false})
  //             }
  //           })
  //           resolve({result: true})
  //         }).then((et) => {
  //           console.log(et)
  //           return {result: true}
  //         }).catch((ec) => {
  //           return {result: false}
  //         })
  //         console.log(info)
  //         return info;
  //       } catch (err) {

  //         return {result: false}
  //       }



  //     } else {
  //       return {result: false}
  //     }
  //   } else {
  //     return {result: false}
  //   }
  // }

  @post('/clone-topic-with-metric')
  async cloneWithMetrics(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object', properties: {topic_id: {type: 'number'}, tag: {type: 'number'}}
        }
      },
    },
  })
  clone_id: {topic_id: number, tag: number}):
    Promise<object> {
    const {topic_id, tag} = clone_id
    let topic_data = await this.newTopicRepository.findById(topic_id)
    if (topic_data) {
      let new_topic = {...topic_data, extra: 1}
      delete new_topic.id
      delete new_topic.data2
      delete new_topic.order
      new_topic.tag = tag
      new_topic.cloneTopicId = topic_id
      new_topic.title = 'Cloned_' + new_topic.title
      new_topic.created = DateTime.utc().toString()
      let new_id = await this.newTopicRepository.create(new_topic)
      if (new_id) {
        let ndp = await this.newMetricRepository.find({where: {newTopicId: topic_id}})
        let ndp_array: any[] = []
        try {
          return await new Promise((resolve, reject) => {
            ndp.forEach(async (ndp_data: any) => {
              let newObj = {...ndp_data, extra: 1}
              newObj.newTopicId = new_id.id
              newObj.tag = tag
              newObj.created = DateTime.utc().toString()
              newObj.cloneTopicId = topic_id
              newObj.cloneMetricId = newObj.id
              delete newObj.id
              delete newObj.order
              delete newObj.data2
              let create_ndp = await this.newMetricRepository.create(newObj)
              if (create_ndp) {
                let ndp2 = await this.newDataPointRepository.find({where: {newMetricId: ndp_data.id}})
                let ndp_array: any[] = []

                ndp2.forEach(async (ndp_data2) => {
                  let newObj1 = {...ndp_data2, extra: 1}
                  newObj1.newMetricId = create_ndp.id

                  newObj1.created = DateTime.utc().toString()
                  newObj1.cloneMetricId = ndp_data.id
                  newObj1.cloneDataPointId = newObj1.id
                  delete newObj1.id
                  delete newObj1.order
                  delete newObj1.data2
                  ndp_array.push(newObj1)
                })
                console.log(newObj, ndp_array.length, ndp2.length)
                let create_ndp2 = await this.newDataPointRepository.createAll(ndp_array).catch((ea) => {

                  reject({result: false, data: new_id})

                })


              }
              else {
                reject({result: false, data: new_id})
              }
            })
            resolve({result: true, data: new_id})
          })




        } catch (err) {

          return {result: false, data: new_id}
        }



      } else {
        return {result: false}
      }
    } else {
      return {result: false}
    }
  }

}
