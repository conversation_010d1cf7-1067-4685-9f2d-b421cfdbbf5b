import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewEfStd,
  NewEfCategory,
} from '../models';
import {NewEfStdRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfStdNewEfCategoryController {
  constructor(
    @repository(NewEfStdRepository) protected newEfStdRepository: NewEfStdRepository,
  ) { }

  @get('/new-ef-stds/{id}/new-ef-categories', {
    responses: {
      '200': {
        description: 'Array of NewEfStd has many NewEfCategory',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfCategory)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEfCategory>,
  ): Promise<NewEfCategory[]> {
    return this.newEfStdRepository.newEfCategories(id).find(filter);
  }

  @post('/new-ef-stds/{id}/new-ef-categories', {
    responses: {
      '200': {
        description: 'NewEfStd model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEfCategory)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEfStd.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfCategory, {
            title: 'NewNewEfCategoryInNewEfStd',
            exclude: ['id'],
            optional: ['newEfStdId']
          }),
        },
      },
    }) newEfCategory: Omit<NewEfCategory, 'id'>,
  ): Promise<NewEfCategory> {
    return this.newEfStdRepository.newEfCategories(id).create(newEfCategory);
  }

  // @patch('/new-ef-stds/{id}/new-ef-categories', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfStd.NewEfCategory PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfCategory, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfCategory: Partial<NewEfCategory>,
  //   @param.query.object('where', getWhereSchemaFor(NewEfCategory)) where?: Where<NewEfCategory>,
  // ): Promise<Count> {
  //   return this.newEfStdRepository.newEfCategories(id).patch(newEfCategory, where);
  // }

  // @del('/new-ef-stds/{id}/new-ef-categories', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfStd.NewEfCategory DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewEfCategory)) where?: Where<NewEfCategory>,
  // ): Promise<Count> {
  //   return this.newEfStdRepository.newEfCategories(id).delete(where);
  // }
}
