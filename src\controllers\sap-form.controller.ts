import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
} from '@loopback/rest';
import {SapCollection} from '../models';
import {SapCollectionRepository} from '../repositories';

export class SapFormController {
  constructor(
    @repository(SapCollectionRepository)
    public sapCollectionRepository : SapCollectionRepository,
  ) {}

  @post('/sap-collections', {
    responses: {
      '200': {
        description: 'SapCollection model instance',
        content: {'application/json': {schema: getModelSchemaRef(SapCollection)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapCollection, {
            title: 'NewSapCollection',
            exclude: ['id'],
          }),
        },
      },
    })
    sapCollection: Omit<SapCollection, 'id'>,
  ): Promise<SapCollection> {
    return this.sapCollectionRepository.create(sapCollection);
  }

  @get('/sap-collections/count', {
    responses: {
      '200': {
        description: 'SapCollection model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(SapCollection) where?: Where<SapCollection>,
  ): Promise<Count> {
    return this.sapCollectionRepository.count(where);
  }

  @get('/sap-collections', {
    responses: {
      '200': {
        description: 'Array of SapCollection model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(SapCollection, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(SapCollection) filter?: Filter<SapCollection>,
  ): Promise<SapCollection[]> {
    return this.sapCollectionRepository.find(filter);
  }

  @patch('/sap-collections', {
    responses: {
      '200': {
        description: 'SapCollection PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapCollection, {partial: true}),
        },
      },
    })
    sapCollection: SapCollection,
    @param.where(SapCollection) where?: Where<SapCollection>,
  ): Promise<Count> {
    return this.sapCollectionRepository.updateAll(sapCollection, where);
  }

  @get('/sap-collections/{id}', {
    responses: {
      '200': {
        description: 'SapCollection model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(SapCollection, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SapCollection, {exclude: 'where'}) filter?: FilterExcludingWhere<SapCollection>
  ): Promise<SapCollection> {
    return this.sapCollectionRepository.findById(id, filter);
  }

  @patch('/sap-collections/{id}', {
    responses: {
      '204': {
        description: 'SapCollection PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapCollection, {partial: true}),
        },
      },
    })
    sapCollection: SapCollection,
  ): Promise<void> {
    await this.sapCollectionRepository.updateById(id, sapCollection);
  }

  @put('/sap-collections/{id}', {
    responses: {
      '204': {
        description: 'SapCollection PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() sapCollection: SapCollection,
  ): Promise<void> {
    await this.sapCollectionRepository.replaceById(id, sapCollection);
  }

  @del('/sap-collections/{id}', {
    responses: {
      '204': {
        description: 'SapCollection DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.sapCollectionRepository.deleteById(id);
  }
}
