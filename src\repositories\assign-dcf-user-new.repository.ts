import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDcfUserNew, AssignDcfUserNewRelations} from '../models';

export class AssignDcfUserNewRepository extends DefaultCrudRepository<
  AssignDcfUserNew,
  typeof AssignDcfUserNew.prototype.id,
  AssignDcfUserNewRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AssignDcfUserNew, dataSource);
  }
}
