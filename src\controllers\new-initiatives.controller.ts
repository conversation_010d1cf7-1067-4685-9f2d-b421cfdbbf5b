import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewInitiatives} from '../models';
import {NewInitiativesRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewInitiativesController {
  constructor(
    @repository(NewInitiativesRepository)
    public newInitiativesRepository : NewInitiativesRepository,
  ) {}

  @post('/new-initiatives')
  @response(200, {
    description: 'NewInitiatives model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewInitiatives)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewInitiatives, {
            title: 'NewNewInitiatives',
            exclude: ['id'],
          }),
        },
      },
    })
    newInitiatives: Omit<NewInitiatives, 'id'>,
  ): Promise<NewInitiatives> {
    return this.newInitiativesRepository.create(newInitiatives);
  }

  @get('/new-initiatives/count')
  @response(200, {
    description: 'NewInitiatives model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewInitiatives) where?: Where<NewInitiatives>,
  ): Promise<Count> {
    return this.newInitiativesRepository.count(where);
  }

  @get('/new-initiatives')
  @response(200, {
    description: 'Array of NewInitiatives model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewInitiatives, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewInitiatives) filter?: Filter<NewInitiatives>,
  ): Promise<NewInitiatives[]> {
    return this.newInitiativesRepository.find(filter);
  }

  // @patch('/new-initiatives')
  // @response(200, {
  //   description: 'NewInitiatives PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewInitiatives, {partial: true}),
  //       },
  //     },
  //   })
  //   newInitiatives: NewInitiatives,
  //   @param.where(NewInitiatives) where?: Where<NewInitiatives>,
  // ): Promise<Count> {
  //   return this.newInitiativesRepository.updateAll(newInitiatives, where);
  // }

  @get('/new-initiatives/{id}')
  @response(200, {
    description: 'NewInitiatives model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewInitiatives, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewInitiatives, {exclude: 'where'}) filter?: FilterExcludingWhere<NewInitiatives>
  ): Promise<NewInitiatives> {
    return this.newInitiativesRepository.findById(id, filter);
  }

  @patch('/new-initiatives/{id}')
  @response(204, {
    description: 'NewInitiatives PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewInitiatives, {partial: true}),
        },
      },
    })
    newInitiatives: NewInitiatives,
  ): Promise<void> {
    await this.newInitiativesRepository.updateById(id, newInitiatives);
  }

  @put('/new-initiatives/{id}')
  @response(204, {
    description: 'NewInitiatives PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newInitiatives: NewInitiatives,
  ): Promise<void> {
    await this.newInitiativesRepository.replaceById(id, newInitiatives);
  }

  @del('/new-initiatives/{id}')
  @response(204, {
    description: 'NewInitiatives DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newInitiativesRepository.deleteById(id);
  }
}
