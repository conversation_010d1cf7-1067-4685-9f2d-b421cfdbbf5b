import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class AssignDcfSuppliers extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  modifier_id?: number;

  @property({
    type: 'number',
  })
  creator_id?: number;

  @property({
    type: 'number',
  })
  supplier_id?: number;

  @property({
    type: 'array',
    itemType: 'number',
  })
  dfcs?: number[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  config?: object[];

  @property({
    type: 'array',
    itemType: 'any',
  })
  comments?: any[];

  @property({
    type: 'number',
  })
  userProfileId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<AssignDcfSuppliers>) {
    super(data);
  }
}

export interface AssignDcfSuppliersRelations {
  // describe navigational properties here
}

export type AssignDcfSuppliersWithRelations = AssignDcfSuppliers & AssignDcfSuppliersRelations;
