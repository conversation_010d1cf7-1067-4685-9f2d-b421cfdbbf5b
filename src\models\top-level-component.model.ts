import {Entity, model, property, hasMany} from '@loopback/repository';
import {ModuleName} from './module-name.model';

@model({settings: {strict: false}})
export class TopLevelComponent extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];
  
  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'string',
  })
  created?: string;
  
  @hasMany(() => ModuleName)
  moduleNames: ModuleName[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<TopLevelComponent>) {
    super(data);
  }
}

export interface TopLevelComponentRelations {
  // describe navigational properties here
}

export type TopLevelComponentWithRelations = TopLevelComponent & TopLevelComponentRelations;
