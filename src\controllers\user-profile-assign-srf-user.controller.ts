import {
  Filter,
  repository
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody
} from '@loopback/rest';
import {
  AssignSrfUser,
  UserProfile,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileAssignSrfUserController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-srf-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignSrfUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignSrfUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignSrfUser>,
  ): Promise<AssignSrfUser[]> {
    return this.userProfileRepository.assignSrfUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-srf-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignSrfUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfUser, {
            title: 'NewAssignSrfUserInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignSrfUser: Omit<AssignSrfUser, 'id'>,
  ): Promise<AssignSrfUser> {
    return this.userProfileRepository.assignSrfUsers(id).create(assignSrfUser);
  }

  // @patch('/user-profiles/{id}/assign-srf-users', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignSrfUser PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignSrfUser, {partial: true}),
  //       },
  //     },
  //   })
  //   assignSrfUser: Partial<AssignSrfUser>,
  //   @param.query.object('where', getWhereSchemaFor(AssignSrfUser)) where?: Where<AssignSrfUser>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignSrfUsers(id).patch(assignSrfUser, where);
  // }

  // @del('/user-profiles/{id}/assign-srf-users', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignSrfUser DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignSrfUser)) where?: Where<AssignSrfUser>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignSrfUsers(id).delete(where);
  // }
}
