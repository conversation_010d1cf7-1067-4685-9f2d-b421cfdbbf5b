import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SupplierAssessmentAssignment,
  SupplierAction,
} from '../models';
import {SupplierAssessmentAssignmentRepository} from '../repositories';

export class SupplierAssessmentAssignmentSupplierActionController {
  constructor(
    @repository(SupplierAssessmentAssignmentRepository) protected supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
  ) { }

  @get('/supplier-assessment-assignments/{id}/supplier-actions', {
    responses: {
      '200': {
        description: 'Array of SupplierAssessmentAssignment has many SupplierAction',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplierAction)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<SupplierAction>,
  ): Promise<SupplierAction[]> {
    return this.supplierAssessmentAssignmentRepository.supplierActions(id).find(filter);
  }

  @post('/supplier-assessment-assignments/{id}/supplier-actions', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(SupplierAction)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof SupplierAssessmentAssignment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAction, {
            title: 'NewSupplierActionInSupplierAssessmentAssignment',
            exclude: ['id'],
            optional: ['supplierAssessmentAssignmentId']
          }),
        },
      },
    }) supplierAction: Omit<SupplierAction, 'id'>,
  ): Promise<SupplierAction> {
    return this.supplierAssessmentAssignmentRepository.supplierActions(id).create(supplierAction);
  }

  @patch('/supplier-assessment-assignments/{id}/supplier-actions', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment.SupplierAction PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAction, {partial: true}),
        },
      },
    })
    supplierAction: Partial<SupplierAction>,
    @param.query.object('where', getWhereSchemaFor(SupplierAction)) where?: Where<SupplierAction>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.supplierActions(id).patch(supplierAction, where);
  }

  @del('/supplier-assessment-assignments/{id}/supplier-actions', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment.SupplierAction DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(SupplierAction)) where?: Where<SupplierAction>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.supplierActions(id).delete(where);
  }
}
