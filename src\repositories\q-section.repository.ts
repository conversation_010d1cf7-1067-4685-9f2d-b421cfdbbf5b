import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {QSection, QSectionRelations, QRequirement, ConsolidateFormCollection} from '../models';
import {QRequirementRepository} from './q-requirement.repository';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';

export class QSectionRepository extends DefaultCrudRepository<
  QSection,
  typeof QSection.prototype.id,
  QSectionRelations
> {

  public readonly qRequirements: HasManyRepositoryFactory<QRequirement, typeof QSection.prototype.id>;

  public readonly srf: BelongsToAccessor<ConsolidateFormCollection, typeof QSection.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('QRequirementRepository') protected qRequirementRepositoryGetter: Getter<QRequirementRepository>, @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>,
  ) {
    super(QSection, dataSource);
    this.srf = this.createBelongsToAccessorFor('srf', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('srf', this.srf.inclusionResolver);
    this.qRequirements = this.createHasManyRepositoryFactoryFor('qRequirements', qRequirementRepositoryGetter,);
    this.registerInclusionResolver('qRequirements', this.qRequirements.inclusionResolver);
  }
}
