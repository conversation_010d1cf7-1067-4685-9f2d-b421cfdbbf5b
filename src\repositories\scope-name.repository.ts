import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ScopeName, ScopeNameRelations, TopicName} from '../models';
import {TopicNameRepository} from './topic-name.repository';

export class ScopeNameRepository extends DefaultCrudRepository<
  ScopeName,
  typeof ScopeName.prototype.id,
  ScopeNameRelations
> {

  public readonly topicNames: HasManyRepositoryFactory<TopicName, typeof ScopeName.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('TopicNameRepository') protected topicNameRepositoryGetter: Getter<TopicNameRepository>,
  ) {
    super(ScopeName, dataSource);
    this.topicNames = this.createHasManyRepositoryFactoryFor('topicNames', topicNameRepositoryGetter,);
    this.registerInclusionResolver('topicNames', this.topicNames.inclusionResolver);
  }
}
