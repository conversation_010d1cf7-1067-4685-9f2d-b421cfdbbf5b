import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignDcfEntityUser,
  FormCollection,
} from '../models';
import {AssignDcfEntityUserRepository} from '../repositories';

export class AssignDcfEntityUserFormCollectionController {
  constructor(
    @repository(AssignDcfEntityUserRepository)
    public assignDcfEntityUserRepository: AssignDcfEntityUserRepository,
  ) { }

  @get('/assign-dcf-entity-users/{id}/form-collection', {
    responses: {
      '200': {
        description: 'FormCollection belonging to AssignDcfEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(FormCollection),
          },
        },
      },
    },
  })
  async getFormCollection(
    @param.path.number('id') id: typeof AssignDcfEntityUser.prototype.id,
  ): Promise<FormCollection> {
    return this.assignDcfEntityUserRepository.dcf(id);
  }
}
