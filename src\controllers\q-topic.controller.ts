import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {QTopic} from '../models';
import {QTopicRepository} from '../repositories';

export class QTopicController {
  constructor(
    @repository(QTopicRepository)
    public qTopicRepository : QTopicRepository,
  ) {}

  @post('/q-topics')
  @response(200, {
    description: 'QTopic model instance',
    content: {'application/json': {schema: getModelSchemaRef(QTopic)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QTopic, {
            title: 'NewQTopic',
            exclude: ['id'],
          }),
        },
      },
    })
    qTopic: Omit<QTopic, 'id'>,
  ): Promise<QTopic> {
    return this.qTopicRepository.create(qTopic);
  }

  @get('/q-topics/count')
  @response(200, {
    description: 'QTopic model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QTopic) where?: Where<QTopic>,
  ): Promise<Count> {
    return this.qTopicRepository.count(where);
  }

  @get('/q-topics')
  @response(200, {
    description: 'Array of QTopic model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QTopic, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QTopic) filter?: Filter<QTopic>,
  ): Promise<QTopic[]> {
    return this.qTopicRepository.find(filter);
  }

  @patch('/q-topics')
  @response(200, {
    description: 'QTopic PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QTopic, {partial: true}),
        },
      },
    })
    qTopic: QTopic,
    @param.where(QTopic) where?: Where<QTopic>,
  ): Promise<Count> {
    return this.qTopicRepository.updateAll(qTopic, where);
  }

  @get('/q-topics/{id}')
  @response(200, {
    description: 'QTopic model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QTopic, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QTopic, {exclude: 'where'}) filter?: FilterExcludingWhere<QTopic>
  ): Promise<QTopic> {
    return this.qTopicRepository.findById(id, filter);
  }

  @patch('/q-topics/{id}')
  @response(204, {
    description: 'QTopic PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QTopic, {partial: true}),
        },
      },
    })
    qTopic: QTopic,
  ): Promise<void> {
    await this.qTopicRepository.updateById(id, qTopic);
  }

  @put('/q-topics/{id}')
  @response(204, {
    description: 'QTopic PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() qTopic: QTopic,
  ): Promise<void> {
    await this.qTopicRepository.replaceById(id, qTopic);
  }

  @del('/q-topics/{id}')
  @response(204, {
    description: 'QTopic DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.qTopicRepository.deleteById(id);
  }
}
