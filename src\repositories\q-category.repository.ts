import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {QCategory, QCategoryRelations, QTopic} from '../models';
import {QTopicRepository} from './q-topic.repository';

export class QCategoryRepository extends DefaultCrudRepository<
  QCategory,
  typeof QCategory.prototype.id,
  QCategoryRelations
> {

  public readonly qTopics: HasManyRepositoryFactory<QTopic, typeof QCategory.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('QTopicRepository') protected qTopicRepositoryGetter: Getter<QTopicRepository>,
  ) {
    super(QCategory, dataSource);
    this.qTopics = this.createHasManyRepositoryFactoryFor('qTopics', qTopicRepositoryGetter,);
    this.registerInclusionResolver('qTopics', this.qTopics.inclusionResolver);
  }
}
