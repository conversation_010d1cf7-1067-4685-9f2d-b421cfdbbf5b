import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SubmitRf} from '../models';
import {SubmitRfRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SubmitRfController {
  constructor(
    @repository(SubmitRfRepository)
    public submitRfRepository : SubmitRfRepository,
  ) {}

  @post('/submit-rfs')
  @response(200, {
    description: 'SubmitRf model instance',
    content: {'application/json': {schema: getModelSchemaRef(SubmitRf)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitRf, {
            title: 'NewSubmitRf',
            exclude: ['id'],
          }),
        },
      },
    })
    submitRf: Omit<SubmitRf, 'id'>,
  ): Promise<SubmitRf> {
    return this.submitRfRepository.create(submitRf);
  }

  @get('/submit-rfs/count')
  @response(200, {
    description: 'SubmitRf model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SubmitRf) where?: Where<SubmitRf>,
  ): Promise<Count> {
    return this.submitRfRepository.count(where);
  }

  @get('/submit-rfs')
  @response(200, {
    description: 'Array of SubmitRf model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SubmitRf, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SubmitRf) filter?: Filter<SubmitRf>,
  ): Promise<SubmitRf[]> {
    return this.submitRfRepository.find(filter);
  }

  // @patch('/submit-rfs')
  // @response(200, {
  //   description: 'SubmitRf PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitRf, {partial: true}),
  //       },
  //     },
  //   })
  //   submitRf: SubmitRf,
  //   @param.where(SubmitRf) where?: Where<SubmitRf>,
  // ): Promise<Count> {
  //   return this.submitRfRepository.updateAll(submitRf, where);
  // }

  @get('/submit-rfs/{id}')
  @response(200, {
    description: 'SubmitRf model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SubmitRf, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SubmitRf, {exclude: 'where'}) filter?: FilterExcludingWhere<SubmitRf>
  ): Promise<SubmitRf> {
    return this.submitRfRepository.findById(id, filter);
  }

  @patch('/submit-rfs/{id}')
  @response(204, {
    description: 'SubmitRf PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitRf, {partial: true}),
        },
      },
    })
    submitRf: SubmitRf,
  ): Promise<void> {
    await this.submitRfRepository.updateById(id, submitRf);
  }

  @put('/submit-rfs/{id}')
  @response(204, {
    description: 'SubmitRf PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() submitRf: SubmitRf,
  ): Promise<void> {
    await this.submitRfRepository.replaceById(id, submitRf);
  }

  @del('/submit-rfs/{id}')
  @response(204, {
    description: 'SubmitRf DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.submitRfRepository.deleteById(id);
  }
}
