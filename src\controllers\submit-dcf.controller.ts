import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SubmitDcf} from '../models';
import {SubmitDcfRepository,DpReportRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';




export class SubmitDcfController {
  constructor(
    @repository(SubmitDcfRepository)
    public submitDcfRepository : SubmitDcfRepository,

    @repository(DpReportRepository)
    public dpReportRepository : DpReportRepository,
   
  ) {}

  @post('/submit-dcfs')
  @response(200, {
    description: 'SubmitDcf model instance',
    content: {'application/json': {schema: getModelSchemaRef(SubmitDcf)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitDcf, {
            title: 'NewSubmitDcf',
            exclude: ['id'],
          }),
        },
      },
    })
    submitDcf: Omit<SubmitDcf, 'id'>,
  ): Promise<SubmitDcf> {
    return this.submitDcfRepository.create(submitDcf);
  }

  @get('/submit-dcfs/count')
  @response(200, {
    description: 'SubmitDcf model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SubmitDcf) where?: Where<SubmitDcf>,
  ): Promise<Count> {
    return this.submitDcfRepository.count(where);
  }

  @get('/submit-dcfs')
  @response(200, {
    description: 'Array of SubmitDcf model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SubmitDcf, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SubmitDcf) filter?: Filter<SubmitDcf>,
  ): Promise<SubmitDcf[]> {
    return this.submitDcfRepository.find(filter);
  }

  // @patch('/submit-dcfs')
  // @response(200, {
  //   description: 'SubmitDcf PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitDcf, {partial: true}),
  //       },
  //     },
  //   })
  //   submitDcf: SubmitDcf,
  //   @param.where(SubmitDcf) where?: Where<SubmitDcf>,
  // ): Promise<Count> {
  //   return this.submitDcfRepository.updateAll(submitDcf, where);
  // }

  @get('/submit-dcfs/{id}')
  @response(200, {
    description: 'SubmitDcf model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SubmitDcf, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SubmitDcf, {exclude: 'where'}) filter?: FilterExcludingWhere<SubmitDcf>
  ): Promise<SubmitDcf> {
    return this.submitDcfRepository.findById(id, filter);
  }

  @patch('/submit-dcfs/{id}')
  @response(204, {
    description: 'SubmitDcf PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitDcf, {partial: true}),
        },
      },
    })
    submitDcf: SubmitDcf,
  ): Promise<void> {
    await this.submitDcfRepository.updateById(id, submitDcf);
  }

  @put('/submit-dcfs/{id}')
  @response(204, {
    description: 'SubmitDcf PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() submitDcf: SubmitDcf,
  ): Promise<void> {
    await this.submitDcfRepository.replaceById(id, submitDcf);
  }

  @del('/submit-dcfs/{id}')
  @response(204, {
    description: 'SubmitDcf DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.submitDcfRepository.deleteById(id);
   
  }


  @del('/submitDcf-dpReports-by-submitId/{id}')
  @response(204, {
    description: 'SubmitDcf & DP Reports DELETE success',
  })
  async deleteDCFDPById(@param.path.number('id') id: number): Promise<void> {
    await this.dpReportRepository.deleteAll({submitId: id});
    await this.submitDcfRepository.deleteById(id);

  }

  
}





