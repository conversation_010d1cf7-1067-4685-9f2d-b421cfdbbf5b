import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  DealerChecklistSubmission,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileDealerChecklistSubmissionController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/dealer-checklist-submissions', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many DealerChecklistSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DealerChecklistSubmission)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<DealerChecklistSubmission>,
  ): Promise<DealerChecklistSubmission[]> {
    return this.userProfileRepository.dealerChecklistSubmissions(id).find(filter);
  }

  @post('/user-profiles/{id}/dealer-checklist-submissions', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerChecklistSubmission)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerChecklistSubmission, {
            title: 'NewDealerChecklistSubmissionInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) dealerChecklistSubmission: Omit<DealerChecklistSubmission, 'id'>,
  ): Promise<DealerChecklistSubmission> {
    return this.userProfileRepository.dealerChecklistSubmissions(id).create(dealerChecklistSubmission);
  }

  @patch('/user-profiles/{id}/dealer-checklist-submissions', {
    responses: {
      '200': {
        description: 'UserProfile.DealerChecklistSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerChecklistSubmission, {partial: true}),
        },
      },
    })
    dealerChecklistSubmission: Partial<DealerChecklistSubmission>,
    @param.query.object('where', getWhereSchemaFor(DealerChecklistSubmission)) where?: Where<DealerChecklistSubmission>,
  ): Promise<Count> {
    return this.userProfileRepository.dealerChecklistSubmissions(id).patch(dealerChecklistSubmission, where);
  }

  @del('/user-profiles/{id}/dealer-checklist-submissions', {
    responses: {
      '200': {
        description: 'UserProfile.DealerChecklistSubmission DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(DealerChecklistSubmission)) where?: Where<DealerChecklistSubmission>,
  ): Promise<Count> {
    return this.userProfileRepository.dealerChecklistSubmissions(id).delete(where);
  }
}
