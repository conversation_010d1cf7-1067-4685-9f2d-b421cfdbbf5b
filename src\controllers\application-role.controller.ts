import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ApplicationRoles} from '../models';
import {ApplicationRolesRepository} from '../repositories';

export class ApplicationRoleController {
  constructor(
    @repository(ApplicationRolesRepository)
    public applicationRolesRepository : ApplicationRolesRepository,
  ) {}

  @post('/application-roles')
  @response(200, {
    description: 'ApplicationRoles model instance',
    content: {'application/json': {schema: getModelSchemaRef(ApplicationRoles)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ApplicationRoles, {
            title: 'NewApplicationRoles',
            exclude: ['id'],
          }),
        },
      },
    })
    applicationRoles: Omit<ApplicationRoles, 'id'>,
  ): Promise<ApplicationRoles> {
    return this.applicationRolesRepository.create(applicationRoles);
  }

  @get('/application-roles/count')
  @response(200, {
    description: 'ApplicationRoles model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ApplicationRoles) where?: Where<ApplicationRoles>,
  ): Promise<Count> {
    return this.applicationRolesRepository.count(where);
  }

  @get('/application-roles')
  @response(200, {
    description: 'Array of ApplicationRoles model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ApplicationRoles, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ApplicationRoles) filter?: Filter<ApplicationRoles>,
  ): Promise<ApplicationRoles[]> {
    return this.applicationRolesRepository.find(filter);
  }

  @patch('/application-roles')
  @response(200, {
    description: 'ApplicationRoles PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ApplicationRoles, {partial: true}),
        },
      },
    })
    applicationRoles: ApplicationRoles,
    @param.where(ApplicationRoles) where?: Where<ApplicationRoles>,
  ): Promise<Count> {
    return this.applicationRolesRepository.updateAll(applicationRoles, where);
  }

  @get('/application-roles/{id}')
  @response(200, {
    description: 'ApplicationRoles model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ApplicationRoles, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ApplicationRoles, {exclude: 'where'}) filter?: FilterExcludingWhere<ApplicationRoles>
  ): Promise<ApplicationRoles> {
    return this.applicationRolesRepository.findById(id, filter);
  }

  @patch('/application-roles/{id}')
  @response(204, {
    description: 'ApplicationRoles PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ApplicationRoles, {partial: true}),
        },
      },
    })
    applicationRoles: ApplicationRoles,
  ): Promise<void> {
    await this.applicationRolesRepository.updateById(id, applicationRoles);
  }

  @put('/application-roles/{id}')
  @response(204, {
    description: 'ApplicationRoles PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() applicationRoles: ApplicationRoles,
  ): Promise<void> {
    await this.applicationRolesRepository.replaceById(id, applicationRoles);
  }

  @del('/application-roles/{id}')
  @response(204, {
    description: 'ApplicationRoles DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.applicationRolesRepository.deleteById(id);
  }
}
