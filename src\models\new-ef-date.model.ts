import {Entity, model, property, hasMany} from '@loopback/repository';
import {NewEf} from './new-ef.model';

@model()
export class NewEfDate extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  start?: string;

  @property({
    type: 'string',
  })
  end?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'any',
  })
  extra?: any;

  @property({
    type: 'number',
  })
  newEfStdId?: number;

  @hasMany(() => NewEf)
  newEfs: NewEf[];

  constructor(data?: Partial<NewEfDate>) {
    super(data);
  }
}

export interface NewEfDateRelations {
  // describe navigational properties here
}

export type NewEfDateWithRelations = NewEfDate & NewEfDateRelations;
