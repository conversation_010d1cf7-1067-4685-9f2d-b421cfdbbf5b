import {
  repository,
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
} from '@loopback/rest';
import {
  DealerAuditorChecklistSubmission,
  VendorCode,
} from '../models';
import {DealerAuditorChecklistSubmissionRepository} from '../repositories';

export class DealerAuditorChecklistSubmissionVendorCodeController {
  constructor(
    @repository(DealerAuditorChecklistSubmissionRepository)
    public dealerAuditorChecklistSubmissionRepository: DealerAuditorChecklistSubmissionRepository,
  ) { }

  @get('/dealer-auditor-checklist-submissions/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to DealerAuditorChecklistSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof DealerAuditorChecklistSubmission.prototype.id,
  ): Promise<VendorCode> {
    return this.dealerAuditorChecklistSubmissionRepository.vendor(id);
  }
}
