import {Entity, model, property} from '@loopback/repository';

@model()
export class AssignSrfUser extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  site?: number;

  @property({
    type: 'any',
  })
  comments?: any;

  @property({
    type: 'number',
  })
  frequency?: number;

  @property({
    type: 'number',
  })
  creator_id?: number;

  @property({
    type: 'number',
  })
  modifier_id?: number;

  @property({
    type: 'number',
  })
  type?: number;



  @property({
    type: 'array',
    itemType: 'any',
  })
  approver_id?: any[];


  @property({
    type: 'number',
  })
  user_id?: number;

  @property({
    type: 'number',
  })
  reviewer_id?: number;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  start_date?: string;

  @property({
    type: 'string',
  })
  end_date?: string;

  @property({
    type: 'number',
  })
  srfId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<AssignSrfUser>) {
    super(data);
  }
}

export interface AssignSrfUserRelations {
  // describe navigational properties here
}

export type AssignSrfUserWithRelations = AssignSrfUser & AssignSrfUserRelations;
