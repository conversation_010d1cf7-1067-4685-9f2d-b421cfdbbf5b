import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SupplyCategory} from '../models';
import {SupplyCategoryRepository} from '../repositories';

export class SupplyCatgoryController {
  constructor(
    @repository(SupplyCategoryRepository)
    public supplyCategoryRepository : SupplyCategoryRepository,
  ) {}

  @post('/supply-categories')
  @response(200, {
    description: 'SupplyCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplyCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplyCategory, {
            title: 'NewSupplyCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    supplyCategory: Omit<SupplyCategory, 'id'>,
  ): Promise<SupplyCategory> {
    return this.supplyCategoryRepository.create(supplyCategory);
  }

  @get('/supply-categories/count')
  @response(200, {
    description: 'SupplyCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplyCategory) where?: Where<SupplyCategory>,
  ): Promise<Count> {
    return this.supplyCategoryRepository.count(where);
  }

  @get('/supply-categories')
  @response(200, {
    description: 'Array of SupplyCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplyCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplyCategory) filter?: Filter<SupplyCategory>,
  ): Promise<SupplyCategory[]> {
    return this.supplyCategoryRepository.find(filter);
  }

  @patch('/supply-categories')
  @response(200, {
    description: 'SupplyCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplyCategory, {partial: true}),
        },
      },
    })
    supplyCategory: SupplyCategory,
    @param.where(SupplyCategory) where?: Where<SupplyCategory>,
  ): Promise<Count> {
    return this.supplyCategoryRepository.updateAll(supplyCategory, where);
  }

  @get('/supply-categories/{id}')
  @response(200, {
    description: 'SupplyCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplyCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SupplyCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplyCategory>
  ): Promise<SupplyCategory> {
    return this.supplyCategoryRepository.findById(id, filter);
  }

  @patch('/supply-categories/{id}')
  @response(204, {
    description: 'SupplyCategory PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplyCategory, {partial: true}),
        },
      },
    })
    supplyCategory: SupplyCategory,
  ): Promise<void> {
    await this.supplyCategoryRepository.updateById(id, supplyCategory);
  }

  @put('/supply-categories/{id}')
  @response(204, {
    description: 'SupplyCategory PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() supplyCategory: SupplyCategory,
  ): Promise<void> {
    await this.supplyCategoryRepository.replaceById(id, supplyCategory);
  }

  @del('/supply-categories/{id}')
  @response(204, {
    description: 'SupplyCategory DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.supplyCategoryRepository.deleteById(id);
  }
}
