import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DdCategoryOne} from '../models';
import {DdCategoryOneRepository} from '../repositories';

export class DdCategoryOneController {
  constructor(
    @repository(DdCategoryOneRepository)
    public initCategoryUnitRepository: DdCategoryOneRepository,
  ) { }

  @post('/dd-category-ones')
  @response(200, {
    description: 'DdCategoryOne model instance',
    content: {'application/json': {schema: getModelSchemaRef(DdCategoryOne)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DdCategoryOne, {
            title: 'NewDdCategoryOne',
            exclude: ['id'],
          }),
        },
      },
    })
    initCategoryUnit: Omit<DdCategoryOne, 'id'>,
  ): Promise<DdCategoryOne> {
    return this.initCategoryUnitRepository.create(initCategoryUnit);
  }

  @get('/dd-category-ones/count')
  @response(200, {
    description: 'DdCategoryOne model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DdCategoryOne) where?: Where<DdCategoryOne>,
  ): Promise<Count> {
    return this.initCategoryUnitRepository.count(where);
  }

  @get('/dd-category-ones')
  @response(200, {
    description: 'Array of DdCategoryOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DdCategoryOne, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DdCategoryOne) filter?: Filter<DdCategoryOne>,
  ): Promise<DdCategoryOne[]> {
    return this.initCategoryUnitRepository.find(filter);
  }

  @patch('/dd-category-ones')
  @response(200, {
    description: 'DdCategoryOne PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DdCategoryOne, {partial: true}),
        },
      },
    })
    initCategoryUnit: DdCategoryOne,
    @param.where(DdCategoryOne) where?: Where<DdCategoryOne>,
  ): Promise<Count> {
    return this.initCategoryUnitRepository.updateAll(initCategoryUnit, where);
  }

  @get('/dd-category-ones/{id}')
  @response(200, {
    description: 'DdCategoryOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DdCategoryOne, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DdCategoryOne, {exclude: 'where'}) filter?: FilterExcludingWhere<DdCategoryOne>
  ): Promise<DdCategoryOne> {
    return this.initCategoryUnitRepository.findById(id, filter);
  }

  @patch('/dd-category-ones/{id}')
  @response(204, {
    description: 'DdCategoryOne PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DdCategoryOne, {partial: true}),
        },
      },
    })
    initCategoryUnit: DdCategoryOne,
  ): Promise<void> {
    await this.initCategoryUnitRepository.updateById(id, initCategoryUnit);
  }

  @put('/dd-category-ones/{id}')
  @response(204, {
    description: 'DdCategoryOne PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() initCategoryUnit: DdCategoryOne,
  ): Promise<void> {
    await this.initCategoryUnitRepository.replaceById(id, initCategoryUnit);
  }

  @del('/dd-category-ones/{id}')
  @response(204, {
    description: 'DdCategoryOne DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.initCategoryUnitRepository.deleteById(id);
  }
}
