import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewCategory, NewCategoryRelations, NewTopic} from '../models';
import {NewTopicRepository} from './new-topic.repository';

export class NewCategoryRepository extends DefaultCrudRepository<
  NewCategory,
  typeof NewCategory.prototype.id,
  NewCategoryRelations
> {

  public readonly newTopics: HasManyRepositoryFactory<NewTopic, typeof NewCategory.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewTopicRepository') protected newTopicRepositoryGetter: Getter<NewTopicRepository>,
  ) {
    super(NewCategory, dataSource);
    this.newTopics = this.createHasManyRepositoryFactoryFor('newTopics', newTopicRepositoryGetter,);
    this.registerInclusionResolver('newTopics', this.newTopics.inclusionResolver);
  }
}
