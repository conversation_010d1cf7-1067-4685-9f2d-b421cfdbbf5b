import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ConsolidateFormCollection, ConsolidateFormCollectionRelations} from '../models';

export class ConsolidateFormCollectionRepository extends DefaultCrudRepository<
  ConsolidateFormCollection,
  typeof ConsolidateFormCollection.prototype.id,
  ConsolidateFormCollectionRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ConsolidateFormCollection, dataSource);
  }
}
