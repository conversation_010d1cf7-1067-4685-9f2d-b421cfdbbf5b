import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignSrfUser, AssignSrfUserRelations} from '../models';

export class AssignSrfUserRepository extends DefaultCrudRepository<
  AssignSrfUser,
  typeof AssignSrfUser.prototype.id,
  AssignSrfUserRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AssignSrfUser, dataSource);
  }
}
