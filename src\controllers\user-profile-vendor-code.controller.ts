import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  VendorCode,
} from '../models';
import {UserProfileRepository, VendorCodeRepository} from '../repositories';

export class UserProfileVendorCodeController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,

  ) { }

  @get('/user-profiles/{id}/vendor-codes', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many VendorCode',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(VendorCode)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<VendorCode>,
  ): Promise<VendorCode[]> {
    return this.userProfileRepository.vendorCodes(id).find(filter);
  }

  @post('/user-profiles/{id}/vendor-codes', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(VendorCode)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(VendorCode, {
            title: 'NewVendorCodeInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) vendorCode: Omit<VendorCode, 'id'>,
  ): Promise<any> {
    let found = await this.userProfileRepository.findById(id);
    if (found) {

      let old = await this.vendorCodeRepository.findOne({where: {userProfileId: id, clientId: found.clientId, code: vendorCode.code}})
      if (!old) {
        let result = await this.userProfileRepository.vendorCodes(id).create(vendorCode);
        return {status: true, message: "Vendor code added", data: result}

      } else {
        return {status: false, message: "Vendor already used"}

      }
    } else {
      return {status: false, message: "User not found"}
    }

  }

  @patch('/user-profiles/{id}/vendor-codes', {
    responses: {
      '200': {
        description: 'UserProfile.VendorCode PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(VendorCode, {partial: true}),
        },
      },
    })
    vendorCode: Partial<VendorCode>,
    @param.query.object('where', getWhereSchemaFor(VendorCode)) where?: Where<VendorCode>,
  ): Promise<Count> {
    return this.userProfileRepository.vendorCodes(id).patch(vendorCode, where);
  }

  @del('/user-profiles/{id}/vendor-codes', {
    responses: {
      '200': {
        description: 'UserProfile.VendorCode DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(VendorCode)) where?: Where<VendorCode>,
  ): Promise<Count> {
    return this.userProfileRepository.vendorCodes(id).delete(where);
  }
}
