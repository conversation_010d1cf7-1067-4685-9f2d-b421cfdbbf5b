import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewEfDate} from '../models';
import {NewEfDateRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfDateController {
  constructor(
    @repository(NewEfDateRepository)
    public newEfDateRepository : NewEfDateRepository,
  ) {}

  @post('/new-ef-dates')
  @response(200, {
    description: 'NewEfDate model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEfDate)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfDate, {
            title: 'NewNewEfDate',
            exclude: ['id'],
          }),
        },
      },
    })
    newEfDate: Omit<NewEfDate, 'id'>,
  ): Promise<NewEfDate> {
    return this.newEfDateRepository.create(newEfDate);
  }

  @get('/new-ef-dates/count')
  @response(200, {
    description: 'NewEfDate model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEfDate) where?: Where<NewEfDate>,
  ): Promise<Count> {
    return this.newEfDateRepository.count(where);
  }

  @get('/new-ef-dates')
  @response(200, {
    description: 'Array of NewEfDate model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEfDate, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEfDate) filter?: Filter<NewEfDate>,
  ): Promise<NewEfDate[]> {
    return this.newEfDateRepository.find(filter);
  }

  // @patch('/new-ef-dates')
  // @response(200, {
  //   description: 'NewEfDate PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfDate, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfDate: NewEfDate,
  //   @param.where(NewEfDate) where?: Where<NewEfDate>,
  // ): Promise<Count> {
  //   return this.newEfDateRepository.updateAll(newEfDate, where);
  // }

  @get('/new-ef-dates/{id}')
  @response(200, {
    description: 'NewEfDate model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEfDate, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEfDate, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEfDate>
  ): Promise<NewEfDate> {
    return this.newEfDateRepository.findById(id, filter);
  }

  @patch('/new-ef-dates/{id}')
  @response(204, {
    description: 'NewEfDate PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfDate, {partial: true}),
        },
      },
    })
    newEfDate: NewEfDate,
  ): Promise<void> {
    await this.newEfDateRepository.updateById(id, newEfDate);
  }

  @put('/new-ef-dates/{id}')
  @response(204, {
    description: 'NewEfDate PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEfDate: NewEfDate,
  ): Promise<void> {
    await this.newEfDateRepository.replaceById(id, newEfDate);
  }

  @del('/new-ef-dates/{id}')
  @response(204, {
    description: 'NewEfDate DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfDateRepository.deleteById(id);
  }
}
