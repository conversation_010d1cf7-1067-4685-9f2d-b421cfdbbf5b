import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SubSurvey,
  Response,
} from '../models';
import {SubSurveyRepository} from '../repositories';

export class SubSurveyResponseController {
  constructor(
    @repository(SubSurveyRepository) protected subSurveyRepository: SubSurveyRepository,
  ) { }

  @get('/sub-surveys/{id}/responses', {
    responses: {
      '200': {
        description: 'Array of SubSurvey has many Response',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Response)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Response>,
  ): Promise<Response[]> {
    return this.subSurveyRepository.responses(id).find(filter);
  }

  @post('/sub-surveys/{id}/responses', {
    responses: {
      '200': {
        description: 'SubSurvey model instance',
        content: {'application/json': {schema: getModelSchemaRef(Response)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof SubSurvey.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Response, {
            title: 'NewResponseInSubSurvey',
            exclude: ['id'],
            optional: ['subSurveyId']
          }),
        },
      },
    }) response: Omit<Response, 'id'>,
  ): Promise<Response> {
    return this.subSurveyRepository.responses(id).create(response);
  }

  @patch('/sub-surveys/{id}/responses', {
    responses: {
      '200': {
        description: 'SubSurvey.Response PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Response, {partial: true}),
        },
      },
    })
    response: Partial<Response>,
    @param.query.object('where', getWhereSchemaFor(Response)) where?: Where<Response>,
  ): Promise<Count> {
    return this.subSurveyRepository.responses(id).patch(response, where);
  }

  @del('/sub-surveys/{id}/responses', {
    responses: {
      '200': {
        description: 'SubSurvey.Response DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Response)) where?: Where<Response>,
  ): Promise<Count> {
    return this.subSurveyRepository.responses(id).delete(where);
  }
}
