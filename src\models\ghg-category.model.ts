import {Entity, hasMany, model, property} from '@loopback/repository';
import {GhgSubCategory} from './ghg-sub-category.model';

@model()
export class GhgCategory extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;
  @property({
    type: 'number',
  })
  newEfStdId?: number;

  @hasMany(() => GhgSubCategory)
  ghgSubCategories: GhgSubCategory[];

  constructor(data?: Partial<GhgCategory>) {
    super(data);
  }
}

export interface GhgCategoryRelations {
  // describe navigational properties here
}

export type GhgCategoryWithRelations = GhgCategory & GhgCategoryRelations;
