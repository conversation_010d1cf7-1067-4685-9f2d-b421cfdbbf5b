import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssessmentSubSection1} from '../models';
import {AssessmentSubSection1Repository} from '../repositories';

export class AssessmentSubSection1Controller {
  constructor(
    @repository(AssessmentSubSection1Repository)
    public assessmentSubSection1Repository : AssessmentSubSection1Repository,
  ) {}

  @post('/assessment-sub-section1s')
  @response(200, {
    description: 'AssessmentSubSection1 model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssessmentSubSection1)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection1, {
            title: 'NewAssessmentSubSection1',
            exclude: ['id'],
          }),
        },
      },
    })
    assessmentSubSection1: Omit<AssessmentSubSection1, 'id'>,
  ): Promise<AssessmentSubSection1> {
    return this.assessmentSubSection1Repository.create(assessmentSubSection1);
  }

  @get('/assessment-sub-section1s/count')
  @response(200, {
    description: 'AssessmentSubSection1 model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssessmentSubSection1) where?: Where<AssessmentSubSection1>,
  ): Promise<Count> {
    return this.assessmentSubSection1Repository.count(where);
  }

  @get('/assessment-sub-section1s')
  @response(200, {
    description: 'Array of AssessmentSubSection1 model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssessmentSubSection1, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssessmentSubSection1) filter?: Filter<AssessmentSubSection1>,
  ): Promise<AssessmentSubSection1[]> {
    return this.assessmentSubSection1Repository.find(filter);
  }

  @patch('/assessment-sub-section1s')
  @response(200, {
    description: 'AssessmentSubSection1 PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection1, {partial: true}),
        },
      },
    })
    assessmentSubSection1: AssessmentSubSection1,
    @param.where(AssessmentSubSection1) where?: Where<AssessmentSubSection1>,
  ): Promise<Count> {
    return this.assessmentSubSection1Repository.updateAll(assessmentSubSection1, where);
  }

  @get('/assessment-sub-section1s/{id}')
  @response(200, {
    description: 'AssessmentSubSection1 model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssessmentSubSection1, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AssessmentSubSection1, {exclude: 'where'}) filter?: FilterExcludingWhere<AssessmentSubSection1>
  ): Promise<AssessmentSubSection1> {
    return this.assessmentSubSection1Repository.findById(id, filter);
  }

  @patch('/assessment-sub-section1s/{id}')
  @response(204, {
    description: 'AssessmentSubSection1 PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection1, {partial: true}),
        },
      },
    })
    assessmentSubSection1: AssessmentSubSection1,
  ): Promise<void> {
    await this.assessmentSubSection1Repository.updateById(id, assessmentSubSection1);
  }

  @put('/assessment-sub-section1s/{id}')
  @response(204, {
    description: 'AssessmentSubSection1 PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() assessmentSubSection1: AssessmentSubSection1,
  ): Promise<void> {
    await this.assessmentSubSection1Repository.replaceById(id, assessmentSubSection1);
  }

  @del('/assessment-sub-section1s/{id}')
  @response(204, {
    description: 'AssessmentSubSection1 DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.assessmentSubSection1Repository.deleteById(id);
  }
}
