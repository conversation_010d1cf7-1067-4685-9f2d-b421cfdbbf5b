import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SubmitRfNew, SubmitRfNewRelations} from '../models';

export class SubmitRfNewRepository extends DefaultCrudRepository<
  SubmitRfNew,
  typeof SubmitRfNew.prototype.id,
  SubmitRfNewRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SubmitRfNew, dataSource);
  }
}
