import {Entity, model, property} from '@loopback/repository';

@model()
export class QlListingFilter extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'number',
  })
  category?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  topic?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  framework?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  metric?: number[];

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<QlListingFilter>) {
    super(data);
  }
}

export interface QlListingFilterRelations {
  // describe navigational properties here
}

export type QlListingFilterWithRelations = QlListingFilter & QlListingFilterRelations;
