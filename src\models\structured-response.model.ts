import {belongsTo, Entity, model, property} from '@loopback/repository';
import {FormCollection} from './form-collection.model';
import {QuantitativeSubmission} from './quantitative-submission.model';

@model()
export class StructuredResponse extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'any',
  })
  maskId?: any;

  @property({
    type: 'any',
  })
  reportedDate?: any;

  @property({
    type: 'any',
  })
  uniqueId?: any;

  @property({
    type: 'any',
  })
  efValue?: any;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  title?: string;

  @property({
    type: 'string',
  })
  date?: string;

  @property({
    type: 'string',
  })
  label?: string;

  @property({
    type: 'any',
  })
  parentId?: any;

  @property({
    type: 'boolean',
  })
  isNull?: boolean;

  @property({
    type: 'boolean',
  })
  isManualForm?: boolean;
  @property({
    type: 'any',
  })
  valueType?: any;

  @property({
    type: 'any',
  })
  currentId?: any;
  @property({
    type: 'any',
  })
  additionalValue1?: any;
  @property({
    type: 'any',
  })
  additionalValue2?: any;
  @property({
    type: 'any',
  })
  additionalValue3?: any;
  @property({
    type: 'any',
  })
  additionalValue4?: any;
  @property({
    type: 'any',
  })
  attachment?: any;

  @property({
    type: 'any',
  })
  formType?: any;

  @property({
    type: 'any',
  })
  dataType?: any;
  @property({
    type: 'any',
  })
  subCategory1?: any;

  @property({
    type: 'any',
  })
  subCategory2?: any;

  @property({
    type: 'any',
  })
  subCategory3?: any;
  @property({
    type: 'any',
  })
  subCategory4?: any;


  @property({
    type: 'any',
  })
  value?: any;
  @property({
    type: 'string',
  })
  created_on?: string;
  @property({
    type: 'string',
  })
  modified_on?: string;
  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'number',
  })
  modified_by?: number;
  @property({
    type: 'array',
    itemType: 'string'
  })
  reporting_period?: string[];
  @property({
    type: 'any'
  })
  conversionValue?: any;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  conversionUnit?: string | null;



  @property({
    type: 'any',
  })
  uom?: any;

  @belongsTo(() => FormCollection)
  dcfId: number;

  @belongsTo(() => QuantitativeSubmission)
  submitDcfId: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<StructuredResponse>) {
    super(data);
  }
}

export interface StructuredResponseRelations {
  // describe navigational properties here
}

export type StructuredResponseWithRelations = StructuredResponse & StructuredResponseRelations;
