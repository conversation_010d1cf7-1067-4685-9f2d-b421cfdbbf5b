import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewsCirculation} from '../models';
import {NewsCirculationRepository} from '../repositories';

export class NewsCirculationController {
  constructor(
    @repository(NewsCirculationRepository)
    public newsCirculationRepository : NewsCirculationRepository,
  ) {}

  @post('/news-circulations')
  @response(200, {
    description: 'NewsCirculation model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewsCirculation)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewsCirculation, {
            title: 'NewNewsCirculation',
            exclude: ['id'],
          }),
        },
      },
    })
    newsCirculation: Omit<NewsCirculation, 'id'>,
  ): Promise<NewsCirculation> {
    return this.newsCirculationRepository.create(newsCirculation);
  }

  @get('/news-circulations/count')
  @response(200, {
    description: 'NewsCirculation model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewsCirculation) where?: Where<NewsCirculation>,
  ): Promise<Count> {
    return this.newsCirculationRepository.count(where);
  }

  @get('/news-circulations')
  @response(200, {
    description: 'Array of NewsCirculation model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewsCirculation, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewsCirculation) filter?: Filter<NewsCirculation>,
  ): Promise<NewsCirculation[]> {
    return this.newsCirculationRepository.find(filter);
  }

  @patch('/news-circulations')
  @response(200, {
    description: 'NewsCirculation PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewsCirculation, {partial: true}),
        },
      },
    })
    newsCirculation: NewsCirculation,
    @param.where(NewsCirculation) where?: Where<NewsCirculation>,
  ): Promise<Count> {
    return this.newsCirculationRepository.updateAll(newsCirculation, where);
  }

  @get('/news-circulations/{id}')
  @response(200, {
    description: 'NewsCirculation model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewsCirculation, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewsCirculation, {exclude: 'where'}) filter?: FilterExcludingWhere<NewsCirculation>
  ): Promise<NewsCirculation> {
    return this.newsCirculationRepository.findById(id, filter);
  }

  @patch('/news-circulations/{id}')
  @response(204, {
    description: 'NewsCirculation PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewsCirculation, {partial: true}),
        },
      },
    })
    newsCirculation: NewsCirculation,
  ): Promise<void> {
    await this.newsCirculationRepository.updateById(id, newsCirculation);
  }

  @put('/news-circulations/{id}')
  @response(204, {
    description: 'NewsCirculation PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newsCirculation: NewsCirculation,
  ): Promise<void> {
    await this.newsCirculationRepository.replaceById(id, newsCirculation);
  }

  @del('/news-circulations/{id}')
  @response(204, {
    description: 'NewsCirculation DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newsCirculationRepository.deleteById(id);
  }
}
