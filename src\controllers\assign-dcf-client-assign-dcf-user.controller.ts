import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AssignDcfClient,
  AssignDcfUser,
} from '../models';
import {AssignDcfClientRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';



export class AssignDcfClientAssignDcfUserController {
  constructor(
    @repository(AssignDcfClientRepository) protected assignDcfClientRepository: AssignDcfClientRepository,
  ) { }

  @get('/assign-dcf-clients/{id}/assign-dcf-users', {
    responses: {
      '200': {
        description: 'Array of AssignDcfClient has many AssignDcfUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDcfUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDcfUser>,
  ): Promise<AssignDcfUser[]> {
    return this.assignDcfClientRepository.assignDcfUsers(id).find(filter);
  }

  @post('/assign-dcf-clients/{id}/assign-dcf-users', {
    responses: {
      '200': {
        description: 'AssignDcfClient model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDcfUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof AssignDcfClient.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfUser, {
            title: 'NewAssignDcfUserInAssignDcfClient',
            exclude: ['id'],
            optional: ['assignDcfClientId']
          }),
        },
      },
    }) assignDcfUser: Omit<AssignDcfUser, 'id'>,
  ): Promise<AssignDcfUser> {
    
    return this.assignDcfClientRepository.assignDcfUsers(id).create(assignDcfUser);
  }

  // @patch('/assign-dcf-clients/{id}/assign-dcf-users', {
  //   responses: {
  //     '200': {
  //       description: 'AssignDcfClient.AssignDcfUser PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfUser, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfUser: Partial<AssignDcfUser>,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfUser)) where?: Where<AssignDcfUser>,
  // ): Promise<Count> {
  //   return this.assignDcfClientRepository.assignDcfUsers(id).patch(assignDcfUser, where);
  // }

  // @del('/assign-dcf-clients/{id}/assign-dcf-users', {
  //   responses: {
  //     '200': {
  //       description: 'AssignDcfClient.AssignDcfUser DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfUser)) where?: Where<AssignDcfUser>,
  // ): Promise<Count> {
  //   return this.assignDcfClientRepository.assignDcfUsers(id).delete(where);
  // }

}
