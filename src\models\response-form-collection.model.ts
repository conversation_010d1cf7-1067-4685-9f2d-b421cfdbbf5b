import {Entity, model, property} from '@loopback/repository';

@model()
export class ResponseFormCollection extends Entity {

  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;
  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  tags?: any;
  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      nullable: true,
    }
  })
  comments?: string | null;

  @property({
    type: 'number',
  })
  curator_id?: number;

  @property({
    type: 'number',
  })
  modifier_id?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  updated?: string;

  constructor(data?: Partial<ResponseFormCollection>) {
    super(data);
  }
}

export interface ResponseFormCollectionRelations {
  // describe navigational properties here
}

export type ResponseFormCollectionWithRelations = ResponseFormCollection & ResponseFormCollectionRelations;
