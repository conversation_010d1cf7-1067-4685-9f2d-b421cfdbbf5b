import {
  Filter,
  repository
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody
} from '@loopback/rest';
import {
  DpReportNew,
  UserProfile,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileDpReportNewController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/dp-report-news', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many DpReportNew',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DpReportNew)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<DpReportNew>,
  ): Promise<DpReportNew[]> {
    return this.userProfileRepository.dpReportNews(id).find(filter);
  }

  @post('/user-profiles/{id}/dp-report-news', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(DpReportNew)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DpReportNew, {
            title: 'NewDpReportNewInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) dpReportNew: Omit<DpReportNew, 'id'>,
  ): Promise<DpReportNew> {
    return this.userProfileRepository.dpReportNews(id).create(dpReportNew);
  }

  // @patch('/user-profiles/{id}/dp-report-news', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.DpReportNew PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(DpReportNew, {partial: true}),
  //       },
  //     },
  //   })
  //   dpReportNew: Partial<DpReportNew>,
  //   @param.query.object('where', getWhereSchemaFor(DpReportNew)) where?: Where<DpReportNew>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.dpReportNews(id).patch(dpReportNew, where);
  // }

  // @del('/user-profiles/{id}/dp-report-news', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.DpReportNew DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(DpReportNew)) where?: Where<DpReportNew>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.dpReportNews(id).delete(where);
  // }
}
