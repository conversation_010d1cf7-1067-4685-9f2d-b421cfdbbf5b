import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  AssignDcfSuppliers,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';





export class UserProfileAssignDcfSuppliersController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-dcf-suppliers', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDcfSuppliers',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDcfSuppliers)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDcfSuppliers>,
  ): Promise<AssignDcfSuppliers[]> {
    return this.userProfileRepository.assignDcfSuppliers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-dcf-suppliers', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDcfSuppliers)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfSuppliers, {
            title: 'NewAssignDcfSuppliersInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDcfSuppliers: Omit<AssignDcfSuppliers, 'id'>,
  ): Promise<AssignDcfSuppliers> {
    return this.userProfileRepository.assignDcfSuppliers(id).create(assignDcfSuppliers);
  }

  // @patch('/user-profiles/{id}/assign-dcf-suppliers', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDcfSuppliers PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfSuppliers, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfSuppliers: Partial<AssignDcfSuppliers>,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfSuppliers)) where?: Where<AssignDcfSuppliers>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDcfSuppliers(id).patch(assignDcfSuppliers, where);
  // }

  // @del('/user-profiles/{id}/assign-dcf-suppliers', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDcfSuppliers DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfSuppliers)) where?: Where<AssignDcfSuppliers>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDcfSuppliers(id).delete(where);
  // }

}
