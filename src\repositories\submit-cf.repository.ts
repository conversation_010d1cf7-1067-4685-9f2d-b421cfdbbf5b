import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SubmitCf, SubmitCfRelations} from '../models';

export class SubmitCfRepository extends DefaultCrudRepository<
  SubmitCf,
  typeof SubmitCf.prototype.id,
  SubmitCfRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SubmitCf, dataSource);
  }
}
