import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {StdScope} from '../models';
import {StdScopeRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdScopeController {
  constructor(
    @repository(StdScopeRepository)
    public stdScopeRepository : StdScopeRepository,
  ) {}

  @post('/std-scopes')
  @response(200, {
    description: 'StdScope model instance',
    content: {'application/json': {schema: getModelSchemaRef(StdScope)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdScope, {
            title: 'NewStdScope',
            exclude: ['id'],
          }),
        },
      },
    })
    stdScope: Omit<StdScope, 'id'>,
  ): Promise<StdScope> {
    return this.stdScopeRepository.create(stdScope);
  }

  @get('/std-scopes/count')
  @response(200, {
    description: 'StdScope model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(StdScope) where?: Where<StdScope>,
  ): Promise<Count> {
    return this.stdScopeRepository.count(where);
  }

  @get('/std-scopes')
  @response(200, {
    description: 'Array of StdScope model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(StdScope, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(StdScope) filter?: Filter<StdScope>,
  ): Promise<StdScope[]> {
    return this.stdScopeRepository.find(filter);
  }

  // @patch('/std-scopes')
  // @response(200, {
  //   description: 'StdScope PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StdScope, {partial: true}),
  //       },
  //     },
  //   })
  //   stdScope: StdScope,
  //   @param.where(StdScope) where?: Where<StdScope>,
  // ): Promise<Count> {
  //   return this.stdScopeRepository.updateAll(stdScope, where);
  // }

  @get('/std-scopes/{id}')
  @response(200, {
    description: 'StdScope model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(StdScope, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(StdScope, {exclude: 'where'}) filter?: FilterExcludingWhere<StdScope>
  ): Promise<StdScope> {
    return this.stdScopeRepository.findById(id, filter);
  }

  @patch('/std-scopes/{id}')
  @response(204, {
    description: 'StdScope PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdScope, {partial: true}),
        },
      },
    })
    stdScope: StdScope,
  ): Promise<void> {
    await this.stdScopeRepository.updateById(id, stdScope);
  }

  @put('/std-scopes/{id}')
  @response(204, {
    description: 'StdScope PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() stdScope: StdScope,
  ): Promise<void> {
    await this.stdScopeRepository.replaceById(id, stdScope);
  }

  @del('/std-scopes/{id}')
  @response(204, {
    description: 'StdScope DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.stdScopeRepository.deleteById(id);
  }
}
