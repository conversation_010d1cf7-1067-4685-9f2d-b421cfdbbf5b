import {DefaultCrudRepository} from '@loopback/repository';
import {Helper, HelperRelations} from '../models';
import {MysqlDataSource} from '../datasources';
import {inject} from '@loopback/core';

export class HelperRepository extends DefaultCrudRepository<
  Helper,
  typeof Helper.prototype.id,
  HelperRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(Helper, dataSource);
  }
}
