import {injectable} from '@loopback/core';
import AWS, {SQS, } from 'aws-sdk';

@injectable()
export class SqsService {
  private sqs: SQS;
  private sqsNew: SQS;

  constructor() {
    // Initialize the SQS client
    AWS.config.credentials = new AWS.EC2MetadataCredentials({
      httpOptions: {timeout: 5000},  // 5s to fetch metadata
      maxRetries: 5                  // retry up to 5 times
    });


    // this.sqs = new SQS({
    //   region: process.env.AWS_REGION_EISQR,
    //   accessKeyId: process.env.AWS_ACCESS_KEY_ID_EISQR,
    //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY_EISQR
    // });
    this.sqs = new AWS.SQS({
      region: process.env.AWS_REGION_EISQR
    });
  }




  // async sendEmail_noSecutrity (email: any, title: string, message: string, cc: any): Promise<any> {


  //   const messageBody = JSON.stringify({email: email, cc: cc ? cc : [], "subject": title, "content": message, "deviceARNs": [], "sender": '<EMAIL>'});
  //   const params = {
  //     MessageBody: messageBody,
  //     QueueUrl: `${process.env.AWS_SQS_URL_EISQR}`
  //   };

  //   try {
  //     const result = await this.sqs.sendMessage(params).promise();
  //     return result;

  //   } catch (error) {
  //     console.error('Error sending message to SQS:', error);
  //     throw error;
  //   }
  // }
  async sendEmail(email: any, title: string, message: string, cc: any): Promise<any> {


    const messageBody = JSON.stringify({email: email, cc: cc ? cc : [], "subject": title, "content": message, "deviceARNs": [], "sender": '<EMAIL>'});
    const params = {
      MessageBody: messageBody,
      QueueUrl: `${process.env.AWS_SQS_URL_EISQR}`
    };

    try {
      const result = await this.sqs.sendMessage(params).promise();
      return result;

    } catch (error) {
      console.error('Error sending message to SQS:', error);
      throw error;
    }
  }

}
