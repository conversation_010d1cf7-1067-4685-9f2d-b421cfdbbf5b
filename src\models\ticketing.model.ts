import {Entity, model, property} from '@loopback/repository';

@model()
export class Ticketing extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'any',
  })
  message?: any;

  @property({
    type: 'number',
  })
  priority?: number;

  @property({
    type: 'number',
  })
  category?: number;

  @property({
    type: 'number',
  })
  userType?: number;

  @property({
    type: 'string',
  })
  subject?: string;

  @property({
    type: 'number',
  })
  raised_by?: number;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  status?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<Ticketing>) {
    super(data);
  }
}

export interface TicketingRelations {
  // describe navigational properties here
}

export type TicketingWithRelations = Ticketing & TicketingRelations;
