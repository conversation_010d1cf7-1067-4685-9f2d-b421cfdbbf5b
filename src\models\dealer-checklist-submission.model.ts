import {belongsTo, Entity, model, property} from '@loopback/repository';
import {UserProfile} from './user-profile.model';
import {VendorCode} from './vendor-code.model';

@model()
export class DealerChecklistSubmission extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  response?: string;
  @property({
    type: 'number',
  })
  formId?: number;
  @property({
    type: 'array',
    itemType: 'string',
  })
  reporting_period?: string[];
  @property({
    type: 'string',
  })
  created_on?: string;
  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  score?: string;

  @property({
    type: 'any',
  })
  status?: any;
  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  vendorCode?: string | null;


  @belongsTo(() => UserProfile)
  dealerId: number;

  @belongsTo(() => VendorCode)
  vendorId: number;

  constructor(data?: Partial<DealerChecklistSubmission>) {
    super(data);
  }
}

export interface DealerChecklistSubmissionRelations {
  // describe navigational properties here
}

export type DealerChecklistSubmissionWithRelations = DealerChecklistSubmission & DealerChecklistSubmissionRelations;
