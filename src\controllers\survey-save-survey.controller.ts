import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Survey,
  SaveSurvey,
} from '../models';
import {SurveyRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SurveySaveSurveyController {
  constructor(
    @repository(SurveyRepository) protected surveyRepository: SurveyRepository,
  ) { }

  @get('/surveys/{id}/save-survey', {
    responses: {
      '200': {
        description: 'Survey has one SaveSurvey',
        content: {
          'application/json': {
            schema: getModelSchemaRef(SaveSurvey),
          },
        },
      },
    },
  })
  async get(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SaveSurvey>,
  ): Promise<SaveSurvey> {
    return this.surveyRepository.saveSurvey(id).get(filter);
  }

  @post('/surveys/{id}/save-survey', {
    responses: {
      '200': {
        description: 'Survey model instance',
        content: {'application/json': {schema: getModelSchemaRef(SaveSurvey)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Survey.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SaveSurvey, {
            title: 'NewSaveSurveyInSurvey',
            exclude: ['id'],
            optional: ['surveyId']
          }),
        },
      },
    }) saveSurvey: Omit<SaveSurvey, 'id'>,
  ): Promise<any> {
    try {
      const survey = await this.surveyRepository.saveSurvey(id).get();
      if(survey)
        return this.surveyRepository.saveSurvey(id).patch(saveSurvey);
    } catch(e) {
        return this.surveyRepository.saveSurvey(id).create(saveSurvey);
    }

  }

  @patch('/surveys/{id}/save-survey', {
    responses: {
      '200': {
        description: 'Survey.SaveSurvey PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SaveSurvey, {partial: true}),
        },
      },
    })
    saveSurvey: Partial<SaveSurvey>,
    @param.query.object('where', getWhereSchemaFor(SaveSurvey)) where?: Where<SaveSurvey>,
  ): Promise<Count> {
    return this.surveyRepository.saveSurvey(id).patch(saveSurvey, where);
  }

  @del('/surveys/{id}/save-survey', {
    responses: {
      '200': {
        description: 'Survey.SaveSurvey DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SaveSurvey)) where?: Where<SaveSurvey>,
  ): Promise<Count> {
    return this.surveyRepository.saveSurvey(id).delete(where);
  }
}
