import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  SubmitRf,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class UserProfileSubmitRfController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/submit-rfs', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SubmitRf',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SubmitRf)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SubmitRf>,
  ): Promise<SubmitRf[]> {
    return this.userProfileRepository.submitRfs(id).find(filter);
  }

  @post('/user-profiles/{id}/submit-rfs', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SubmitRf)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitRf, {
            title: 'NewSubmitRfInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) submitRf: Omit<SubmitRf, 'id'>,
  ): Promise<SubmitRf> {
    return this.userProfileRepository.submitRfs(id).create(submitRf);
  }

  // @patch('/user-profiles/{id}/submit-rfs', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitRf PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitRf, {partial: true}),
  //       },
  //     },
  //   })
  //   submitRf: Partial<SubmitRf>,
  //   @param.query.object('where', getWhereSchemaFor(SubmitRf)) where?: Where<SubmitRf>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitRfs(id).patch(submitRf, where);
  // }

  // @del('/user-profiles/{id}/submit-rfs', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitRf DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(SubmitRf)) where?: Where<SubmitRf>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitRfs(id).delete(where);
  // }
}
