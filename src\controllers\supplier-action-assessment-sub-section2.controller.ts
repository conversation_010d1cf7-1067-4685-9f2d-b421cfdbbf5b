import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierAction,
  AssessmentSubSection2,
} from '../models';
import {SupplierActionRepository} from '../repositories';

export class SupplierActionAssessmentSubSection2Controller {
  constructor(
    @repository(SupplierActionRepository)
    public supplierActionRepository: SupplierActionRepository,
  ) { }

  @get('/supplier-actions/{id}/assessment-sub-section2', {
    responses: {
      '200': {
        description: 'AssessmentSubSection2 belonging to SupplierAction',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssessmentSubSection2)},
          },
        },
      },
    },
  })
  async getAssessmentSubSection2(
    @param.path.number('id') id: typeof SupplierAction.prototype.id,
  ): Promise<AssessmentSubSection2> {
    return this.supplierActionRepository.assessmentSubSection2(id);
  }
}
