import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  AssignDcfUser,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';





export class UserProfileAssignDcfUserController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-dcf-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDcfUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDcfUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDcfUser>,
  ): Promise<AssignDcfUser[]> {
    return this.userProfileRepository.assignDcfUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-dcf-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDcfUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfUser, {
            title: 'NewAssignDcfUserInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDcfUser: Omit<AssignDcfUser, 'id'>,
  ): Promise<AssignDcfUser> {
    return this.userProfileRepository.assignDcfUsers(id).create(assignDcfUser);
  }

  // @patch('/user-profiles/{id}/assign-dcf-users', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDcfUser PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfUser, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfUser: Partial<AssignDcfUser>,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfUser)) where?: Where<AssignDcfUser>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDcfUsers(id).patch(assignDcfUser, where);
  // }

  // @del('/user-profiles/{id}/assign-dcf-users', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDcfUser DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfUser)) where?: Where<AssignDcfUser>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDcfUsers(id).delete(where);
  // }
  
}
