import {belongsTo, Entity, model, property} from '@loopback/repository';
import {VendorCode} from './vendor-code.model';

@model()
export class DealerAuditorChecklistSubmission extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  formId?: number;

  @property({
    type: 'any',

    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      type: 'array',
      items: {
        type: 'object',
      },
      nullable: true,
    }

  })
  reportMailStatus?: any[] | null;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  response?: string;



  @property({
    type: 'string',
  })
  score?: string;



  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'number',
  })
  modified_by?: number;
  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  approved_on?: string;

  @property({
    type: 'number',
  })
  approved_by?: number;

  @property({
    type: 'any',
  })
  status?: any;
  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'number',
  })
  dealerAssessmentAssignmentId?: number;

  @property({
    type: 'number',
  })
  dealerId?: number;

  @property({
    type: 'string',
  })
  updatedScore?: string;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  updatedResponse?: string;
  @property({
    type: 'any',

    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      type: 'array',
      items: {
        type: 'string',
      },
      nullable: true,
    }

  })
  improvements?: any[] | null;
  @property({
    type: 'any',

    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      type: 'array',
      items: {
        type: 'string',
      },
      nullable: true,
    }

  })
  goodPractices?: any[] | null;
  @property({
    type: 'any',

    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      type: 'array',
      items: {
        type: 'string',
      },
      nullable: true,
    }

  })
  nonCompliances?: any[] | null;

  @belongsTo(() => VendorCode)
  vendorId: number;

  vendor: VendorCode;

  constructor(data?: Partial<DealerAuditorChecklistSubmission>) {
    super(data);
  }
}

export interface DealerAuditorChecklistSubmissionRelations {
  // describe navigational properties here
}

export type DealerAuditorChecklistSubmissionWithRelations = DealerAuditorChecklistSubmission & DealerAuditorChecklistSubmissionRelations;
