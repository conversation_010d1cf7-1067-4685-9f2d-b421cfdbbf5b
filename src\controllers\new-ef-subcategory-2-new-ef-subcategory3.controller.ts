import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewEfSubcategory2,
  NewEfSubcategory3,
} from '../models';
import {NewEfSubcategory2Repository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfSubcategory2NewEfSubcategory3Controller {
  constructor(
    @repository(NewEfSubcategory2Repository) protected newEfSubcategory2Repository: NewEfSubcategory2Repository,
  ) { }

  @get('/new-ef-subcategory2s/{id}/new-ef-subcategory3s', {
    responses: {
      '200': {
        description: 'Array of NewEfSubcategory2 has many NewEfSubcategory3',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfSubcategory3)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEfSubcategory3>,
  ): Promise<NewEfSubcategory3[]> {
    return this.newEfSubcategory2Repository.newEfSubcategory3s(id).find(filter);
  }

  @post('/new-ef-subcategory2s/{id}/new-ef-subcategory3s', {
    responses: {
      '200': {
        description: 'NewEfSubcategory2 model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEfSubcategory3)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEfSubcategory2.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory3, {
            title: 'NewNewEfSubcategory3InNewEfSubcategory2',
            exclude: ['id'],
            optional: ['newEfSubcategory2Id']
          }),
        },
      },
    }) newEfSubcategory3: Omit<NewEfSubcategory3, 'id'>,
  ): Promise<NewEfSubcategory3> {
    return this.newEfSubcategory2Repository.newEfSubcategory3s(id).create(newEfSubcategory3);
  }

  // @patch('/new-ef-subcategory2s/{id}/new-ef-subcategory3s', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfSubcategory2.NewEfSubcategory3 PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfSubcategory3, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfSubcategory3: Partial<NewEfSubcategory3>,
  //   @param.query.object('where', getWhereSchemaFor(NewEfSubcategory3)) where?: Where<NewEfSubcategory3>,
  // ): Promise<Count> {
  //   return this.newEfSubcategory2Repository.newEfSubcategory3s(id).patch(newEfSubcategory3, where);
  // }

  // @del('/new-ef-subcategory2s/{id}/new-ef-subcategory3s', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfSubcategory2.NewEfSubcategory3 DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewEfSubcategory3)) where?: Where<NewEfSubcategory3>,
  // ): Promise<Count> {
  //   return this.newEfSubcategory2Repository.newEfSubcategory3s(id).delete(where);
  // }
}
