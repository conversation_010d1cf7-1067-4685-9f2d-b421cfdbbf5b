import {belongsTo, Entity, model, property} from '@loopback/repository';
import {VendorCode} from './vendor-code.model';

@model()
export class SupplierActionHistory extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  correctiveAction?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  rootCause?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  actionTaken?: string | null;



  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: {
      nullable: true,
    }
  })
  auditorAttachments?: object[] | null;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierAttachments?: object[] | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  approverComments?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  type?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  reject?: number | null;


  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  supplier_submitted_by?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  supplier_submitted_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  returned_on?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  returned_by?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  approved_on?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  approved_by?: number | null;

  @belongsTo(() => VendorCode)
  vendorId: number;

  @property({
    type: 'number',
  })
  supplierActionId?: number;

  constructor(data?: Partial<SupplierActionHistory>) {
    super(data);
  }
}

export interface SupplierActionHistoryRelations {
  // describe navigational properties here
}

export type SupplierActionHistoryWithRelations = SupplierActionHistory & SupplierActionHistoryRelations;
