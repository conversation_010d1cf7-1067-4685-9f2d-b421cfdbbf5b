import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignQlEntity,
  QCategory,
} from '../models';
import {AssignQlEntityRepository} from '../repositories';

export class AssignQlEntityQCategoryController {
  constructor(
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
  ) { }

  @get('/assign-ql-entities/{id}/q-category', {
    responses: {
      '200': {
        description: 'QCategory belonging to AssignQlEntity',
        content: {
          'application/json': {
            schema: getModelSchemaRef(QCategory),
          },
        },
      },
    },
  })
  async getQCategory(
    @param.path.number('id') id: typeof AssignQlEntity.prototype.id,
  ): Promise<QCategory> {
    return this.assignQlEntityRepository.qCategory(id);
  }
}
