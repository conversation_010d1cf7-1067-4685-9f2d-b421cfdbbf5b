import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from "luxon";
import {FormCollection} from '../models';
import {FormCollectionRepository, NewDataPointRepository} from '../repositories';




export class FormCollectionController {
  constructor(
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(NewDataPointRepository)
    public newDataPointRepository: NewDataPointRepository

  ) { }

  @post('/form-collections')
  @response(200, {
    description: 'FormCollection model instance',
    content: {'application/json': {schema: getModelSchemaRef(FormCollection)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(FormCollection, {
            title: 'NewFormCollection',
            exclude: ['id'],
          }),
        },
      },
    })
    formCollection: Omit<FormCollection, 'id'>,
  ): Promise<FormCollection> {
    const Form = await this.formCollectionRepository.create(formCollection);
    const id = Form.id
    const suffix = formCollection.suffix + id;
    Form.suffix = suffix;
    Form.created = DateTime.utc().toString()
    await this.formCollectionRepository.updateById(id, {suffix: suffix, created: DateTime.utc().toString(), updated: DateTime.utc().toString()});
    return Form;
  }

  @get('/form-collections/count')
  @response(200, {
    description: 'FormCollection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(FormCollection) where?: Where<FormCollection>,
  ): Promise<Count> {
    return this.formCollectionRepository.count(where);
  }

  @get('/form-collections')
  @response(200, {
    description: 'Array of FormCollection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(FormCollection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(FormCollection) filter?: Filter<FormCollection>,
  ): Promise<FormCollection[]> {
    return this.formCollectionRepository.find(filter);
  }

  @patch('/form-collections')
  @response(200, {
    description: 'FormCollection PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(FormCollection, {partial: true}),
        },
      },
    })
    formCollection: FormCollection,
    @param.where(FormCollection) where?: Where<FormCollection>,
  ): Promise<Count> {

    return this.formCollectionRepository.updateAll(formCollection, where);
  }

  @get('/form-collections/{id}')
  @response(200, {
    description: 'FormCollection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(FormCollection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(FormCollection, {exclude: 'where'}) filter?: FilterExcludingWhere<FormCollection>
  ): Promise<FormCollection> {
    const dcf = await this.formCollectionRepository.findById(id, filter);
    let requireDps = dcf?.data1?.filter((i: any) => i).map((i: any) => i.name) || []
    const datapoint = await this.newDataPointRepository.find({where: {suffix: {inq: requireDps}}})
    let unit = datapoint.filter(x => Array.isArray(x.data1) && x.data1.length).map((x: any) => ({dp: x.suffix, unit: x?.data1[0]?.unit}))
    for (const item of dcf?.data1 || []) {
      item.dpunit = unit.find(x => x.dp === item.name)?.unit || ''
    }
    return dcf
  }

  @patch('/form-collections/{id}')
  @response(204, {
    description: 'FormCollection PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(FormCollection, {partial: true}),
        },
      },
    })
    formCollection: FormCollection,
  ): Promise<void> {
    formCollection.updated = DateTime.utc().toString()
    await this.formCollectionRepository.updateById(id, formCollection);
  }

  @put('/form-collections/{id}')
  @response(204, {
    description: 'FormCollection PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() formCollection: FormCollection,
  ): Promise<void> {
    await this.formCollectionRepository.replaceById(id, formCollection);
  }

  @del('/form-collections/{id}')
  @response(204, {
    description: 'FormCollection DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.formCollectionRepository.deleteById(id);
  }

  @post('/delete-form-collections-by-ids')
  @response(204, {
    description: 'FormCollection DELETE success',
  })
  async deleteByMulipleIds(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            ids: {
              type: 'array',
              items: {
                type: 'number',
              },
            },
            date: {
              type: 'string'
            },
          },
          required: ['ids', 'date'],
        },
      },
    },
  })
  requestBody: {ids: number[], date: string},): Promise<any> {
    const {ids, date} = requestBody;
    const dt = DateTime.utc().toFormat('yyyyLLLdd')

    try {
      if (date === dt) {

        const deleteResult = await this.formCollectionRepository.deleteAll({
          id: {inq: ids},
        });

        return {result: (deleteResult.count === ids.length), user: deleteResult};
      } else {
        throw new HttpErrors.BadRequest('Invalidation');
      }


    } catch (e) {
      throw new HttpErrors.BadRequest(e);
    }

  }

}
