import {Entity, model, property, hasMany} from '@loopback/repository';
import {Survey} from './survey.model';

@model({settings: {strict: false}})
export class SurveyTitle extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'boolean',
  })
  doubleMateriality?: boolean;

  @property({
    type: 'string',
  })
  created?: string;

  @hasMany(() => Survey)
  surveys: Survey[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<SurveyTitle>) {
    super(data);
  }
}

export interface SurveyTitleRelations {
  // describe navigational properties here
}

export type SurveyTitleWithRelations = SurveyTitle & SurveyTitleRelations;
