import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {VendorCode} from '../models';
import {VendorCodeRepository} from '../repositories';

export class VendorCodeController {
  constructor(
    @repository(VendorCodeRepository)
    public vendorCodeRepository: VendorCodeRepository,
  ) { }

  @post('/vendor-codes')
  @response(200, {
    description: 'VendorCode model instance',
    content: {'application/json': {schema: getModelSchemaRef(VendorCode)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(VendorCode, {
            title: 'NewVendorCode',
            exclude: ['id'],
          }),
        },
      },
    })
    vendorCode: Omit<VendorCode, 'id'>,
  ): Promise<VendorCode> {
    return this.vendorCodeRepository.create(vendorCode);
  }

  @get('/vendor-codes/count')
  @response(200, {
    description: 'VendorCode model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(VendorCode) where?: Where<VendorCode>,
  ): Promise<Count> {
    return this.vendorCodeRepository.count(where);
  }

  @get('/vendor-codes')
  @response(200, {
    description: 'Array of VendorCode model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(VendorCode, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(VendorCode) filter?: Filter<VendorCode>,
  ): Promise<VendorCode[]> {
    return this.vendorCodeRepository.find(filter);
  }

  @patch('/vendor-codes')
  @response(200, {
    description: 'VendorCode PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(VendorCode, {partial: true}),
        },
      },
    })
    vendorCode: VendorCode,
    @param.where(VendorCode) where?: Where<VendorCode>,
  ): Promise<Count> {
    return this.vendorCodeRepository.updateAll(vendorCode, where);
  }

  @get('/vendor-codes/{id}')
  @response(200, {
    description: 'VendorCode model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(VendorCode, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(VendorCode, {exclude: 'where'}) filter?: FilterExcludingWhere<VendorCode>
  ): Promise<VendorCode> {
    return this.vendorCodeRepository.findById(id, filter);
  }

  @patch('/vendor-codes/{id}')
  @response(204, {
    description: 'VendorCode PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(VendorCode, {partial: true}),
        },
      },
    })
    vendorCode: VendorCode,
  ): Promise<void> {
    // Check if vendorCode has a code and validate uniqueness
    if (vendorCode.code) {
      const existingVendorWithCode = await this.vendorCodeRepository.findOne({
        where: {
          and: [
            {code: vendorCode.code},
            {id: {neq: id}} // Exclude the current record being updated
          ]
        }
      });

      if (existingVendorWithCode) {
        throw new HttpErrors.Conflict(
          `Vendor code '${vendorCode.code}' already exists. Please use a different code.`
        );
      }
    }

    await this.vendorCodeRepository.updateById(id, vendorCode);
  }

  @put('/vendor-codes/{id}')
  @response(204, {
    description: 'VendorCode PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() vendorCode: VendorCode,
  ): Promise<void> {
    await this.vendorCodeRepository.replaceById(id, vendorCode);
  }

  @del('/vendor-codes/{id}')
  @response(204, {
    description: 'VendorCode DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.vendorCodeRepository.deleteById(id);
  }
}
