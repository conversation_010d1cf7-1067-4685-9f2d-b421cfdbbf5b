import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewClientCertification, NewClientCertificationRelations} from '../models';

export class NewClientCertificationRepository extends DefaultCrudRepository<
  NewClientCertification,
  typeof NewClientCertification.prototype.id,
  NewClientCertificationRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(NewClientCertification, dataSource);
  }
}
