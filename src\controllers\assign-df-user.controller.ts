import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignDfUser} from '../models';
import {AssignDfUserRepository} from '../repositories';

export class AssignDfUserController {
  constructor(
    @repository(AssignDfUserRepository)
    public assignDfUserRepository: AssignDfUserRepository,
  ) { }

  @post('/assign-df-users')
  @response(200, {
    description: 'AssignDfUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDfUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfUser, {
            title: 'NewAssignDfUser',
            exclude: ['id'],
          }),
        },
      },
    })
    assignDfUser: Omit<AssignDfUser, 'id'>,
  ): Promise<AssignDfUser> {
    return this.assignDfUserRepository.create(assignDfUser);
  }

  @get('/assign-df-users/count')
  @response(200, {
    description: 'AssignDfUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDfUser) where?: Where<AssignDfUser>,
  ): Promise<Count> {
    return this.assignDfUserRepository.count(where);
  }

  @get('/assign-df-users')
  @response(200, {
    description: 'Array of AssignDfUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDfUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDfUser) filter?: Filter<AssignDfUser>,
  ): Promise<AssignDfUser[]> {
    return this.assignDfUserRepository.find(filter);
  }

  // @patch('/assign-df-users')
  // @response(200, {
  //   description: 'AssignDfUser PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDfUser, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDfUser: AssignDfUser,
  //   @param.where(AssignDfUser) where?: Where<AssignDfUser>,
  // ): Promise<Count> {
  //   return this.assignDfUserRepository.updateAll(assignDfUser, where);
  // }

  @get('/assign-df-users/{id}')
  @response(200, {
    description: 'AssignDfUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDfUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDfUser, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDfUser>
  ): Promise<AssignDfUser> {
    return this.assignDfUserRepository.findById(id, filter);
  }

  @patch('/assign-df-users/{id}')
  @response(204, {
    description: 'AssignDfUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfUser, {partial: true}),
        },
      },
    })
    assignDfUser: AssignDfUser,
  ): Promise<void> {
    await this.assignDfUserRepository.updateById(id, assignDfUser);
  }

  @put('/assign-df-users/{id}')
  @response(204, {
    description: 'AssignDfUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignDfUser: AssignDfUser,
  ): Promise<void> {
    await this.assignDfUserRepository.replaceById(id, assignDfUser);
  }

  @del('/assign-df-users/{id}')
  @response(204, {
    description: 'AssignDfUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDfUserRepository.deleteById(id);
  }
}
