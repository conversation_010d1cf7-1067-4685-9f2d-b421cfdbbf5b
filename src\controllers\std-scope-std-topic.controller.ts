import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  StdScope,
  StdTopic,
} from '../models';
import {StdScopeRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdScopeStdTopicController {
  constructor(
    @repository(StdScopeRepository) protected stdScopeRepository: StdScopeRepository,
  ) { }

  @get('/std-scopes/{id}/std-topics', {
    responses: {
      '200': {
        description: 'Array of StdScope has many StdTopic',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(StdTopic)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<StdTopic>,
  ): Promise<StdTopic[]> {
    return this.stdScopeRepository.stdTopics(id).find(filter);
  }

  @post('/std-scopes/{id}/std-topics', {
    responses: {
      '200': {
        description: 'StdScope model instance',
        content: {'application/json': {schema: getModelSchemaRef(StdTopic)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof StdScope.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdTopic, {
            title: 'NewStdTopicInStdScope',
            exclude: ['id'],
            optional: ['stdScopeId']
          }),
        },
      },
    }) stdTopic: Omit<StdTopic, 'id'>,
  ): Promise<StdTopic> {
    return this.stdScopeRepository.stdTopics(id).create(stdTopic);
  }

  // @patch('/std-scopes/{id}/std-topics', {
  //   responses: {
  //     '200': {
  //       description: 'StdScope.StdTopic PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StdTopic, {partial: true}),
  //       },
  //     },
  //   })
  //   stdTopic: Partial<StdTopic>,
  //   @param.query.object('where', getWhereSchemaFor(StdTopic)) where?: Where<StdTopic>,
  // ): Promise<Count> {
  //   return this.stdScopeRepository.stdTopics(id).patch(stdTopic, where);
  // }

  // @del('/std-scopes/{id}/std-topics', {
  //   responses: {
  //     '200': {
  //       description: 'StdScope.StdTopic DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(StdTopic)) where?: Where<StdTopic>,
  // ): Promise<Count> {
  //   return this.stdScopeRepository.stdTopics(id).delete(where);
  // }
}
