import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {CertificationLevel} from '../models';
import {CertificationLevelRepository} from '../repositories';

export class CertificationLevelController {
  constructor(
    @repository(CertificationLevelRepository)
    public certificationLevelRepository : CertificationLevelRepository,
  ) {}

  @post('/certification-levels')
  @response(200, {
    description: 'CertificationLevel model instance',
    content: {'application/json': {schema: getModelSchemaRef(CertificationLevel)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertificationLevel, {
            title: 'NewCertificationLevel',
            exclude: ['id'],
          }),
        },
      },
    })
    certificationLevel: Omit<CertificationLevel, 'id'>,
  ): Promise<CertificationLevel> {
    return this.certificationLevelRepository.create(certificationLevel);
  }

  @get('/certification-levels/count')
  @response(200, {
    description: 'CertificationLevel model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(CertificationLevel) where?: Where<CertificationLevel>,
  ): Promise<Count> {
    return this.certificationLevelRepository.count(where);
  }

  @get('/certification-levels')
  @response(200, {
    description: 'Array of CertificationLevel model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(CertificationLevel, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(CertificationLevel) filter?: Filter<CertificationLevel>,
  ): Promise<CertificationLevel[]> {
    return this.certificationLevelRepository.find(filter);
  }

  @patch('/certification-levels')
  @response(200, {
    description: 'CertificationLevel PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertificationLevel, {partial: true}),
        },
      },
    })
    certificationLevel: CertificationLevel,
    @param.where(CertificationLevel) where?: Where<CertificationLevel>,
  ): Promise<Count> {
    return this.certificationLevelRepository.updateAll(certificationLevel, where);
  }

  @get('/certification-levels/{id}')
  @response(200, {
    description: 'CertificationLevel model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(CertificationLevel, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(CertificationLevel, {exclude: 'where'}) filter?: FilterExcludingWhere<CertificationLevel>
  ): Promise<CertificationLevel> {
    return this.certificationLevelRepository.findById(id, filter);
  }

  @patch('/certification-levels/{id}')
  @response(204, {
    description: 'CertificationLevel PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertificationLevel, {partial: true}),
        },
      },
    })
    certificationLevel: CertificationLevel,
  ): Promise<void> {
    await this.certificationLevelRepository.updateById(id, certificationLevel);
  }

  @put('/certification-levels/{id}')
  @response(204, {
    description: 'CertificationLevel PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() certificationLevel: CertificationLevel,
  ): Promise<void> {
    await this.certificationLevelRepository.replaceById(id, certificationLevel);
  }

  @del('/certification-levels/{id}')
  @response(204, {
    description: 'CertificationLevel DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.certificationLevelRepository.deleteById(id);
  }
}
