import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignDcfEntity,
  FormCollection,
} from '../models';
import {AssignDcfEntityRepository} from '../repositories';

export class AssignDcfEntityFormCollectionController {
  constructor(
    @repository(AssignDcfEntityRepository)
    public assignDcfEntityRepository: AssignDcfEntityRepository,
  ) { }

  @get('/assign-dcf-entities/{id}/form-collection', {
    responses: {
      '200': {
        description: 'FormCollection belonging to AssignDcfEntity',
        content: {
          'application/json': {
            schema: getModelSchemaRef(FormCollection),
          },
        },
      },
    },
  })
  async getFormCollection(
    @param.path.number('id') id: typeof AssignDcfEntity.prototype.id,
  ): Promise<FormCollection> {
    return this.assignDcfEntityRepository.dcf(id);
  }
}
