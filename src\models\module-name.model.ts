import {Entity, model, property, hasMany} from '@loopback/repository';
import {ScopeName} from './scope-name.model';

@model({settings: {strict: false}})
export class ModuleName extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'string',
  })
  created?: string;

  @hasMany(() => ScopeName)
  scopeNames: ScopeName[];

  @property({
    type: 'number',
  })
  topLevelComponentId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<ModuleName>) {
    super(data);
  }
}

export interface ModuleNameRelations {
  // describe navigational properties here
}

export type ModuleNameWithRelations = ModuleName & ModuleNameRelations;
