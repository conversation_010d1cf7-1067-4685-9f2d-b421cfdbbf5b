import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ComputedIndicator} from '../models';
import {ComputedIndicatorRepository} from '../repositories';

export class ComputedIndicatorController {
  constructor(
    @repository(ComputedIndicatorRepository)
    public computedIndicatorRepository : ComputedIndicatorRepository,
  ) {}

  @post('/computed-indicators')
  @response(200, {
    description: 'ComputedIndicator model instance',
    content: {'application/json': {schema: getModelSchemaRef(ComputedIndicator)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ComputedIndicator, {
            title: 'NewComputedIndicator',
            exclude: ['id'],
          }),
        },
      },
    })
    computedIndicator: Omit<ComputedIndicator, 'id'>,
  ): Promise<ComputedIndicator> {
    return this.computedIndicatorRepository.create(computedIndicator);
  }

  @get('/computed-indicators/count')
  @response(200, {
    description: 'ComputedIndicator model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ComputedIndicator) where?: Where<ComputedIndicator>,
  ): Promise<Count> {
    return this.computedIndicatorRepository.count(where);
  }

  @get('/computed-indicators')
  @response(200, {
    description: 'Array of ComputedIndicator model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ComputedIndicator, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ComputedIndicator) filter?: Filter<ComputedIndicator>,
  ): Promise<ComputedIndicator[]> {
    return this.computedIndicatorRepository.find(filter);
  }

  @patch('/computed-indicators')
  @response(200, {
    description: 'ComputedIndicator PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ComputedIndicator, {partial: true}),
        },
      },
    })
    computedIndicator: ComputedIndicator,
    @param.where(ComputedIndicator) where?: Where<ComputedIndicator>,
  ): Promise<Count> {
    return this.computedIndicatorRepository.updateAll(computedIndicator, where);
  }

  @get('/computed-indicators/{id}')
  @response(200, {
    description: 'ComputedIndicator model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ComputedIndicator, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ComputedIndicator, {exclude: 'where'}) filter?: FilterExcludingWhere<ComputedIndicator>
  ): Promise<ComputedIndicator> {
    return this.computedIndicatorRepository.findById(id, filter);
  }

  @patch('/computed-indicators/{id}')
  @response(204, {
    description: 'ComputedIndicator PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ComputedIndicator, {partial: true}),
        },
      },
    })
    computedIndicator: ComputedIndicator,
  ): Promise<void> {
    await this.computedIndicatorRepository.updateById(id, computedIndicator);
  }

  @put('/computed-indicators/{id}')
  @response(204, {
    description: 'ComputedIndicator PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() computedIndicator: ComputedIndicator,
  ): Promise<void> {
    await this.computedIndicatorRepository.replaceById(id, computedIndicator);
  }

  @del('/computed-indicators/{id}')
  @response(204, {
    description: 'ComputedIndicator DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.computedIndicatorRepository.deleteById(id);
  }
}
