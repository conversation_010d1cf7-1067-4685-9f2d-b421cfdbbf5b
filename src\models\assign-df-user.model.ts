import {Entity, model, property} from '@loopback/repository';

@model()
export class AssignDfUser extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  site?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  comments?: any[];

  @property({
    type: 'number',
  })
  creator_id?: number;

  @property({
    type: 'number',
  })
  modifier_id?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  approver_id?: any[];

  @property({
    type: 'number',
  })
  user_id?: number;

  @property({
    type: 'number',
  })
  reviewer_id?: number;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  dfId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<AssignDfUser>) {
    super(data);
  }
}

export interface AssignDfUserRelations {
  // describe navigational properties here
}

export type AssignDfUserWithRelations = AssignDfUser & AssignDfUserRelations;
