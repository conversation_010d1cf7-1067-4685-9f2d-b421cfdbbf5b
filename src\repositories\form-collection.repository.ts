import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {FormCollection, FormCollectionRelations, AssignDcfUser} from '../models';
import {AssignDcfUserRepository} from './assign-dcf-user.repository';

export class FormCollectionRepository extends DefaultCrudRepository<
  FormCollection,
  typeof FormCollection.prototype.id,
  FormCollectionRelations
> {

  public readonly assignDcfUsers: HasManyRepositoryFactory<AssignDcfUser, typeof FormCollection.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('AssignDcfUserRepository') protected assignDcfUserRepositoryGetter: Getter<AssignDcfUserRepository>,
  ) {
    super(FormCollection, dataSource);
    this.assignDcfUsers = this.createHasManyRepositoryFactoryFor('assignDcfUsers', assignDcfUserRepositoryGetter,);
    this.registerInclusionResolver('assignDcfUsers', this.assignDcfUsers.inclusionResolver);
  }
}
