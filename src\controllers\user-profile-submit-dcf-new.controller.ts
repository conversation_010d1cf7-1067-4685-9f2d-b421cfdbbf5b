import {
  Filter,
  repository
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody
} from '@loopback/rest';
import {
  SubmitDcfNew,
  UserProfile,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileSubmitDcfNewController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/submit-dcf-news', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SubmitDcfNew',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SubmitDcfNew)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SubmitDcfNew>,
  ): Promise<SubmitDcfNew[]> {
    return this.userProfileRepository.submitDcfNews(id).find(filter);
  }

  @post('/user-profiles/{id}/submit-dcf-news', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SubmitDcfNew)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitDcfNew, {
            title: 'NewSubmitDcfNewInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) submitDcfNew: Omit<SubmitDcfNew, 'id'>,
  ): Promise<SubmitDcfNew> {
    return this.userProfileRepository.submitDcfNews(id).create(submitDcfNew);
  }

  // @patch('/user-profiles/{id}/submit-dcf-news', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitDcfNew PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitDcfNew, {partial: true}),
  //       },
  //     },
  //   })
  //   submitDcfNew: Partial<SubmitDcfNew>,
  //   @param.query.object('where', getWhereSchemaFor(SubmitDcfNew)) where?: Where<SubmitDcfNew>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitDcfNews(id).patch(submitDcfNew, where);
  // }

  // @del('/user-profiles/{id}/submit-dcf-news', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitDcfNew DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(SubmitDcfNew)) where?: Where<SubmitDcfNew>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitDcfNews(id).delete(where);
  // }
}
