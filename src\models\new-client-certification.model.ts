import {Entity, model, property} from '@loopback/repository';

@model()
export class NewClientCertification extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  certificateId?: number;

  @property({
    type: 'number',
  })
  scopeType?: number;

  @property({
    type: 'number',
  })
  status?: number;

  @property({
    type: 'any',
  })
  certLevelId?: any;


  @property({
    type: 'any',
  })
  issuedDate?: any;


  @property({
    type: 'any',
  })
  expectedDate?: any;
  @property({
    type: 'any',
  })
  validity?: any;
  @property({
    type: 'string',
  })
  remark?: string;

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'array',
    itemType: 'any',
  })
  attachment?: any[];


  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'object',
  })
  applicability?: object;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<NewClientCertification>) {
    super(data);
  }
}

export interface NewClientCertificationRelations {
  // describe navigational properties here
}

export type NewClientCertificationWithRelations = NewClientCertification & NewClientCertificationRelations;
