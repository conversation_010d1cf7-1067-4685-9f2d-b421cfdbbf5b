import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, Options, repository} from '@loopback/repository';
import {v4 as uuidv4} from 'uuid';
import {MysqlDataSource} from '../datasources';
import {AssessmentSubSection2, AssessmentSubSection2Relations, AssessmentSubSection3, ConsolidateFormCollection} from '../models';
import {AssessmentSubSection3Repository} from './assessment-sub-section3.repository';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';

export class AssessmentSubSection2Repository extends DefaultCrudRepository<
  AssessmentSubSection2,
  typeof AssessmentSubSection2.prototype.id,
  AssessmentSubSection2Relations
> {

  public readonly assessmentSubSection3s: HasManyRepositoryFactory<AssessmentSubSection3, typeof AssessmentSubSection2.prototype.id>;

  public readonly form: BelongsToAccessor<ConsolidateFormCollection, typeof AssessmentSubSection2.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('AssessmentSubSection3Repository') protected assessmentSubSection3RepositoryGetter: Getter<AssessmentSubSection3Repository>, @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>,
  ) {
    super(AssessmentSubSection2, dataSource);
    this.form = this.createBelongsToAccessorFor('form', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('form', this.form.inclusionResolver);

    this.assessmentSubSection3s = this.createHasManyRepositoryFactoryFor('assessmentSubSection3s', assessmentSubSection3RepositoryGetter,);
    this.registerInclusionResolver('assessmentSubSection3s', this.assessmentSubSection3s.inclusionResolver);
  }
  async create(entity: Partial<AssessmentSubSection2>, options?: Options): Promise<AssessmentSubSection2> {

    entity.id = uuidv4(); // Generate UUID before saving if `id` is missing

    return super.create(entity, options);
  }
}
