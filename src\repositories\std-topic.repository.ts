import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {StdTopic, StdTopicRelations} from '../models';

export class StdTopicRepository extends DefaultCrudRepository<
  StdTopic,
  typeof StdTopic.prototype.id,
  StdTopicRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(StdTopic, dataSource);
  }
}
