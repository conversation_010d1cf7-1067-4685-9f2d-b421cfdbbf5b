import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  ClientEfCategoryAssignment,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileClientEfCategoryAssignmentController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/client-ef-category-assignments', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many ClientEfCategoryAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ClientEfCategoryAssignment)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ClientEfCategoryAssignment>,
  ): Promise<ClientEfCategoryAssignment[]> {
    return this.userProfileRepository.clientEfCategoryAssignments(id).find(filter);
  }

  @post('/user-profiles/{id}/client-ef-category-assignments', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(ClientEfCategoryAssignment)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryAssignment, {
            title: 'NewClientEfCategoryAssignmentInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) clientEfCategoryAssignment: Omit<ClientEfCategoryAssignment, 'id'>,
  ): Promise<ClientEfCategoryAssignment> {
    return this.userProfileRepository.clientEfCategoryAssignments(id).create(clientEfCategoryAssignment);
  }

  @patch('/user-profiles/{id}/client-ef-category-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.ClientEfCategoryAssignment PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryAssignment, {partial: true}),
        },
      },
    })
    clientEfCategoryAssignment: Partial<ClientEfCategoryAssignment>,
    @param.query.object('where', getWhereSchemaFor(ClientEfCategoryAssignment)) where?: Where<ClientEfCategoryAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.clientEfCategoryAssignments(id).patch(clientEfCategoryAssignment, where);
  }

  @del('/user-profiles/{id}/client-ef-category-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.ClientEfCategoryAssignment DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(ClientEfCategoryAssignment)) where?: Where<ClientEfCategoryAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.clientEfCategoryAssignments(id).delete(where);
  }
}
