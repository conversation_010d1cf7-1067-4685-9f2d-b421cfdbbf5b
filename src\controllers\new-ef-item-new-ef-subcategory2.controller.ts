import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  NewEfItem,
  NewEfSubcategory2,
} from '../models';
import {NewEfItemRepository} from '../repositories';

export class NewEfItemNewEfSubcategory2Controller {
  constructor(
    @repository(NewEfItemRepository)
    public newEfItemRepository: NewEfItemRepository,
  ) { }

  @get('/new-ef-items/{id}/new-ef-subcategory2', {
    responses: {
      '200': {
        description: 'NewEfSubcategory2 belonging to NewEfItem',
        content: {
          'application/json': {
            schema: getModelSchemaRef(NewEfSubcategory2),
          },
        },
      },
    },
  })
  async getNewEfSubcategory2(
    @param.path.number('id') id: typeof NewEfItem.prototype.id,
  ): Promise<NewEfSubcategory2> {
    return this.newEfItemRepository.subcat2(id);
  }
}
