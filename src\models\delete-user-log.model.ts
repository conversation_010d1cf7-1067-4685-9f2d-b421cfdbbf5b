import {Entity, model, property} from '@loopback/repository';

@model()
export class DeleteUserLog extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  deletedUserId?: number;

  @property({
    type: 'number',
  })
  requestUserId?: number;



  @property({
    type: 'string',
  })
  deletedOn?: string;

  @property({
    type: 'string',
  })
  deletedMailId?: string;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<DeleteUserLog>) {
    super(data);
  }
}

export interface DeleteUserLogRelations {
  // describe navigational properties here
}

export type DeleteUserLogWithRelations = DeleteUserLog & DeleteUserLogRelations;
