import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {HttpErrors} from '@loopback/rest';
import {MysqlDataSource} from '../datasources';
import {QuantitativeDpReport, QuantitativeSubmission} from '../models';
import {QuantitativeDpReportRepository, QuantitativeSubmissionRepository} from '../repositories'; // Replace with your actual repositories

export class QuantitativeApproval {
  constructor(
    @repository(QuantitativeDpReportRepository)
    private quantitativeDpReportRepository: QuantitativeDpReportRepository,
    @repository(QuantitativeSubmissionRepository)
    private quantitativeSubmissionRepository: QuantitativeSubmissionRepository,
    @inject('datasources.mysql') private dataSource: MysqlDataSource,
  ) { }

  async updateQuantitativeSubmissionAndDp(
    submissionId: number,
    submissionObject: Partial<QuantitativeSubmission>,
    approvalDpArray: QuantitativeDpReport[]
  ): Promise<void> {
    const tx = await this.dataSource.beginTransaction();

    try {
      await this.quantitativeSubmissionRepository.updateById(submissionId, submissionObject, {transaction: tx});
      await this.quantitativeDpReportRepository.createAll(approvalDpArray, {transaction: tx});
      await tx.commit();
    } catch (err) {
      await tx.rollback();
      throw new HttpErrors.BadRequest('Failed to update Submission and create dp report: ' + err.message);
    }
  }
}
