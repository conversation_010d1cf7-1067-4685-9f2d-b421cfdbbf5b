import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {TopicName} from '../models';
import {TopicNameRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class TopicNameController {
  constructor(
    @repository(TopicNameRepository)
    public topicNameRepository : TopicNameRepository,
  ) {}

  @post('/topic-names')
  @response(200, {
    description: 'TopicName model instance',
    content: {'application/json': {schema: getModelSchemaRef(TopicName)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TopicName, {
            title: 'NewTopicName',
            exclude: ['id'],
          }),
        },
      },
    })
    topicName: Omit<TopicName, 'id'>,
  ): Promise<TopicName> {
    return this.topicNameRepository.create(topicName);
  }

  @get('/topic-names/count')
  @response(200, {
    description: 'TopicName model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(TopicName) where?: Where<TopicName>,
  ): Promise<Count> {
    return this.topicNameRepository.count(where);
  }

  @get('/topic-names')
  @response(200, {
    description: 'Array of TopicName model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(TopicName, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(TopicName) filter?: Filter<TopicName>,
  ): Promise<TopicName[]> {
    return this.topicNameRepository.find(filter);
  }

  // @patch('/topic-names')
  // @response(200, {
  //   description: 'TopicName PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(TopicName, {partial: true}),
  //       },
  //     },
  //   })
  //   topicName: TopicName,
  //   @param.where(TopicName) where?: Where<TopicName>,
  // ): Promise<Count> {
  //   return this.topicNameRepository.updateAll(topicName, where);
  // }

  @get('/topic-names/{id}')
  @response(200, {
    description: 'TopicName model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(TopicName, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(TopicName, {exclude: 'where'}) filter?: FilterExcludingWhere<TopicName>
  ): Promise<TopicName> {
    return this.topicNameRepository.findById(id, filter);
  }

  @patch('/topic-names/{id}')
  @response(204, {
    description: 'TopicName PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TopicName, {partial: true}),
        },
      },
    })
    topicName: TopicName,
  ): Promise<void> {
    await this.topicNameRepository.updateById(id, topicName);
  }

  @put('/topic-names/{id}')
  @response(204, {
    description: 'TopicName PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() topicName: TopicName,
  ): Promise<void> {
    await this.topicNameRepository.replaceById(id, topicName);
  }

  @del('/topic-names/{id}')
  @response(204, {
    description: 'TopicName DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.topicNameRepository.deleteById(id);
  }
}
