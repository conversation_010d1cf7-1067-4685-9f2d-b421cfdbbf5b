import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  PolicyProcedure,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfilePolicyProcedureController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/policy-procedures', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many PolicyProcedure',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(PolicyProcedure)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<PolicyProcedure>,
  ): Promise<PolicyProcedure[]> {
    return this.userProfileRepository.policyProcedures(id).find(filter);
  }

  @post('/user-profiles/{id}/policy-procedures', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(PolicyProcedure)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PolicyProcedure, {
            title: 'NewPolicyProcedureInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) policyProcedure: Omit<PolicyProcedure, 'id'>,
  ): Promise<PolicyProcedure> {
    return this.userProfileRepository.policyProcedures(id).create(policyProcedure);
  }

  @patch('/user-profiles/{id}/policy-procedures', {
    responses: {
      '200': {
        description: 'UserProfile.PolicyProcedure PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(PolicyProcedure, {partial: true}),
        },
      },
    })
    policyProcedure: Partial<PolicyProcedure>,
    @param.query.object('where', getWhereSchemaFor(PolicyProcedure)) where?: Where<PolicyProcedure>,
  ): Promise<Count> {
    return this.userProfileRepository.policyProcedures(id).patch(policyProcedure, where);
  }

  @del('/user-profiles/{id}/policy-procedures', {
    responses: {
      '200': {
        description: 'UserProfile.PolicyProcedure DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(PolicyProcedure)) where?: Where<PolicyProcedure>,
  ): Promise<Count> {
    return this.userProfileRepository.policyProcedures(id).delete(where);
  }
}
