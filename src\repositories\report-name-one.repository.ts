import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ReportNameOne, ReportNameOneRelations, ReportNameTwo} from '../models';
import {ReportNameTwoRepository} from './report-name-two.repository';

export class ReportNameOneRepository extends DefaultCrudRepository<
  ReportNameOne,
  typeof ReportNameOne.prototype.id,
  ReportNameOneRelations
> {

  public readonly reportNameTwos: HasManyRepositoryFactory<ReportNameTwo, typeof ReportNameOne.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ReportNameTwoRepository') protected reportNameTwoRepositoryGetter: Getter<ReportNameTwoRepository>,
  ) {
    super(ReportNameOne, dataSource);
    this.reportNameTwos = this.createHasManyRepositoryFactoryFor('reportNameTwos', reportNameTwoRepositoryGetter,);
    this.registerInclusionResolver('reportNameTwos', this.reportNameTwos.inclusionResolver);
  }
}
