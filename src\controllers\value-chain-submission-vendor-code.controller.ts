import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ValueChainSubmission,
  VendorCode,
} from '../models';
import {ValueChainSubmissionRepository} from '../repositories';

export class ValueChainSubmissionVendorCodeController {
  constructor(
    @repository(ValueChainSubmissionRepository)
    public valueChainSubmissionRepository: ValueChainSubmissionRepository,
  ) { }

  @get('/value-chain-submissions/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to ValueChainSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof ValueChainSubmission.prototype.id,
  ): Promise<VendorCode> {
    return this.valueChainSubmissionRepository.vendor(id);
  }
}
