import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  NewClientCertification,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileNewClientCertificationController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/new-client-certifications', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many NewClientCertification',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewClientCertification)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewClientCertification>,
  ): Promise<NewClientCertification[]> {
    return this.userProfileRepository.newClientCertifications(id).find(filter);
  }

  @post('/user-profiles/{id}/new-client-certifications', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewClientCertification)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewClientCertification, {
            title: 'NewNewClientCertificationInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) newClientCertification: Omit<NewClientCertification, 'id'>,
  ): Promise<NewClientCertification> {
    return this.userProfileRepository.newClientCertifications(id).create(newClientCertification);
  }

  @patch('/user-profiles/{id}/new-client-certifications', {
    responses: {
      '200': {
        description: 'UserProfile.NewClientCertification PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewClientCertification, {partial: true}),
        },
      },
    })
    newClientCertification: Partial<NewClientCertification>,
    @param.query.object('where', getWhereSchemaFor(NewClientCertification)) where?: Where<NewClientCertification>,
  ): Promise<Count> {
    return this.userProfileRepository.newClientCertifications(id).patch(newClientCertification, where);
  }

  @del('/user-profiles/{id}/new-client-certifications', {
    responses: {
      '200': {
        description: 'UserProfile.NewClientCertification DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(NewClientCertification)) where?: Where<NewClientCertification>,
  ): Promise<Count> {
    return this.userProfileRepository.newClientCertifications(id).delete(where);
  }
}
