import {inject} from '@loopback/core';
import {
  Filter,
  repository
} from '@loopback/repository';
import {
  HttpErrors,
  Request,
  RestBindings,
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody,
  response
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {
  QuantitativeSubmission,
  UserProfile
} from '../models';
import {AssignDcfEntityUserRepository, FormCollectionRepository, NewDataPointRepository, QuantitativeSubmissionRepository, StructuredResponseRepository, UserProfileRepository} from '../repositories';
import {QuantitativeApproval} from '../services/QuantitativeApprove.service';
import {UserProfileController} from './user-profile.controller';

export class UserProfileQuantitativeSubmissionController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(StructuredResponseRepository) protected structuredResponseRepository: StructuredResponseRepository,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(AssignDcfEntityUserRepository) protected assignDcfEntityUserRepository: AssignDcfEntityUserRepository,
    @repository(NewDataPointRepository)
    public newDataPointRepository: NewDataPointRepository,
    @repository(QuantitativeSubmissionRepository) protected quantitativeSubmissionRepository: QuantitativeSubmissionRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @inject('services.QuantitativeApproval')
    private quantitativeApprovalService: QuantitativeApproval
  ) { }

  @get('/user-profiles/{id}/quantitative-submissions', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many QuantitativeSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QuantitativeSubmission)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QuantitativeSubmission>,
  ): Promise<QuantitativeSubmission[]> {
    return this.userProfileRepository.quantitativeSubmissions(id).find(filter);
  }
  @get('/user-profiles/{id}/quantitative-submissions-custom', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many QuantitativeSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QuantitativeSubmission)},
          },
        },
      },
    },
  })
  async findCustom(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QuantitativeSubmission>,
  ): Promise<QuantitativeSubmission[]> {
    const totalData = await this.userProfileRepository.quantitativeSubmissions(id).find(filter);
    const responseArray: any = [];
    let reqDcfs = await this.formCollectionRepository.find({where: {id: {inq: Array.from(new Set(totalData.map(i => i.dcfId)))}}})
    for (const result of totalData) {
      const dcf = reqDcfs.find(x => x.id === result.dcfId)
      if (result.response && result.response.length && dcf) {
        if (result.response.some(x => x.type === 'number')) {
          let updatedForm: any[] = this.updateFormFields(result.response, dcf?.data1 || [])
          let requireDps = updatedForm.filter((i: any) => i.name !== undefined && i.name !== null).map((i: any) => i.name)
          const datapoint = await this.newDataPointRepository.find({where: {suffix: {inq: requireDps}}})
          let unit = datapoint.filter(x => Array.isArray(x.data1) && x.data1.length).map((x: any) => ({dp: x.suffix, unit: x?.data1[0]?.unit}))

          for (const item of updatedForm) {
            item.dpunit = unit.find(x => x.dp === item.name)?.unit || ''
          }

          responseArray.push({...result, response: updatedForm})

        } else {
          responseArray.push({...result})
        }



      } else {
        responseArray.push({...result})
      }
    }
    return responseArray
  }
  @post('/user-profiles/{id}/quantitative-submissions', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(QuantitativeSubmission)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeSubmission, {
            title: 'NewQuantitativeSubmissionInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) quantitativeSubmission: Omit<QuantitativeSubmission, 'id'>,
  ): Promise<QuantitativeSubmission> {
    const {dcfId, tier0_id, tier1_id, tier2_id, tier3_id, reporting_period} = quantitativeSubmission

    let data = await this.userProfileRepository.quantitativeSubmissions(id).find({where: {dcfId, tier0_id, tier1_id, tier2_id, tier3_id}})
    if (data.length) {
      let isMatch = data.filter(item => item.reporting_period &&
        item.reporting_period.some(period => reporting_period && reporting_period.includes(period))
      );
      if (isMatch.length === 0) {

        return this.userProfileRepository.quantitativeSubmissions(id).create(quantitativeSubmission);
      } else {
        throw new HttpErrors.BadRequest('Data Already Exist');
      }
    } else {
      return this.userProfileRepository.quantitativeSubmissions(id).create(quantitativeSubmission);
    }

  }
  @post('/user-profiles/{id}/quantitative-submissions-custom', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(QuantitativeSubmission)}},
      },
    },
  })
  async createCustom(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeSubmission, {
            title: 'NewQuantitativeSubmissionInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) quantitativeSubmission: Omit<QuantitativeSubmission, 'id'>,
  ): Promise<QuantitativeSubmission> {
    const {dcfId, tier0_id, tier1_id, tier2_id, tier3_id, reporting_period} = quantitativeSubmission

    let data = await this.userProfileRepository.quantitativeSubmissions(id).find({where: {dcfId, tier0_id, tier1_id, tier2_id, tier3_id}})
    if (data.length) {
      let isMatch = data.filter(item => item.reporting_period &&
        item.reporting_period.some(period => reporting_period && reporting_period.includes(period))
      );
      if (isMatch.length === 0) {
        const submissionToCreate = {...quantitativeSubmission};
        const structuredResponse = submissionToCreate.response2;
        delete quantitativeSubmission.response2
        const submitted = await this.userProfileRepository.quantitativeSubmissions(id).create({...quantitativeSubmission, response2: []});

        if (structuredResponse && Array.isArray(structuredResponse)) {
          for (const xyz of structuredResponse) {
            // Assuming 'xyz' is an object, you can directly modify its properties
            if (typeof xyz === 'object' && xyz !== null) {
              (xyz as any)['submitDcfId'] = submitted.id;
              (xyz as any)['dcfId'] = submitted.dcfId;
              (xyz as any)['reporting_period'] = submitted.reporting_period;
              (xyz as any)['created_on'] = DateTime.utc().toString()
              if ('userId' in xyz) {
                (xyz as any)['created_by'] = xyz['userId'];
                delete xyz['userId'];
              }
              let result = await this.userProfileRepository.structuredResponses(id).create(xyz);
            }
          }
        } else {
          console.error('structuredResponse is not an array or is undefined');
        }

        return submitted
      } else {
        throw new HttpErrors.BadRequest('Data Already Exist');
      }
    } else {
      const submissionToCreate = {...quantitativeSubmission};
      const structuredResponse = submissionToCreate.response2;
      delete quantitativeSubmission.response2
      const submitted = await this.userProfileRepository.quantitativeSubmissions(id).create({...quantitativeSubmission, response2: []});

      if (structuredResponse && Array.isArray(structuredResponse)) {
        for (const xyz of structuredResponse) {
          // Assuming 'xyz' is an object, you can directly modify its properties
          if (typeof xyz === 'object' && xyz !== null) {
            (xyz as any)['submitDcfId'] = submitted.id;
            (xyz as any)['dcfId'] = submitted.dcfId;
            (xyz as any)['reporting_period'] = submitted.reporting_period;
            (xyz as any)['created_on'] = DateTime.utc().toString()
            if ('userId' in xyz) {
              (xyz as any)['created_by'] = xyz['userId'];
              delete xyz['userId'];
            }
            // Use 'xyz' to create a new structured response in the repository
            let result = await this.userProfileRepository.structuredResponses(id).create(xyz);
          }
        }
      } else {
        console.error('structuredResponse is not an array or is undefined');
      }

      return submitted
    }



  }
  @post('/quantitative-submissions-initiative-past')
  @response(200, {
    description: 'Assignment along with reporting period, also submission if any',
  })
  async initiativePastSubmission(
    @inject(RestBindings.Http.REQUEST) req: Request,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {type: 'number'},
              dcfId: {type: 'number'},
              level: {type: 'number'},
              locationId: {type: 'number'},
              month: {type: 'string'},
            },
          },
        },
      },
    })
    data: {
      month: string;
      dcfId: number;
      level: number;
      locationId: number;
      userProfileId: number;
    },
  ): Promise<any> {
    const {month, dcfId, level, locationId, userProfileId} = data;
    const timeZone = 'Asia/Singapore';
    let assignment = await this.userProfileRepository.assignDcfEntityUsers(userProfileId).find({where: {dcfId, level, locationId}})

    if (assignment.length > 0) {
      const submissionFound = await this.userProfileRepository.quantitativeSubmissions(userProfileId).find({where: {level, locationId, dcfId, type: {neq: 0}}})
      const assignmentFound = this.getFilteredAssignmentByDateString(month, assignment, timeZone.toString());
      let result = assignmentFound.map((i: any) => ({...i, data: submissionFound.find(x => x.reporting_period && (this.getRPTextFormat(x.reporting_period) === i.reporting_period))}))

      return {
        assignment, data: result.map((i: any) => i.data).filter((i: any) => i).map((i: any) => i.response).slice(-1).flatMap((x: any) => x), status: assignmentFound.length === result.map((i: any) => i.data).filter((x: any) => x).length,
      }
    } else {

      return {
        status: false,
        result: [],
        data: [],
        assignment: []
      }
    }

  }
  @post('/user-profiles/{id}/multiple-import-qn-submissions', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
      },
    },
  })
  async multipleimport(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @inject(RestBindings.Http.REQUEST) req: Request,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              data: {
                type: 'array',
                items: {
                  type: 'object',
                },
              },
              user_id: {
                type: 'number',
              },
            },
            required: ['data', 'user_id'],
          },
        },
      },
    })
    requestObject: {data: any[], user_id: number},
  ): Promise<any> {
    if (!requestObject || !requestObject.data || !requestObject.user_id) {
      throw new Error('Invalid request: data and user_id are required');
    }
    const timeZone = 'Asia/Singapore';

    const {data, user_id} = requestObject;
    let acceptedData: any[] = [], rejectedData: any[] = [], uploadData: any[] = [], existingData: any[] = [], attachmentRequired = [304, 285, 287, 310, 268], importedDcfIds = [264, 304, 305, 287, 285]

    if (data.length) {
      for (const submission of data) {
        const assignment = await this.assignDcfEntityUserRepository.find({
          where: {entityAssId: submission.entityAssId, id: submission.entityUserAssId, dcfId: submission.dcfId, userProfileId: id}
        });

        if (assignment.length === 1) {
          const assignmentObject = assignment[0];

          if (assignmentObject.reporter_ids?.includes(user_id) && assignmentObject.frequency && assignmentObject.start_date) {
            const possiblerps = this.getMonthsBetween(
              assignmentObject.start_date,
              assignmentObject.end_date,
              assignmentObject.frequency === 4 ? 12 : assignmentObject.frequency === 5 ? 6 : assignmentObject.frequency, timeZone
            );
            const requestedrp = this.getRPTextFormat(submission.reporting_period);

            if (requestedrp && possiblerps.length) {

              if (possiblerps.includes(requestedrp)) {
                if (assignmentObject.reviewer_ids?.length === 0) {
                  // submission['reviewed_by'] = user_id;
                  // submission['reviewed_on'] = submission['submitted_on'];
                  // submission['reviewer_modified_on'] = submission['submitted_on'];
                  // submission['reviewer_modified_by'] = user_id;
                  submission['self'] = true;
                  submission['type'] = this.getDisplayStatus(requestedrp) ? (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 2 : 0
                } else {
                  submission['type'] = this.getDisplayStatus(requestedrp) ? (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 1 : 0
                  submission['self'] = false;
                }
                submission['imported'] = 1;
                delete submission['rp'];

                submission['frequency'] = assignmentObject.frequency;
                submission['logs'] = [];

                if (submission.response.length === 0 && submission.edit === 1 && this.getDisplayStatus(requestedrp)) {
                  const excelData = JSON.parse(JSON.stringify(submission['excel_data']));

                  const {dcfId, tier0_id, tier1_id, tier2_id, tier3_id, reporting_period, response2} = submission;

                  const data = await this.userProfileRepository.quantitativeSubmissions(id).find({
                    where: {dcfId, tier0_id, tier1_id, tier2_id, tier3_id}
                  });

                  if (data.length) {
                    const isMatch = data.filter(item =>
                      item.reporting_period && item.reporting_period.some(period =>
                        reporting_period && reporting_period.includes(period)
                      )
                    );

                    if (isMatch.length === 0) {
                      delete submission['excel_data'];
                      delete submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`]
                      try {
                        let result = await this.userProfileRepository.quantitativeSubmissions(id).create({...submission, response2: [], reject: 0});
                        this.updateStructuredResponse(result, null, response2, user_id, id)
                        uploadData.push(result)

                        acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                      } catch {
                        rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Err 1: Problem with uploading'})));


                      }
                    } else {

                      let findData = data.filter(item => (item.reporting_period ? (this.getRPTextFormat(item.reporting_period) === requestedrp) : false) && item.id === submission.id)

                      if (findData.length === 1) {
                        let oldData = {...findData[0]}
                        if (oldData.type === 3) {
                          rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Data Already Approved & Locked'})));

                        } if (oldData.type === 2 && (assignmentObject.reviewer_ids && assignmentObject?.reviewer_ids?.length)) {
                          rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Data Reviewed & Under Approval'})));

                        } else if (oldData.type === 2 && (!assignmentObject.reviewer_ids || assignmentObject?.reviewer_ids?.length === 0)) {
                          const oldObj: {[key: string]: any} = {};


                          oldObj['self'] = true;
                          oldObj['type'] = (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 2


                          oldObj['last_modified_on'] = submission['submitted_on'];
                          oldObj['last_modified_by'] = user_id
                          oldObj['edit'] = 1
                          oldObj['reject'] = 0
                          oldObj['response'] = []
                          oldObj['response2'] = []
                          try {
                            let result = await this.quantitativeSubmissionRepository.updateById(oldData.id, oldObj);
                            this.updateStructuredResponse(oldObj, oldData, response2, user_id, id)
                            existingData.push(await this.quantitativeSubmissionRepository.findOne({where: {id: oldData.id}}))
                            acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                          } catch {
                            rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Problem with updating exisitng data'})));
                          }

                        } else {
                          const oldObj: {[key: string]: any} = {};
                          if (oldData.type === 0) {
                            if (assignmentObject.reviewer_ids?.length === 0) {
                              // if (submission['reviewer_modified_on'] === null) {
                              //   oldObj['reviewed_by'] = user_id;
                              //   oldObj['reviewed_on'] = submission['submitted_on']
                              // }

                              // oldObj['reviewer_modified_on'] = user_id
                              // oldObj['reviewer_modified_by'] = user_id;
                              oldObj['self'] = true;
                              oldObj['type'] = this.getDisplayStatus(requestedrp) ? (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 2 : 0

                            } else {
                              if ((submission['reject'] !== null && submission['reject'] !== 1)) {
                                oldObj['submitted_by'] = user_id
                                oldObj['submitted_on'] = submission['submitted_on'];
                              }

                              oldObj['reporter_modified_on'] = submission['submitted_on'];
                              oldObj['reporter_modified_by'] = user_id

                              oldObj['type'] = this.getDisplayStatus(requestedrp) ? (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 1 : 0
                              oldObj['self'] = false;
                            }
                            oldObj['last_modified_on'] = submission['submitted_on'];
                            oldObj['last_modified_by'] = user_id
                            oldObj['edit'] = 1
                            oldObj['reject'] = 0
                            oldObj['response'] = []
                            oldObj['response2'] = []
                            try {
                              let result = await this.quantitativeSubmissionRepository.updateById(oldData.id, oldObj);
                              existingData.push(await this.quantitativeSubmissionRepository.findOne({where: {id: oldData.id}}))
                              acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                              this.updateStructuredResponse(oldObj, oldData, response2, user_id, id)

                            } catch {
                              rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Problem with updating exisitng data'})));


                            }



                          } else if (oldData.type === 1) {
                            if (assignmentObject.reviewer_ids?.length === 0) {
                              // if (submission['reviewer_modified_on'] === null) {
                              //   oldObj['reviewed_by'] = user_id;
                              //   oldObj['reviewed_on'] = submission['submitted_on']
                              // }
                              // oldObj['reviewer_modified_on'] = user_id
                              // oldObj['reviewer_modified_by'] = submission['submitted_on']
                              oldObj['self'] = true;
                              oldObj['type'] = (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 2

                            } else {
                              oldObj['reporter_modified_on'] = submission['submitted_on'];
                              oldObj['reporter_modified_by'] = user_id
                              oldObj['type'] = (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 1
                              oldObj['self'] = false;
                            }
                            oldObj['last_modified_on'] = submission['submitted_on'];
                            oldObj['last_modified_by'] = user_id
                            oldObj['edit'] = 1
                            oldObj['response'] = []
                            oldObj['response2'] = []
                            oldObj['reject'] = 0
                            try {
                              let result = await this.quantitativeSubmissionRepository.updateById(oldData.id, oldObj);
                              this.updateStructuredResponse(oldObj, oldData, response2, user_id, id)
                              existingData.push(await this.quantitativeSubmissionRepository.findOne({where: {id: oldData.id}}))
                              acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                            } catch {
                              rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Problem with updating exisitng data'})));
                            }
                          } else {
                            rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Unexpected Data Status Found'})));
                          }

                        }

                      } else {
                        rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Multiple matches found for this Entity & DCF'})));
                      }

                    }
                  } else {
                    delete submission['excel_data'];
                    delete submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`]
                    try {
                      let result = await this.userProfileRepository.quantitativeSubmissions(id).create({...submission, response2: [], reject: 0});
                      uploadData.push(result)
                      this.updateStructuredResponse(result, null, response2, user_id, id)
                      acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                    } catch {
                      rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Err 2: Problem with uploading'})));


                    }
                  }
                } else if (submission.response.length !== 0 && !submission.edit) {
                  const excelData = submission['excel_data'];

                  const {dcfId, tier0_id, tier1_id, tier2_id, tier3_id, reporting_period, response2} = submission;

                  const data = await this.userProfileRepository.quantitativeSubmissions(id).find({
                    where: {dcfId, tier0_id, tier1_id, tier2_id, tier3_id}
                  });

                  if (data.length) {
                    const isMatch = data.filter(item =>
                      item.reporting_period && item.reporting_period.some(period =>
                        reporting_period && reporting_period.includes(period)
                      )
                    );

                    if (isMatch.length === 0) {
                      delete submission['excel_data'];
                      delete submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`]
                      try {
                        let result = await this.userProfileRepository.quantitativeSubmissions(id).create({...submission, response2: [], reject: 0});
                        uploadData.push(result)
                        this.updateStructuredResponse(result, null, response2, user_id, id)
                        acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                      } catch {
                        rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Err 3: Problem with uploading'})));
                      }
                    } else {
                      let findData = data.filter(item => (item.reporting_period ? (this.getRPTextFormat(item.reporting_period) === requestedrp) : false) && item.id === submission.id)

                      if (findData.length === 1) {
                        let oldData = {...findData[0]}
                        if (oldData.type === 3) {
                          rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Data Already Approved & Locked'})));

                        } else if (oldData.type === 2 && (assignmentObject.reviewer_ids && assignmentObject?.reviewer_ids?.length)) {
                          rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Data Reviewed & Under Approval'})));

                        } else if (oldData.type === 2 && (!assignmentObject.reviewer_ids || assignmentObject?.reviewer_ids?.length === 0)) {
                          const oldObj: {[key: string]: any} = {};


                          oldObj['self'] = true;
                          oldObj['type'] = this.getDisplayStatus(requestedrp) ? (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 2 : 0


                          oldObj['last_modified_on'] = submission['submitted_on'];
                          oldObj['last_modified_by'] = user_id
                          oldObj['edit'] = 0
                          oldObj['reject'] = 0
                          oldObj['response'] = submission['response']
                          oldObj['response2'] = []
                          try {
                            let result = await this.quantitativeSubmissionRepository.updateById(oldData.id, oldObj);
                            existingData.push(await this.quantitativeSubmissionRepository.findOne({where: {id: oldData.id}}))
                            acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                            this.updateStructuredResponse(oldObj, oldData, response2, user_id, id)

                          } catch {
                            rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Problem with updating exisitng data'})));


                          }

                        } else {
                          const oldObj: {[key: string]: any} = {};
                          if (oldData.type === 0) {
                            if (assignmentObject.reviewer_ids?.length === 0) {
                              // if (submission['reviewer_modified_on'] === null) {
                              //   oldObj['reviewed_by'] = user_id;
                              //   oldObj['reviewed_on'] = submission['submitted_on']
                              // }

                              // oldObj['reviewer_modified_on'] = user_id
                              // oldObj['reviewer_modified_by'] = user_id;
                              oldObj['self'] = true;
                              oldObj['type'] = this.getDisplayStatus(requestedrp) ? (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 2 : 0

                            } else {
                              if ((submission['reject'] !== null && submission['reject'] !== 1)) {
                                oldObj['submitted_by'] = user_id
                                oldObj['submitted_on'] = submission['submitted_on'];
                              }

                              oldObj['reporter_modified_on'] = submission['submitted_on'];
                              oldObj['reporter_modified_by'] = user_id

                              oldObj['type'] = this.getDisplayStatus(requestedrp) ? (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 1 : 0
                              oldObj['self'] = false;
                            }
                            oldObj['last_modified_on'] = submission['submitted_on'];
                            oldObj['last_modified_by'] = user_id
                            oldObj['edit'] = 0
                            oldObj['reject'] = 0
                            oldObj['response'] = submission['response']
                            oldObj['response2'] = []
                            try {
                              let result = await this.quantitativeSubmissionRepository.updateById(oldData.id, oldObj);
                              existingData.push(await this.quantitativeSubmissionRepository.findOne({where: {id: oldData.id}}))
                              acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                              this.updateStructuredResponse(oldObj, oldData, response2, user_id, id)

                            } catch {
                              rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Problem with updating exisitng data'})));


                            }



                          } else if (oldData.type === 1) {
                            if (assignmentObject.reviewer_ids?.length === 0) {
                              // if (submission['reviewer_modified_on'] === null) {
                              //   oldObj['reviewed_by'] = user_id;
                              //   oldObj['reviewed_on'] = submission['submitted_on']
                              // }
                              // oldObj['reviewer_modified_on'] = user_id
                              // oldObj['reviewer_modified_by'] = submission['submitted_on']
                              oldObj['self'] = true;
                              oldObj['type'] = (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 2

                            } else {
                              oldObj['reporter_modified_on'] = submission['submitted_on'];
                              oldObj['reporter_modified_by'] = user_id
                              oldObj['type'] = (submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`] === true || attachmentRequired.includes(submission.dcfId)) ? 0 : 1
                              oldObj['self'] = false;
                            }
                            oldObj['last_modified_on'] = submission['submitted_on'];
                            oldObj['last_modified_by'] = user_id
                            oldObj['edit'] = 0
                            oldObj['reject'] = 0
                            oldObj['response'] = submission['response']
                            oldObj['response2'] = []
                            try {
                              let result = await this.quantitativeSubmissionRepository.updateById(oldData.id, oldObj);
                              existingData.push(await this.quantitativeSubmissionRepository.findOne({where: {id: oldData.id}}))
                              acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                              this.updateStructuredResponse(oldObj, oldData, response2, user_id, id)

                            } catch {
                              rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Problem with updating exisitng data'})));


                            }
                          } else {
                            rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Unexpected Data Status Found'})));
                          }

                        }

                      } else {
                        rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Multiple matches found for this Entity & DCF'})));
                      }


                    }
                  } else {
                    delete submission['excel_data'];
                    delete submission[`Attachment available? - Attachment is not mandatory for this form and the default is 'No'`]
                    try {
                      let result = await this.userProfileRepository.quantitativeSubmissions(id).create({...submission, response2: [], reject: 0});
                      uploadData.push(result)
                      acceptedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Uploaded Success'})));
                      this.updateStructuredResponse(result, null, response2, user_id, id)
                    } catch {
                      rejectedData.push(...excelData.map((x: any) => ({...x, "Rejection Remarks": 'Err 4:Problem with uploading'})));

                    }
                  }
                } else {
                  rejectedData.push(...submission['excel_data'].map((x: any) => ({...x, "Rejection Remarks": 'Mismatch in Null Report/Response. Note: Cant submit Null Report before due month'})));


                }
              } else {
                rejectedData.push(...submission['excel_data'].map((x: any) => ({...x, "Rejection Remarks": 'Reporting Period Mismatch' + requestedrp})));

              }
            } else {
              rejectedData.push(...submission['excel_data'].map((x: any) => ({...x, "Rejection Remarks": possiblerps?.length !== 0 ? 'Reporting Period Mismatch' : 'Reporting Period Not Found in Assignemnt'})));


            }
          } else {
            rejectedData.push(...submission['excel_data'].map((x: any) => ({...x, "Rejection Remarks": 'Role Not Assigned'})));

          }
        } else {
          rejectedData.push(...submission['excel_data'].map((x: any) => ({...x, "Rejection Remarks": 'Assignment Not Found, Kindly Reload page For Update'})));
        }
      }
    }

    return {acceptedData, rejectedData, uploadData, existingData};
  }

  updateStructuredResponse = async (newResponse: any, oldResponse: any, structuredResponse: any, userId: number, uid: any) => {
    try {
      if (uid) {
        if (oldResponse && newResponse) {
          if (newResponse.type != null && [0, 1, 2].includes(newResponse.type) && structuredResponse) {

            if (newResponse.edit === 1) {
              if (oldResponse.edit !== 1) {
                await this.structuredResponseRepository.deleteAll({submitDcfId: oldResponse.id})
                await this.structuredResponseRepository.createAll(structuredResponse.map((y: any) => {
                  const {userId: x, ...rest} = y; // Destructure to exclude userId
                  return {
                    ...rest, userProfileId: uid,
                    created_by: userId,
                    created_on: DateTime.utc().toString(),
                    reporting_period: oldResponse.reporting_period,
                  };
                }));
              }


            } else {
              if (oldResponse.edit === 1 && structuredResponse?.length) {


                await this.structuredResponseRepository.deleteAll({submitDcfId: oldResponse.id})
                await this.structuredResponseRepository.createAll(structuredResponse.map((y: any) => {
                  const {userId: x, ...rest} = y; // Destructure to exclude userId
                  return {
                    ...rest,
                    created_by: userId, userProfileId: uid,
                    created_on: DateTime.utc().toString(),
                    reporting_period: oldResponse.reporting_period,
                  };
                }));

              } else {
                const existingList = await this.structuredResponseRepository.find({where: {submitDcfId: oldResponse.id}})
                let existingIds: number[] = [];


                for (const item of structuredResponse) {
                  if ('maskId' in item && 'uniqueId' in item) {
                    (item as any)['reporting_period'] = oldResponse.reporting_period

                    let index = existingList.find((x: any) => x.maskId === item?.maskId && x.uniqueId === item?.uniqueId && x.dcfId === oldResponse.dcfId)
                    if (index) {
                      if ('userId' in item) {
                        (item as any)['modified_by'] = item['userId'];
                        delete item['userId'];
                      }
                      existingIds.push(index.id || 0)
                      try {
                        await this.structuredResponseRepository.updateById(index.id, {...item, userProfileId: uid, modified_on: DateTime.utc().toString()})

                      } catch (e) {
                        console.log(e)
                      }
                    } else {
                      if ('userId' in item) {
                        (item as any)['created_by'] = item['userId'];
                        delete item['userId'];
                      }
                      await this.structuredResponseRepository.create({...item, userProfileId: uid, created_on: DateTime.utc().toString()})
                    }
                  }

                }
                const deleteIds = existingList.map(i => i.id).filter(x => x).filter((j: any) => !existingIds.includes(j))
                if (deleteIds.length) {
                  await this.structuredResponseRepository.deleteAll({id: {inq: deleteIds}})
                }

              }

            }

          }
        } else if (newResponse && !oldResponse) {
          if (structuredResponse && Array.isArray(structuredResponse)) {
            for (const xyz of structuredResponse) {
              // Assuming 'xyz' is an object, you can directly modify its properties
              if (typeof xyz === 'object' && xyz !== null) {
                (xyz as any)['submitDcfId'] = newResponse.id;
                (xyz as any)['dcfId'] = newResponse.dcfId;
                (xyz as any)['reporting_period'] = newResponse.reporting_period;
                (xyz as any)['created_on'] = DateTime.utc().toString()
                if ('userId' in xyz) {
                  (xyz as any)['created_by'] = xyz['userId'];
                  delete xyz['userId'];
                }
                let result = await this.userProfileRepository.structuredResponses(uid).create(xyz);
              }
            }
          } else {
            console.error('structuredResponse is not an array or is undefined');
          }
        } else {
          console.log('wrong')
        }
      }
    } catch (e) {
      console.log(e)
    }


  }
  getFilteredAssignmentByDateString(dateString: any, assignments: any, timeZone: any) {
    const targetDate = DateTime.fromFormat(dateString, "MMM-yyyy", {zone: 'utc'}).setZone(timeZone);

    return assignments.map((assignment: any) => {
      const {start_date, end_date, frequency: freq} = assignment;
      let frequency = freq === 4 ? 12 : freq === 5 ? 6 : freq
      // Parse start and end dates for the assignment
      const startDate = DateTime.fromISO(start_date, {zone: 'utc'}).setZone(timeZone);
      const endDate = end_date ? DateTime.fromISO(end_date, {zone: 'utc'}) : DateTime.utc().setZone(timeZone);

      // Initialize reporting periods array
      const reportingPeriods = [];
      let currentStart = startDate;

      // Generate reporting periods until we reach the end date or the target date
      while (currentStart < endDate) {
        const currentEnd = currentStart.plus({months: frequency}).minus({days: 1});

        // Break if the period goes past the target date
        if (currentEnd >= targetDate) {
          break;
        }
        if (frequency === 1) {
          reportingPeriods.push(
            `${currentStart.toFormat("MMM-yyyy")}`
          );
        } else {
          reportingPeriods.push(
            `${currentStart.toFormat("MMM-yyyy")} to ${currentEnd.toFormat("MMM-yyyy")}`
          );
        }



        // Move to the next reporting period
        currentStart = currentStart.plus({months: frequency});
      }

      return reportingPeriods.map(i => ({...assignment, reporting_period: i}))

    }).flatMap((i: any) => i)
  }
  updateFormFields(oldForm: any = [], newForm: any = []) {
    // Helper to find a field by name and type
    const findField = (form: any, field: any) => form.find((f: any) => f.name === field.name && f.type === field.type);

    // Helper to merge fields while keeping old values (excluding paragraph type)
    function mergeFields(oldField: any, newField: any) {
      const mergedField = {...newField};

      // Retain value from old field if present
      if (oldField.value !== undefined) {
        mergedField.value = oldField.value;
      }

      // Handle `values` array for selections
      if (Array.isArray(newField.values)) {
        mergedField.values = newField.values.map((newValue: any) => {
          const oldValue = oldField.values?.find((v: any) => v.value === newValue.value) || {};
          return {
            ...newValue,
            selected: oldValue.selected !== undefined ? oldValue.selected : newValue.selected,
          };
        });
      }

      return mergedField;
    }

    const result: any = [];

    // Iterate over oldForm and compare fields with newForm
    oldForm.forEach((oldField: any, index: any) => {
      const newField = newForm[index];

      // Handle paragraph types with matching indices
      if (oldField.type === "paragraph") {
        if (newField && newField.type === "paragraph" && index === newForm.indexOf(newField)) {
          result.push(newField); // Replace paragraph field only if index matches
        } else {
          result.push(oldField); // Retain old paragraph if index mismatch
        }
      } else {
        const matchingNewField = findField(newForm, oldField);
        if (matchingNewField) {
          result.push(mergeFields(oldField, matchingNewField));
        } else {
          result.push(oldField); // Keep old field if no exact match in new form
        }
      }
    });

    // Add new fields not in oldForm, ignoring paragraph types
    newForm.forEach((newField: any, index: number) => {
      if (!findField(oldForm, newField) && newField.type !== "paragraph") {
        result.push(newField);
      }
    });

    return result;
  }
  getDisplayStatus = (rp: string): boolean => {

    const [startMonth, endMonth] = rp.split(' to ');

    const month = endMonth ? endMonth : startMonth;
    const [monthValue, year] = month.split('-');
    const endOfMonth = DateTime.fromObject({year: parseInt(year), month: DateTime.fromFormat(monthValue, 'LLL').month}).endOf('month');
    const currentDate = DateTime.local();

    return endOfMonth.diff(currentDate, 'days').days <= 0;

  }
  getRPTextFormat(item: string[]): string {
    if (item.length !== 0) {
      if (item.length >= 2) {
        const startDate = DateTime.fromFormat(item[0], "MM-yyyy").toFormat(
          "LLL-yyyy"
        );
        const endDate = DateTime.fromFormat(
          item[item.length - 1],
          "MM-yyyy"
        ).toFormat("LLL-yyyy");
        return `${startDate} to ${endDate}`;
      } else {
        return DateTime.fromFormat(item[0], "MM-yyyy").toFormat("LLL-yyyy");
      }
    } else {
      return ''
    }
  }
  getMonthsBetween(start_date: string, end_date: any, frequency: number, zone: any): string[] {
    const startDate = DateTime.fromISO(start_date, {zone: 'utc'}).setZone(zone);
    const endDate = end_date ? DateTime.fromISO(end_date, {zone: 'utc'}).setZone(zone) : DateTime.utc().setZone(zone)

    const months = [];
    let currentMonth = startDate;

    while (currentMonth <= endDate) {
      if (frequency === 1) {

        if (currentMonth.startOf('month').diff(DateTime.utc().setZone(zone), 'days').days <= 0) {
          months.push(currentMonth.toFormat("LLL-yyyy"));
        }
      } else {

        if (currentMonth.startOf('month').diff(DateTime.utc().setZone(zone), 'days').days <= 0) {

          months.push(
            currentMonth.toFormat("LLL-yyyy") +
            " to " +
            currentMonth.plus({months: frequency - 1}).toFormat("LLL-yyyy")
          );
        }


      }

      currentMonth = currentMonth.plus({months: frequency});
    }

    return months;
  }
}
