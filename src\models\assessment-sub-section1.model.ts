import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {AssessmentSubSection2} from './assessment-sub-section2.model';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';

@model()
export class AssessmentSubSection1 extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
  })
  title?: string;
  @property({
    type: 'any',
  })
  order?: any;
  @property({
    type: 'boolean',
    default: false,
  })
  isRoot?: boolean;

  @property({
    type: 'string',
  })
  assessmentSectionId?: string;

  @hasMany(() => AssessmentSubSection2)
  assessmentSubSection2s: AssessmentSubSection2[];

  @belongsTo(() => ConsolidateFormCollection, {name: 'srf'})
  formId: number;

  constructor(data?: Partial<AssessmentSubSection1>) {
    super(data);
  }
}

export interface AssessmentSubSection1Relations {
  // describe navigational properties here
}

export type AssessmentSubSection1WithRelations = AssessmentSubSection1 & AssessmentSubSection1Relations;
