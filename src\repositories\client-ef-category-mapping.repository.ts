import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ClientEfCategoryMapping, ClientEfCategoryMappingRelations, GhgCategory, GhgSubCategory, NewEfCategory, NewEfStd} from '../models';
import {GhgCategoryRepository} from './ghg-category.repository';
import {GhgSubCategoryRepository} from './ghg-sub-category.repository';
import {NewEfCategoryRepository} from './new-ef-category.repository';
import {NewEfStdRepository} from './new-ef-std.repository';

export class ClientEfCategoryMappingRepository extends DefaultCrudRepository<
  ClientEfCategoryMapping,
  typeof ClientEfCategoryMapping.prototype.id,
  ClientEfCategoryMappingRelations
> {

  public readonly efStandard: BelongsToAccessor<NewEfStd, typeof ClientEfCategoryMapping.prototype.id>;

  public readonly efCategory: BelongsToAccessor<NewEfCategory, typeof ClientEfCategoryMapping.prototype.id>;

  public readonly efGhgSubCat: BelongsToAccessor<GhgSubCategory, typeof ClientEfCategoryMapping.prototype.id>;

  public readonly efGhgCat: BelongsToAccessor<GhgCategory, typeof ClientEfCategoryMapping.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfStdRepository') protected newEfStdRepositoryGetter: Getter<NewEfStdRepository>, @repository.getter('NewEfCategoryRepository') protected newEfCategoryRepositoryGetter: Getter<NewEfCategoryRepository>, @repository.getter('GhgCategoryRepository') protected ghgCategoryRepositoryGetter: Getter<GhgCategoryRepository>, @repository.getter('GhgSubCategoryRepository') protected ghgSubCategoryRepositoryGetter: Getter<GhgSubCategoryRepository>,
  ) {
    super(ClientEfCategoryMapping, dataSource);
    this.efGhgCat = this.createBelongsToAccessorFor('efGhgCat', ghgCategoryRepositoryGetter,);
    this.registerInclusionResolver('efGhgCat', this.efGhgCat.inclusionResolver);
    this.efGhgSubCat = this.createBelongsToAccessorFor('efGhgSubCat', ghgSubCategoryRepositoryGetter,);
    this.registerInclusionResolver('efGhgSubCat', this.efGhgSubCat.inclusionResolver);
    this.efCategory = this.createBelongsToAccessorFor('efCategory', newEfCategoryRepositoryGetter,);
    this.registerInclusionResolver('efCategory', this.efCategory.inclusionResolver);
    this.efStandard = this.createBelongsToAccessorFor('efStandard', newEfStdRepositoryGetter,);
    this.registerInclusionResolver('efStandard', this.efStandard.inclusionResolver);
  }
}
