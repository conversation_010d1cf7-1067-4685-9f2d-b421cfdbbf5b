import {Entity, model, property} from '@loopback/repository';

@model()
export class CertificationLevel extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  modified_by?: number;
  @property({
    type: 'number',
  })
  certificationId?: number;

  constructor(data?: Partial<CertificationLevel>) {
    super(data);
  }
}

export interface CertificationLevelRelations {
  // describe navigational properties here
}

export type CertificationLevelWithRelations = CertificationLevel & CertificationLevelRelations;
