import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ManagementQuestion, ManagementQuestionRelations} from '../models';

export class ManagementQuestionRepository extends DefaultCrudRepository<
  ManagementQuestion,
  typeof ManagementQuestion.prototype.id,
  ManagementQuestionRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ManagementQuestion, dataSource);
  }
}
