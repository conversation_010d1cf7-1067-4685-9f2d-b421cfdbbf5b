import {
  Filter,
  repository
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
  post,
  requestBody
} from '@loopback/rest';
import {
  SubmitRfNew,
  UserProfile,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileSubmitRfNewController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/submit-rf-news', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SubmitRfNew',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SubmitRfNew)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SubmitRfNew>,
  ): Promise<SubmitRfNew[]> {
    return this.userProfileRepository.submitRfNews(id).find(filter);
  }

  @post('/user-profiles/{id}/submit-rf-news', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SubmitRfNew)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitRfNew, {
            title: 'NewSubmitRfNewInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) submitRfNew: Omit<SubmitRfNew, 'id'>,
  ): Promise<SubmitRfNew> {
    return this.userProfileRepository.submitRfNews(id).create(submitRfNew);
  }

  // @patch('/user-profiles/{id}/submit-rf-news', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitRfNew PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitRfNew, {partial: true}),
  //       },
  //     },
  //   })
  //   submitRfNew: Partial<SubmitRfNew>,
  //   @param.query.object('where', getWhereSchemaFor(SubmitRfNew)) where?: Where<SubmitRfNew>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitRfNews(id).patch(submitRfNew, where);
  // }

  // @del('/user-profiles/{id}/submit-rf-news', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitRfNew DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(SubmitRfNew)) where?: Where<SubmitRfNew>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitRfNews(id).delete(where);
  // }
}
