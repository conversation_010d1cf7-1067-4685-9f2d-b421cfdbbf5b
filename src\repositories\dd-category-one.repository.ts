import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DdCategoryOne, DdCategoryOneRelations} from '../models';

export class DdCategoryOneRepository extends DefaultCrudRepository<
  DdCategoryOne,
  typeof DdCategoryOne.prototype.id,
  DdCategoryOneRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(DdCategoryOne, dataSource);
  }
}
