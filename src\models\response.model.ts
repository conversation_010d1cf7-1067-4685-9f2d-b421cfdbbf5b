import {Entity, belongsTo, model, property} from '@loopback/repository';
import {StakeHolder} from './stake-holder.model';
import {UserProfile} from './user-profile.model';

@model({settings: {strict: false}})
export class Response extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  uniqueId?: string;

  @property({
    type: 'string',
  })
  feedback?: string;

  @property({
    type: 'array',
    itemType: 'object',
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  answers?: object[];

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  surveyId?: number;

  @property({
    type: 'string',
  })
  type?: string;

  @belongsTo(() => UserProfile)
  userProfileId: number;

  @belongsTo(() => StakeHolder)
  stakeHolderId: number;

  @property({
    type: 'number',
  })
  subSurveyId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Response>) {
    super(data);
  }
}

export interface ResponseRelations {
  // describe navigational properties here
}

export type ResponseWithRelations = Response & ResponseRelations;
