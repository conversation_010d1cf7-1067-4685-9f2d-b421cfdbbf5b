import {Entity, model, property} from '@loopback/repository';

@model()
export class ChangeManagement extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  clientId?: number;
  @property({
    type: 'number',
  })
  type?: number;


  @property({
    type: 'number',
  })
  implementationScreen?: number;
  @property({
    type: 'string',
  })
  communicationMsg?: string;

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'array',
    itemType: 'any',
  })
  attachment?: any[];

  @property({
    type: 'any'
  })
  version?: any;

  @property({
    type: 'string',
  })
  dateOfImplementation?: string;
  @property({
    type: 'string',
  })
  implementationTypeOthers?: string;
  @property({
    type: 'string',
  })
  description?: string;
  @property({
    type: 'string',
  })
  approvedBy?: string;
  @property({
    type: 'string',
  })
  modified_on?: string;
  @property({
    type: 'string',
  })
  created_on?: string;
  @property({
    type: 'number',
  })
  modified_by?: number;
  @property({
    type: 'number',
  })
  created_by?: number;


  constructor(data?: Partial<ChangeManagement>) {
    super(data);
  }
}

export interface ChangeManagementRelations {
  // describe navigational properties here
}

export type ChangeManagementWithRelations = ChangeManagement & ChangeManagementRelations;
