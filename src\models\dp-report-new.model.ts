import {Entity, model, property} from '@loopback/repository';

@model()
export class DpReportNew extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  user_type?: string;

  @property({
    type: 'number',
  })
  dcfId?: number;

  @property({
    type: 'string',
  })
  dpId?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  rp?: string[];

  @property({
    type: 'number',
  })
  site?: number;

  @property({
    type: 'number',
  })
  clientId?: number;

  @property({
    type: 'number',
  })
  reporter_id?: number;

  @property({
    type: 'number',
  })
  reviewer_id?: number;

  @property({
    type: 'number',
    itemType: 'number',
    required: false,
    mysql: {
      dataType: "BIGINT"
    }
  })
  form_id?: number;

  @property({
    type: 'number',
  })
  form_type?: number;

  @property({
    type: 'any',
  })
  year?: any;

  @property({
    type: 'any',
  })
  value?: any;

  @property({
    type: 'any',
  })
  type?: any;

  @property({
    type: 'number',
  })
  submitId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<DpReportNew>) {
    super(data);
  }
}

export interface DpReportNewRelations {
  // describe navigational properties here
}

export type DpReportNewWithRelations = DpReportNew & DpReportNewRelations;
