import {Entity, model, property, hasMany} from '@loopback/repository';
import {NewIndicator} from './new-indicator.model';

@model()
export class NewTargetsTwo extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  comments?: any[];

  @property({
    type: 'array',
    itemType: 'any',
  })
  data1?: any[];

  @property({
    type: 'number',
  })
  newGoalsId?: number;

  @hasMany(() => NewIndicator)
  newIndicators: NewIndicator[];

  constructor(data?: Partial<NewTargetsTwo>) {
    super(data);
  }
}

export interface NewTargetsTwoRelations {
  // describe navigational properties here
}

export type NewTargetsTwoWithRelations = NewTargetsTwo & NewTargetsTwoRelations;
