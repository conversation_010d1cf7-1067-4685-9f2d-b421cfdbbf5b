import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewEfDate,
  NewEf,
} from '../models';
import {NewEfDateRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfDateNewEfController {
  constructor(
    @repository(NewEfDateRepository) protected newEfDateRepository: NewEfDateRepository,
  ) { }

  @get('/new-ef-dates/{id}/new-efs', {
    responses: {
      '200': {
        description: 'Array of NewEfDate has many NewEf',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEf)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEf>,
  ): Promise<NewEf[]> {
    return this.newEfDateRepository.newEfs(id).find(filter);
  }

  @post('/new-ef-dates/{id}/new-efs', {
    responses: {
      '200': {
        description: 'NewEfDate model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEf)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEfDate.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEf, {
            title: 'NewNewEfInNewEfDate',
            exclude: ['id'],
            optional: ['newEfDateId']
          }),
        },
      },
    }) newEf: Omit<NewEf, 'id'>,
  ): Promise<NewEf> {
    return this.newEfDateRepository.newEfs(id).create(newEf);
  }

  // @patch('/new-ef-dates/{id}/new-efs', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfDate.NewEf PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEf, {partial: true}),
  //       },
  //     },
  //   })
  //   newEf: Partial<NewEf>,
  //   @param.query.object('where', getWhereSchemaFor(NewEf)) where?: Where<NewEf>,
  // ): Promise<Count> {
  //   return this.newEfDateRepository.newEfs(id).patch(newEf, where);
  // }

  // @del('/new-ef-dates/{id}/new-efs', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfDate.NewEf DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewEf)) where?: Where<NewEf>,
  // ): Promise<Count> {
  //   return this.newEfDateRepository.newEfs(id).delete(where);
  // }

  
}
