import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  AssignRfEntity,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileAssignRfEntityController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-rf-entities', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignRfEntity',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignRfEntity)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignRfEntity>,
  ): Promise<AssignRfEntity[]> {
    return this.userProfileRepository.assignRfEntities(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-rf-entities', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignRfEntity)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignRfEntity, {
            title: 'NewAssignRfEntityInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignRfEntity: Omit<AssignRfEntity, 'id'>,
  ): Promise<AssignRfEntity> {
    return this.userProfileRepository.assignRfEntities(id).create(assignRfEntity);
  }

  @patch('/user-profiles/{id}/assign-rf-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignRfEntity PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignRfEntity, {partial: true}),
        },
      },
    })
    assignRfEntity: Partial<AssignRfEntity>,
    @param.query.object('where', getWhereSchemaFor(AssignRfEntity)) where?: Where<AssignRfEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignRfEntities(id).patch(assignRfEntity, where);
  }

  @del('/user-profiles/{id}/assign-rf-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignRfEntity DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignRfEntity)) where?: Where<AssignRfEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignRfEntities(id).delete(where);
  }
}
