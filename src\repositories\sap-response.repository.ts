import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SapResponse, SapResponseRelations} from '../models';

export class SapResponseRepository extends DefaultCrudRepository<
  SapResponse,
  typeof SapResponse.prototype.id,
  SapResponseRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SapResponse, dataSource);
  }
}
