import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Certification} from '../models';
import {CertificationRepository} from '../repositories';

export class CertificationController {
  constructor(
    @repository(CertificationRepository)
    public certificationRepository : CertificationRepository,
  ) {}

  @post('/certifications')
  @response(200, {
    description: 'Certification model instance',
    content: {'application/json': {schema: getModelSchemaRef(Certification)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Certification, {
            title: 'NewCertification',
            exclude: ['id'],
          }),
        },
      },
    })
    certification: Omit<Certification, 'id'>,
  ): Promise<Certification> {
    return this.certificationRepository.create(certification);
  }

  @get('/certifications/count')
  @response(200, {
    description: 'Certification model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Certification) where?: Where<Certification>,
  ): Promise<Count> {
    return this.certificationRepository.count(where);
  }

  @get('/certifications')
  @response(200, {
    description: 'Array of Certification model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Certification, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Certification) filter?: Filter<Certification>,
  ): Promise<Certification[]> {
    return this.certificationRepository.find(filter);
  }

  @patch('/certifications')
  @response(200, {
    description: 'Certification PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Certification, {partial: true}),
        },
      },
    })
    certification: Certification,
    @param.where(Certification) where?: Where<Certification>,
  ): Promise<Count> {
    return this.certificationRepository.updateAll(certification, where);
  }

  @get('/certifications/{id}')
  @response(200, {
    description: 'Certification model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Certification, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(Certification, {exclude: 'where'}) filter?: FilterExcludingWhere<Certification>
  ): Promise<Certification> {
    return this.certificationRepository.findById(id, filter);
  }

  @patch('/certifications/{id}')
  @response(204, {
    description: 'Certification PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Certification, {partial: true}),
        },
      },
    })
    certification: Certification,
  ): Promise<void> {
    await this.certificationRepository.updateById(id, certification);
  }

  @put('/certifications/{id}')
  @response(204, {
    description: 'Certification PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() certification: Certification,
  ): Promise<void> {
    await this.certificationRepository.replaceById(id, certification);
  }

  @del('/certifications/{id}')
  @response(204, {
    description: 'Certification DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.certificationRepository.deleteById(id);
  }
}
