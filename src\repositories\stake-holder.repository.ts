import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {StakeHolder, StakeHolderRelations} from '../models';

export class StakeHolderRepository extends DefaultCrudRepository<
  StakeHolder,
  typeof StakeHolder.prototype.id,
  StakeHolderRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(StakeHolder, dataSource);
  }
}
