import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ClientInitiative, ClientInitiativeRelations} from '../models';

export class ClientInitiativeRepository extends DefaultCrudRepository<
  ClientInitiative,
  typeof ClientInitiative.prototype.id,
  ClientInitiativeRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ClientInitiative, dataSource);
  }
}
