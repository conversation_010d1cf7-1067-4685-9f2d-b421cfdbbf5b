import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SupplyCategory, SupplyCategoryRelations, SupplySection} from '../models';
import {SupplyChecklistRepository} from './supply-checklist.repository';
import {SupplySectionRepository} from './supply-section.repository';

export class SupplyCategoryRepository extends DefaultCrudRepository<
  SupplyCategory,
  typeof SupplyCategory.prototype.id,
  SupplyCategoryRelations
> {



  public readonly supplySections: HasManyRepositoryFactory<SupplySection, typeof SupplyCategory.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('SupplyChecklistRepository') protected supplyChecklistRepositoryGetter: Getter<SupplyChecklistRepository>, @repository.getter('SupplySectionRepository') protected supplySectionRepositoryGetter: Getter<SupplySectionRepository>,
  ) {
    super(SupplyCategory, dataSource);
    this.supplySections = this.createHasManyRepositoryFactoryFor('supplySections', supplySectionRepositoryGetter,);
    this.registerInclusionResolver('supplySections', this.supplySections.inclusionResolver);

  }
}
