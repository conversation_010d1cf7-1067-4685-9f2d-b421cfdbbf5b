import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignRfEntity} from '../models';
import {AssignRfEntityRepository} from '../repositories';

export class AssignRfEntityController {
  constructor(
    @repository(AssignRfEntityRepository)
    public assignRfEntityRepository : AssignRfEntityRepository,
  ) {}

  @post('/assign-rf-entities')
  @response(200, {
    description: 'AssignRfEntity model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignRfEntity)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignRfEntity, {
            title: 'NewAssignRfEntity',
            exclude: ['id'],
          }),
        },
      },
    })
    assignRfEntity: Omit<AssignRfEntity, 'id'>,
  ): Promise<AssignRfEntity> {
    return this.assignRfEntityRepository.create(assignRfEntity);
  }

  @get('/assign-rf-entities/count')
  @response(200, {
    description: 'AssignRfEntity model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignRfEntity) where?: Where<AssignRfEntity>,
  ): Promise<Count> {
    return this.assignRfEntityRepository.count(where);
  }

  @get('/assign-rf-entities')
  @response(200, {
    description: 'Array of AssignRfEntity model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignRfEntity, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignRfEntity) filter?: Filter<AssignRfEntity>,
  ): Promise<AssignRfEntity[]> {
    return this.assignRfEntityRepository.find(filter);
  }

  @patch('/assign-rf-entities')
  @response(200, {
    description: 'AssignRfEntity PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignRfEntity, {partial: true}),
        },
      },
    })
    assignRfEntity: AssignRfEntity,
    @param.where(AssignRfEntity) where?: Where<AssignRfEntity>,
  ): Promise<Count> {
    return this.assignRfEntityRepository.updateAll(assignRfEntity, where);
  }

  @get('/assign-rf-entities/{id}')
  @response(200, {
    description: 'AssignRfEntity model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignRfEntity, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignRfEntity, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignRfEntity>
  ): Promise<AssignRfEntity> {
    return this.assignRfEntityRepository.findById(id, filter);
  }

  @patch('/assign-rf-entities/{id}')
  @response(204, {
    description: 'AssignRfEntity PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignRfEntity, {partial: true}),
        },
      },
    })
    assignRfEntity: AssignRfEntity,
  ): Promise<void> {
    await this.assignRfEntityRepository.updateById(id, assignRfEntity);
  }

  @put('/assign-rf-entities/{id}')
  @response(204, {
    description: 'AssignRfEntity PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignRfEntity: AssignRfEntity,
  ): Promise<void> {
    await this.assignRfEntityRepository.replaceById(id, assignRfEntity);
  }

  @del('/assign-rf-entities/{id}')
  @response(204, {
    description: 'AssignRfEntity DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignRfEntityRepository.deleteById(id);
  }
}
