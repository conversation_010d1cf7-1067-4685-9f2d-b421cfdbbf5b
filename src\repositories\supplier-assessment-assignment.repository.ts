import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, HasManyRepositoryFactory, HasOneRepositoryFactory, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AuditorAssignmentSubmission, ConsolidateFormCollection, SupplierAction, SupplierAssessmentAssignment, SupplierAssessmentAssignmentRelations, SupplierAssignmentSubmission, SupplierSectionSubmission, UserProfile, VendorCode} from '../models';
import {AuditorAssignmentSubmissionRepository} from './auditor-assignment-submission.repository';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';
import {SupplierActionRepository} from './supplier-action.repository';
import {SupplierAssignmentSubmissionRepository} from './supplier-assignment-submission.repository';
import {SupplierSectionSubmissionRepository} from './supplier-section-submission.repository';
import {UserProfileRepository} from './user-profile.repository';
import {VendorCodeRepository} from './vendor-code.repository';

export class SupplierAssessmentAssignmentRepository extends DefaultCrudRepository<
  SupplierAssessmentAssignment,
  typeof SupplierAssessmentAssignment.prototype.id,
  SupplierAssessmentAssignmentRelations
> {

  public readonly supplierAssignmentSubmission: HasOneRepositoryFactory<SupplierAssignmentSubmission, typeof SupplierAssessmentAssignment.prototype.id>;

  public readonly auditorAssignmentSubmission: HasOneRepositoryFactory<AuditorAssignmentSubmission, typeof SupplierAssessmentAssignment.prototype.id>;
  public readonly srf: BelongsToAccessor<ConsolidateFormCollection, typeof SupplierAssessmentAssignment.prototype.id>;

  public readonly supplierSectionSubmissions: HasManyRepositoryFactory<SupplierSectionSubmission, typeof SupplierAssessmentAssignment.prototype.id>;

  public readonly supplierActions: HasManyRepositoryFactory<SupplierAction, typeof SupplierAssessmentAssignment.prototype.id>;

  public readonly supplier: BelongsToAccessor<UserProfile, typeof SupplierAssessmentAssignment.prototype.id>;

  public readonly vendor: BelongsToAccessor<VendorCode, typeof SupplierAssessmentAssignment.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('SupplierAssignmentSubmissionRepository') protected supplierAssignmentSubmissionRepositoryGetter: Getter<SupplierAssignmentSubmissionRepository>, @repository.getter('AuditorAssignmentSubmissionRepository') protected auditorAssignmentSubmissionRepositoryGetter: Getter<AuditorAssignmentSubmissionRepository>,
    @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>, @repository.getter('SupplierSectionSubmissionRepository') protected supplierSectionSubmissionRepositoryGetter: Getter<SupplierSectionSubmissionRepository>, @repository.getter('SupplierActionRepository') protected supplierActionRepositoryGetter: Getter<SupplierActionRepository>, @repository.getter('UserProfileRepository') protected userProfileRepositoryGetter: Getter<UserProfileRepository>, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>,
  ) {
    super(SupplierAssessmentAssignment, dataSource);
    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
    this.supplier = this.createBelongsToAccessorFor('supplier', userProfileRepositoryGetter,);
    this.registerInclusionResolver('supplier', this.supplier.inclusionResolver);
    this.supplierActions = this.createHasManyRepositoryFactoryFor('supplierActions', supplierActionRepositoryGetter,);
    this.registerInclusionResolver('supplierActions', this.supplierActions.inclusionResolver);
    this.supplierSectionSubmissions = this.createHasManyRepositoryFactoryFor('supplierSectionSubmissions', supplierSectionSubmissionRepositoryGetter,);
    this.registerInclusionResolver('SupplierSectionSubmissions', this.supplierSectionSubmissions.inclusionResolver);
    this.auditorAssignmentSubmission = this.createHasOneRepositoryFactoryFor('auditorAssignmentSubmission', auditorAssignmentSubmissionRepositoryGetter,);
    this.registerInclusionResolver('auditorAssignmentSubmission', this.auditorAssignmentSubmission.inclusionResolver);
    this.supplierAssignmentSubmission = this.createHasOneRepositoryFactoryFor('supplierAssignmentSubmission', supplierAssignmentSubmissionRepositoryGetter,);
    this.registerInclusionResolver('supplierAssignmentSubmission', this.supplierAssignmentSubmission.inclusionResolver);
    this.srf = this.createBelongsToAccessorFor('srf', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('srf', this.srf.inclusionResolver);
  }
}
