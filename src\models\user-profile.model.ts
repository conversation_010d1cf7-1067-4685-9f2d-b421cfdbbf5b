import {Entity, hasMany, hasOne, model, property} from '@loopback/repository';
import {AssignDcfClient} from './assign-dcf-client.model';
import {AssignDcfEntityUser} from './assign-dcf-entity-user.model';
import {AssignDcfEntity} from './assign-dcf-entity.model';
import {AssignDcfSuppliers} from './assign-dcf-suppliers.model';
import {AssignDcfUserNew} from './assign-dcf-user-new.model';
import {AssignDcfUser} from './assign-dcf-user.model';
import {AssignDfEntityUser} from './assign-df-entity-user.model';
import {AssignDfEntity} from './assign-df-entity.model';
import {AssignDfUser} from './assign-df-user.model';
import {AssignQlEntityUser} from './assign-ql-entity-user.model';
import {AssignQlEntity} from './assign-ql-entity.model';
import {AssignRfEntity} from './assign-rf-entity.model';
import {AssignRfUsers} from './assign-rf-users.model';
import {AssignSrfEntityUser} from './assign-srf-entity-user.model';
import {AssignSrfEntity} from './assign-srf-entity.model';
import {AssignSrfUser} from './assign-srf-user.model';
import {ClientEfCategoryAssignment} from './client-ef-category-assignment.model';
import {ClientEfCategoryMapping} from './client-ef-category-mapping.model';
import {ClientInitiative} from './client-initiative.model';
import {ComputedIndicator} from './computed-indicator.model';
import {DealerAssessmentAssignment} from './dealer-assessment-assignment.model';
import {DealerChecklistSubmission} from './dealer-checklist-submission.model';
import {DealerResponseForm} from './dealer-response-form.model';
import {DeleteUserLog} from './delete-user-log.model';
import {DpReportNew} from './dp-report-new.model';
import {DpReport} from './dp-report.model';
import {FormCollection} from './form-collection.model';
import {Frequency} from './frequency.model';
import {Helper} from './helper.model';
import {IndicatorApproverAssignment} from './indicator-approver-assignment.model';
import {IndicatorSection} from './indicator-section.model';
import {LocationOne} from './location-one.model';
import {NewClientCertification} from './new-client-certification.model';
import {NewGoals} from './new-goals.model';
import {NewsCirculation} from './news-circulation.model';
import {PolicyProcedure} from './policy-procedure.model';
import {QlListingFilter} from './ql-listing-filter.model';
import {QnIndicatorApproval} from './qn-indicator-approval.model';
import {QualitativeApproval} from './qualitative-approval.model';
import {QualitativeSubmission} from './qualitative-submission.model';
import {QuantitativeDpReport} from './quantitative-dp-report.model';
import {QuantitativeSubmission} from './quantitative-submission.model';
import {SapCollection} from './sap-collection.model';
import {SapResponse} from './sap-response.model';
import {StructuredResponse} from './structured-response.model';
import {SubmitCf} from './submit-cf.model';
import {SubmitDcfNew} from './submit-dcf-new.model';
import {SubmitDcf} from './submit-dcf.model';
import {SubmitRfNew} from './submit-rf-new.model';
import {SubmitRf} from './submit-rf.model';
import {SupplierAssessmentAssignment} from './supplier-assessment-assignment.model';
import {SupplierSectionSubmission} from './supplier-section-submission.model';
import {Ticketing} from './ticketing.model';
import {ValueChainSubmission} from './value-chain-submission.model';
import {VendorCode} from './vendor-code.model';

@model()
export class UserProfile extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  company?: string;


  @property({
    type: 'object',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  information?: any;
  @property({
    type: 'object',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  coginfo?: any;

  @property({
    type: 'string',


  })
  role?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierCode?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  dealerCode?: string | null;

  @property({
    type: 'boolean',
    default: true,
  })
  active?: boolean;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  userPortalUrl: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  adminPortalUrl: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierPortalUrl: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  cognitoRefUserName: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  supervisor: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_by: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  created_by: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  emailSentCount: number | null;

  @property({
    type: 'boolean',
    default: false
  })
  ssoLogin: boolean;

  @property({
    type: 'string',
  })
  userId: string;

  @property({
    type: 'number',

  })
  clientId: number;

  @property({
    type: 'object',
  })
  access?: object;

  @property({
    type: 'string',
  })
  fyStartMonth: string;

  @property({
    type: 'array',
    itemType: 'string'
  })
  tierLabel: string[];
  @property({
    type: 'any',
    default: 0,
  })
  blocked?: any;

  @hasMany(() => LocationOne)
  locationOnes: LocationOne[];

  @hasMany(() => Frequency)
  frequencies: Frequency[];

  @hasMany(() => AssignDcfClient)
  assignDcfClients: AssignDcfClient[];

  @hasMany(() => NewGoals)
  newGoals: NewGoals[];

  @hasMany(() => FormCollection)
  formCollections: FormCollection[];

  @hasMany(() => AssignDcfUser)
  assignDcfUsers: AssignDcfUser[];

  @hasMany(() => AssignDcfSuppliers)
  assignDcfSuppliers: AssignDcfSuppliers[];

  @hasMany(() => SubmitDcf)
  submitDcfs: SubmitDcf[];

  @hasMany(() => DpReport)
  dpReports: DpReport[];

  @hasMany(() => SubmitRf)
  submitRfs: SubmitRf[];

  @hasMany(() => QlListingFilter)
  qlListingFilters: QlListingFilter[];

  @hasMany(() => AssignRfUsers)
  assignRfUsers: AssignRfUsers[];

  @hasMany(() => SubmitCf)
  submitCfs: SubmitCf[];

  @hasMany(() => AssignDcfUserNew)
  assignDcfUserNews: AssignDcfUserNew[];

  @hasMany(() => SubmitRfNew)
  submitRfNews: SubmitRfNew[];

  @hasMany(() => SubmitDcfNew)
  submitDcfNews: SubmitDcfNew[];

  @hasMany(() => DpReportNew)
  dpReportNews: DpReportNew[];

  @hasMany(() => AssignDfUser)
  assignDfUsers: AssignDfUser[];

  @hasMany(() => AssignSrfUser)
  assignSrfUsers: AssignSrfUser[];

  @hasMany(() => AssignRfEntity)
  assignRfEntities: AssignRfEntity[];

  @hasMany(() => AssignDcfEntity)
  assignDcfEntities: AssignDcfEntity[];

  @hasMany(() => AssignDfEntity)
  assignDfEntities: AssignDfEntity[];

  @hasMany(() => AssignSrfEntity)
  assignSrfEntities: AssignSrfEntity[];

  @hasMany(() => AssignDcfEntityUser)
  assignDcfEntityUsers: AssignDcfEntityUser[];

  @hasMany(() => AssignDfEntityUser)
  assignDfEntityUsers: AssignDfEntityUser[];

  @hasMany(() => AssignSrfEntityUser)
  assignSrfEntityUsers: AssignSrfEntityUser[];

  @hasMany(() => QuantitativeSubmission)
  quantitativeSubmissions: QuantitativeSubmission[];

  @hasMany(() => ClientEfCategoryAssignment)
  clientEfCategoryAssignments: ClientEfCategoryAssignment[];

  @hasMany(() => ClientInitiative)
  clientInitiatives: ClientInitiative[];

  @hasMany(() => QuantitativeDpReport)
  quantitativeDpReports: QuantitativeDpReport[];

  @hasMany(() => NewClientCertification)
  newClientCertifications: NewClientCertification[];

  @hasMany(() => QualitativeSubmission)
  qualitativeSubmissions: QualitativeSubmission[];

  @hasMany(() => IndicatorApproverAssignment)
  indicatorApproverAssignments: IndicatorApproverAssignment[];

  @hasMany(() => SupplierAssessmentAssignment)
  supplierAssessmentAssignments: SupplierAssessmentAssignment[];

  @hasMany(() => PolicyProcedure)
  policyProcedures: PolicyProcedure[];

  @hasMany(() => QualitativeApproval)
  qualitativeApprovals: QualitativeApproval[];

  @hasMany(() => SupplierSectionSubmission)
  SupplierSectionSubmissions: SupplierSectionSubmission[];

  @hasMany(() => QnIndicatorApproval)
  qnIndicatorApprovals: QnIndicatorApproval[];

  @hasMany(() => DealerResponseForm)
  dealerResponseForms: DealerResponseForm[];

  @hasOne(() => Helper)
  helper: Helper;

  @hasMany(() => DealerAssessmentAssignment)
  dealerAssessmentAssignments: DealerAssessmentAssignment[];

  @hasMany(() => IndicatorSection)
  indicatorSections: IndicatorSection[];

  @hasMany(() => SapCollection)
  sapCollections: SapCollection[];

  @hasMany(() => SapResponse)
  sapResponses: SapResponse[];

  @hasMany(() => ClientEfCategoryMapping)
  clientEfCategoryMappings: ClientEfCategoryMapping[];

  @hasMany(() => StructuredResponse)
  structuredResponses: StructuredResponse[];

  @hasMany(() => DealerChecklistSubmission)
  dealerChecklistSubmissions: DealerChecklistSubmission[];

  @hasMany(() => VendorCode)
  vendorCodes: VendorCode[];

  @hasMany(() => ValueChainSubmission)
  valueChainSubmissions: ValueChainSubmission[];

  @hasMany(() => Ticketing)
  ticketings: Ticketing[];

  @hasMany(() => NewsCirculation)
  newsCirculations: NewsCirculation[];

  @hasMany(() => AssignQlEntity)
  assignQlEntities: AssignQlEntity[];

  @hasMany(() => AssignQlEntityUser)
  assignQlEntityUsers: AssignQlEntityUser[];

  @hasMany(() => DeleteUserLog)
  deleteUserLogs: DeleteUserLog[];

  @hasMany(() => ComputedIndicator)
  computedIndicators: ComputedIndicator[];

  constructor(data?: Partial<UserProfile>) {
    super(data);
  }
}

export interface UserProfileRelations {
  // describe navigational properties here
}

export type UserProfileWithRelations = UserProfile & UserProfileRelations;
