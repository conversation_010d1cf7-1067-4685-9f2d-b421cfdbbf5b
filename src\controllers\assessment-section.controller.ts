import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {AssessmentSection} from '../models';
import {AssessmentSectionRepository, SupplierSectionSubmissionRepository} from '../repositories';



export class AssessmentSectionController {
  constructor(
    @repository(AssessmentSectionRepository)
    public assessmentSectionRepository: AssessmentSectionRepository,
    @repository(SupplierSectionSubmissionRepository)
    public supplierSectionSubmissionRepository: SupplierSectionSubmissionRepository,
  ) { }

  @post('/assessment-sections')
  @response(200, {
    description: 'AssessmentSection model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssessmentSection)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSection, {
            title: 'NewAssessmentSection',
            exclude: ['id'],
          }),
        },
      },
    })
    assessmentSection: Omit<AssessmentSection, 'id'>,
  ): Promise<AssessmentSection> {
    return this.assessmentSectionRepository.create(assessmentSection);
  }

  @get('/assessment-sections/count')
  @response(200, {
    description: 'AssessmentSection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssessmentSection) where?: Where<AssessmentSection>,
  ): Promise<Count> {
    return this.assessmentSectionRepository.count(where);
  }

  @get('/assessment-sections')
  @response(200, {
    description: 'Array of AssessmentSection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssessmentSection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssessmentSection) filter?: Filter<AssessmentSection>,
  ): Promise<AssessmentSection[]> {
    return this.assessmentSectionRepository.find(filter);
  }

  @patch('/assessment-sections')
  @response(200, {
    description: 'AssessmentSection PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSection, {partial: true}),
        },
      },
    })
    assessmentSection: AssessmentSection,
    @param.where(AssessmentSection) where?: Where<AssessmentSection>,
  ): Promise<Count> {
    return this.assessmentSectionRepository.updateAll(assessmentSection, where);
  }

  @get('/assessment-sections/{id}')
  @response(200, {
    description: 'AssessmentSection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssessmentSection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AssessmentSection, {exclude: 'where'}) filter?: FilterExcludingWhere<AssessmentSection>
  ): Promise<AssessmentSection> {
    return this.assessmentSectionRepository.findById(id, filter);
  }

  @patch('/assessment-sections/{id}')
  @response(204, {
    description: 'AssessmentSection PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSection, {partial: true}),
        },
      },
    })
    assessmentSection: AssessmentSection,
  ): Promise<void> {
    await this.assessmentSectionRepository.updateById(id, assessmentSection);
  }

  @put('/assessment-sections/{id}')
  @response(204, {
    description: 'AssessmentSection PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() assessmentSection: AssessmentSection,
  ): Promise<void> {
    await this.assessmentSectionRepository.replaceById(id, assessmentSection);
  }

  @del('/assessment-sections/{id}')
  @response(204, {
    description: 'AssessmentSection DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.assessmentSectionRepository.deleteById(id);
  }

  @post('/assessment-sections-custom')
  @response(200, {
    description: 'AssessmentSection model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssessmentSection)}},
  })
  async getAssessment(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {

              assignmentId: {
                type: 'string',
              }
            },
            required: ['assignmentId'],
          },
        },
      },
    })
    requestBody: any,
  ): Promise<{}> {
    const {assignmentId, type} = requestBody


    let sectionData = await this.assessmentSectionRepository.find({
      include: [
        {
          relation: "assessmentSubSection1s",
          scope: {
            include: [{
              relation: "assessmentSubSection2s", "scope": {include: ['form']}
            }],

          },
        },
      ],
    })

    let submittedData = await this.supplierSectionSubmissionRepository.find({where: {supplierAssessmentAssignmentId: assignmentId}})
    const sectionData2 = JSON.parse(JSON.stringify(sectionData))
    const result = await this.processSections(sectionData2, submittedData);


    return result
  }
  processSections(
    sectionData: any[],
    submittedData: any[]
  ): Promise<any[]> {
    return new Promise((resolve) => {
      for (const section of sectionData) {
        if (section.assessmentSubSection1s) {
          // Initialize counts for the section
          section.totalCount = section.assessmentSubSection1s.length;
          section.submittedCount = 0;
          section.draftedCount = 0;

          for (const subsection1 of section.assessmentSubSection1s) {
            if (subsection1.assessmentSubSection2s) {
              // Initialize counts for subsection1
              subsection1.totalCount = subsection1.assessmentSubSection2s.length;
              subsection1.submittedCount = 0;
              subsection1.draftedCount = 0;

              for (let subsection2 of subsection1.assessmentSubSection2s) {
                // Check if subsection2 is submitted and get its type
                const submission = submittedData.find(
                  (i) =>
                    i.assessmentSectionId === section.id &&
                    i.assessmentSubSection1Id === subsection1.id &&
                    i.assessmentSubSection2Id === subsection2.id
                );

                if (submission) {
                  subsection2.submitted = submission;

                  if (submission.type === 1) {
                    subsection2.count = 1
                    subsection1.submittedCount++; // Increment submittedCount for type 1
                  } else if (submission.type === 0) {
                    subsection2.count = 0
                    subsection1.draftedCount++; // Increment draftedCount for type 0
                  }
                }
              }

              // Check if all subsection2s of subsection1 are submitted
              if (subsection1.submittedCount === subsection1.totalCount) {
                section.submittedCount++; // Increment section's submittedCount
              }

              // Check if all subsection2s of subsection1 are drafted
              if (subsection1.draftedCount === subsection1.totalCount) {
                section.draftedCount++; // Increment section's draftedCount
              }
            } else {
              // Handle cases where subsection1 has no subsection2s
              subsection1.totalCount = 0;
              subsection1.submittedCount = 0;
              subsection1.draftedCount = 0;
            }
          }
        } else {
          // Handle cases where section has no subsection1s
          section.totalCount = 0;
          section.submittedCount = 0;
          section.draftedCount = 0;
        }
      }

      resolve(sectionData); // Resolve with the updated data
    });
  }







}
