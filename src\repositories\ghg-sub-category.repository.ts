import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {GhgSubCategory, GhgSubCategoryRelations, NewEfCategory} from '../models';
import {NewEfCategoryRepository} from './new-ef-category.repository';

export class GhgSubCategoryRepository extends DefaultCrudRepository<
  GhgSubCategory,
  typeof GhgSubCategory.prototype.id,
  GhgSubCategoryRelations
> {

  public readonly newEfCategories: HasManyRepositoryFactory<NewEfCategory, typeof GhgSubCategory.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfCategoryRepository') protected newEfCategoryRepositoryGetter: Getter<NewEfCategoryRepository>,
  ) {
    super(GhgSubCategory, dataSource);
    this.newEfCategories = this.createHasManyRepositoryFactoryFor('newEfCategories', newEfCategoryRepositoryGetter,);
    this.registerInclusionResolver('newEfCategories', this.newEfCategories.inclusionResolver);
  }
}
