import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignDcfSuppliers} from '../models';
import {AssignDcfSuppliersRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';



export class AssignDcfSuppliersController {
  constructor(
    @repository(AssignDcfSuppliersRepository)
    public assignDcfSuppliersRepository : AssignDcfSuppliersRepository,
  ) {}

  @post('/assign-dcf-suppliers')
  @response(200, {
    description: 'AssignDcfSuppliers model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDcfSuppliers)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfSuppliers, {
            title: 'NewAssignDcfSuppliers',
            exclude: ['id'],
          }),
        },
      },
    })
    assignDcfSuppliers: Omit<AssignDcfSuppliers, 'id'>,
  ): Promise<AssignDcfSuppliers> {
    return this.assignDcfSuppliersRepository.create(assignDcfSuppliers);
  }

  @get('/assign-dcf-suppliers/count')
  @response(200, {
    description: 'AssignDcfSuppliers model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDcfSuppliers) where?: Where<AssignDcfSuppliers>,
  ): Promise<Count> {
    return this.assignDcfSuppliersRepository.count(where);
  }

  @get('/assign-dcf-suppliers')
  @response(200, {
    description: 'Array of AssignDcfSuppliers model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDcfSuppliers, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDcfSuppliers) filter?: Filter<AssignDcfSuppliers>,
  ): Promise<AssignDcfSuppliers[]> {
    return this.assignDcfSuppliersRepository.find(filter);
  }

  // @patch('/assign-dcf-suppliers')
  // @response(200, {
  //   description: 'AssignDcfSuppliers PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfSuppliers, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfSuppliers: AssignDcfSuppliers,
  //   @param.where(AssignDcfSuppliers) where?: Where<AssignDcfSuppliers>,
  // ): Promise<Count> {
  //   return this.assignDcfSuppliersRepository.updateAll(assignDcfSuppliers, where);
  // }

  @get('/assign-dcf-suppliers/{id}')
  @response(200, {
    description: 'AssignDcfSuppliers model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDcfSuppliers, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDcfSuppliers, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDcfSuppliers>
  ): Promise<AssignDcfSuppliers> {
    return this.assignDcfSuppliersRepository.findById(id, filter);
  }

  @patch('/assign-dcf-suppliers/{id}')
  @response(204, {
    description: 'AssignDcfSuppliers PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfSuppliers, {partial: true}),
        },
      },
    })
    assignDcfSuppliers: AssignDcfSuppliers,
  ): Promise<void> {
    await this.assignDcfSuppliersRepository.updateById(id, assignDcfSuppliers);
  }

  @put('/assign-dcf-suppliers/{id}')
  @response(204, {
    description: 'AssignDcfSuppliers PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignDcfSuppliers: AssignDcfSuppliers,
  ): Promise<void> {
    await this.assignDcfSuppliersRepository.replaceById(id, assignDcfSuppliers);
  }

  @del('/assign-dcf-suppliers/{id}')
  @response(204, {
    description: 'AssignDcfSuppliers DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDcfSuppliersRepository.deleteById(id);
  }
}
