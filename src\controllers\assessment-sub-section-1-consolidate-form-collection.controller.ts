import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssessmentSubSection1,
  ConsolidateFormCollection,
} from '../models';
import {AssessmentSubSection1Repository} from '../repositories';

export class AssessmentSubSection1ConsolidateFormCollectionController {
  constructor(
    @repository(AssessmentSubSection1Repository)
    public assessmentSubSection1Repository: AssessmentSubSection1Repository,
  ) { }

  @get('/assessment-sub-section1s/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to AssessmentSubSection1',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ConsolidateFormCollection),
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.string('id') id: typeof AssessmentSubSection1.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.assessmentSubSection1Repository.srf(id);
  }
}
