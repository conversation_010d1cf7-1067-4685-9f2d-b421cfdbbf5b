// Uncomment these imports to begin using these cool features!

// import {inject} from '@loopback/core';
import {inject} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  get,
  post,
  requestBody
} from '@loopback/rest';
import {createTransport} from 'nodemailer';
import {StakeHolderRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';


const MailService = createTransport({
  port: 465,               // true for 465, false for other ports
  host: "smtp.gmail.com",
  auth: {
    user: '<EMAIL>',
    pass: 'dgwbhnasqxxgnxmb',
  },
  secure: true,
})
type keyAndPassword = {
  type: number,
  enterprise: string,
  name: string,
  site: string,
  form: string,
  rp: string,
  email: string,
  date: string,
  subject: string
}



export class MailTestController {
  constructor(
    @repository(StakeHolderRepository)
    public stakeHolderRepository: StakeHolderRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @get('/sendMail')
  async sendTestMail(): Promise<object> {


    // Reply with a greeting, the current time, the url, and request headers
    const mailData = {
      from: '<EMAIL>',  // sender address
      to: '<EMAIL>',   // list of receivers
      subject: 'Sending Email using Node.js',
      text: 'That was easy!',
      html: `<b>Hey there! </b>
               <br> This is our first message sent with Nodemailer. You quota for tomorrow is over<br/>`,
    };
    const MailService = createTransport({
      port: 465,               // true for 465, false for other ports
      host: "smtp.gmail.com",
      auth: {
        user: '<EMAIL>',
        pass: 'dgwbhnasqxxgnxmb',
      },
      secure: true,
    })
    const info = await this.sqsService.sendEmail(mailData.to, mailData.subject, mailData.html, []).then((info) => {

      return {}
    }).catch((err) => {
      return {}
    })


    return {}
  }

  @post('/email')
  async sendMail(
    @requestBody() data: any,
  ): Promise<boolean> {

    data.category = data.category.label


    const users = await this.stakeHolderRepository.find({where: {categories: data.category}});

    users.forEach(async (element) => {
      if (element.email) {
        const info = await this.sqsService.sendEmail(element.email, 'Eisqr Questionnaire Link', `<b>Hey there! </b>
      <br> Link to Questionnaire <a href="${data.link}/${element.id}" target="_blank">${data.link}</a><br/>`, []).then((info) => {
          return true
        }).catch((err) => {
          return false
        })
      }


    })

    return true

  }
  // @post('/post-email')
  // async testEMail(
  //   @requestBody() data: any,
  // ): Promise<boolean> {
  //   const {to, subject, body, cc} = data

  //   try {
  //     const info = await this.sqsService.sendEmail(to, subject, body, cc).then((info) => {
  //       console.log(info)
  //       return info
  //     }).catch((err) => {
  //       console.log(err)
  //       return err
  //     })
  //     return info

  //   } catch (error) {
  //     console.error(error);
  //     return false; // Failed to send email
  //   }
  // }

  @post('/post-email')
  async testNewEMail(
    @requestBody() data: any,
  ): Promise<boolean> {
    const {to, subject, body, cc} = data

    try {
      const info = await this.sqsService.sendEmail(to, subject, body, cc).then((info) => {
        console.log(info)
        return info
      }).catch((err) => {
        console.log(err)
        return err
      })
      return info

    } catch (error) {
      console.error(error);
      return false; // Failed to send email
    }
  }


  @post('/send-email')
  async sendEMail(
    @requestBody() data: any,
  ): Promise<boolean> {

    try {
      const info = await this.sqsService.sendEmail(data.email, data.subject, data.body, []).then((info) => {
        console.log(info)
        return info
      }).catch((err) => {
        console.log(err)
        return err
      })
      return info

    } catch (error) {
      console.error(error);
      return false; // Failed to send email
    }
  }



  @post('/send-remainder')
  async sendRemainder(
    @requestBody() data: keyAndPassword,
  ): Promise<string> {
    let textData = ''
    if (data.type === 1) {
      textData = `<p>The following data submissions are over due from you as of ${data.date}. Please submit the same at the earliest so that the consolidated report can be prepared by ${data.enterprise}.</p> <p>Form Name : ${data.form}</p> <p> Site: ${data.site}</p> <p> Reporting Period : ${data.rp}</p> `

    } else if (data.type === 2) {
      textData = `<p>The following data submissions are overdue from your Team Member ${data.name} as of ${data.date}. Please assist in expediting the submission at the earliest so that the consolidated report can be prepared by ${data.enterprise}.</p> <p>Form Name : ${data.form}</p> <p> Site: ${data.site}</p> <p> Reporting Period : ${data.rp}</p>  `
    } else if (data.type === 3) {
      textData = `<p>The following data submissions are due for review by you as of ${data.date}. Please assist in completion of the same at the earliest so that the consolidated report can be prepared by ${data.enterprise}. </p> <p>Form Name : ${data.form}</p> <p> Site: ${data.site}</p> <p> Reporting Period : ${data.rp}</p> `
    } else if (data.type === 4) {
      textData = `<p>The following data submisisons are due for review from your Team Member ${data.name} as of ${data.date}. Please assist in expediting the submission at the earliest so that the consolidated report can be prepared by ${data.enterprise}. </p> <p>Form Name : ${data.form}</p> <p> Site: ${data.site}</p> <p> Reporting Period : ${data.rp}</p> `
    }

    const info = await this.sqsService.sendEmail(data.email, data.subject, textData, []).then((info) => {
      console.log(info)
      return 'Mail has been sent Successfully';
    }).catch((err) => {
      console.log(err)
      return err
    })
    return info

  }
}
