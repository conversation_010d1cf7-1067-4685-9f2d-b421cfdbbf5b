import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ClientEfCategoryAssignment} from '../models';
import {ClientEfCategoryAssignmentRepository} from '../repositories';

export class ClientEfCategoryAssignmentController {
  constructor(
    @repository(ClientEfCategoryAssignmentRepository)
    public clientEfCategoryAssignmentRepository : ClientEfCategoryAssignmentRepository,
  ) {}

  @post('/client-ef-category-assignments')
  @response(200, {
    description: 'ClientEfCategoryAssignment model instance',
    content: {'application/json': {schema: getModelSchemaRef(ClientEfCategoryAssignment)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryAssignment, {
            title: 'NewClientEfCategoryAssignment',
            exclude: ['id'],
          }),
        },
      },
    })
    clientEfCategoryAssignment: Omit<ClientEfCategoryAssignment, 'id'>,
  ): Promise<ClientEfCategoryAssignment> {
    return this.clientEfCategoryAssignmentRepository.create(clientEfCategoryAssignment);
  }

  @get('/client-ef-category-assignments/count')
  @response(200, {
    description: 'ClientEfCategoryAssignment model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ClientEfCategoryAssignment) where?: Where<ClientEfCategoryAssignment>,
  ): Promise<Count> {
    return this.clientEfCategoryAssignmentRepository.count(where);
  }

  @get('/client-ef-category-assignments')
  @response(200, {
    description: 'Array of ClientEfCategoryAssignment model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ClientEfCategoryAssignment, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ClientEfCategoryAssignment) filter?: Filter<ClientEfCategoryAssignment>,
  ): Promise<ClientEfCategoryAssignment[]> {
    return this.clientEfCategoryAssignmentRepository.find(filter);
  }

  @patch('/client-ef-category-assignments')
  @response(200, {
    description: 'ClientEfCategoryAssignment PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryAssignment, {partial: true}),
        },
      },
    })
    clientEfCategoryAssignment: ClientEfCategoryAssignment,
    @param.where(ClientEfCategoryAssignment) where?: Where<ClientEfCategoryAssignment>,
  ): Promise<Count> {
    return this.clientEfCategoryAssignmentRepository.updateAll(clientEfCategoryAssignment, where);
  }

  @get('/client-ef-category-assignments/{id}')
  @response(200, {
    description: 'ClientEfCategoryAssignment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ClientEfCategoryAssignment, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ClientEfCategoryAssignment, {exclude: 'where'}) filter?: FilterExcludingWhere<ClientEfCategoryAssignment>
  ): Promise<ClientEfCategoryAssignment> {
    return this.clientEfCategoryAssignmentRepository.findById(id, filter);
  }

  @patch('/client-ef-category-assignments/{id}')
  @response(204, {
    description: 'ClientEfCategoryAssignment PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryAssignment, {partial: true}),
        },
      },
    })
    clientEfCategoryAssignment: ClientEfCategoryAssignment,
  ): Promise<void> {
    await this.clientEfCategoryAssignmentRepository.updateById(id, clientEfCategoryAssignment);
  }

  @put('/client-ef-category-assignments/{id}')
  @response(204, {
    description: 'ClientEfCategoryAssignment PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() clientEfCategoryAssignment: ClientEfCategoryAssignment,
  ): Promise<void> {
    await this.clientEfCategoryAssignmentRepository.replaceById(id, clientEfCategoryAssignment);
  }

  @del('/client-ef-category-assignments/{id}')
  @response(204, {
    description: 'ClientEfCategoryAssignment DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.clientEfCategoryAssignmentRepository.deleteById(id);
  }
}
