import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ScopeName} from '../models';
import {ScopeNameRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ScopeNameController {
  constructor(
    @repository(ScopeNameRepository)
    public scopeNameRepository : ScopeNameRepository,
  ) {}

  @post('/scope-names')
  @response(200, {
    description: 'ScopeName model instance',
    content: {'application/json': {schema: getModelSchemaRef(ScopeName)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeName, {
            title: 'NewScopeName',
            exclude: ['id'],
          }),
        },
      },
    })
    scopeName: Omit<ScopeName, 'id'>,
  ): Promise<ScopeName> {
    return this.scopeNameRepository.create(scopeName);
  }

  @get('/scope-names/count')
  @response(200, {
    description: 'ScopeName model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ScopeName) where?: Where<ScopeName>,
  ): Promise<Count> {
    return this.scopeNameRepository.count(where);
  }

  @get('/scope-names')
  @response(200, {
    description: 'Array of ScopeName model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ScopeName, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ScopeName) filter?: Filter<ScopeName>,
  ): Promise<ScopeName[]> {
    return this.scopeNameRepository.find(filter);
  }

  // @patch('/scope-names')
  // @response(200, {
  //   description: 'ScopeName PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ScopeName, {partial: true}),
  //       },
  //     },
  //   })
  //   scopeName: ScopeName,
  //   @param.where(ScopeName) where?: Where<ScopeName>,
  // ): Promise<Count> {
  //   return this.scopeNameRepository.updateAll(scopeName, where);
  // }

  @get('/scope-names/{id}')
  @response(200, {
    description: 'ScopeName model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ScopeName, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ScopeName, {exclude: 'where'}) filter?: FilterExcludingWhere<ScopeName>
  ): Promise<ScopeName> {
    return this.scopeNameRepository.findById(id, filter);
  }

  @patch('/scope-names/{id}')
  @response(204, {
    description: 'ScopeName PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeName, {partial: true}),
        },
      },
    })
    scopeName: ScopeName,
  ): Promise<void> {
    await this.scopeNameRepository.updateById(id, scopeName);
  }

  @put('/scope-names/{id}')
  @response(204, {
    description: 'ScopeName PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() scopeName: ScopeName,
  ): Promise<void> {
    await this.scopeNameRepository.replaceById(id, scopeName);
  }

  @del('/scope-names/{id}')
  @response(204, {
    description: 'ScopeName DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.scopeNameRepository.deleteById(id);
  }
}
