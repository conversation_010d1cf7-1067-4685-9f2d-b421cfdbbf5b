import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {QnIndicatorApproval} from '../models';
import {QnIndicatorApprovalRepository} from '../repositories';

export class QnIndicatorApprovalController {
  constructor(
    @repository(QnIndicatorApprovalRepository)
    public qnIndicatorApprovalRepository : QnIndicatorApprovalRepository,
  ) {}

  @post('/qn-indicator-approvals')
  @response(200, {
    description: 'QnIndicatorApproval model instance',
    content: {'application/json': {schema: getModelSchemaRef(QnIndicatorApproval)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QnIndicatorApproval, {
            title: 'NewQnIndicatorApproval',
            exclude: ['id'],
          }),
        },
      },
    })
    qnIndicatorApproval: Omit<QnIndicatorApproval, 'id'>,
  ): Promise<QnIndicatorApproval> {
    return this.qnIndicatorApprovalRepository.create(qnIndicatorApproval);
  }

  @get('/qn-indicator-approvals/count')
  @response(200, {
    description: 'QnIndicatorApproval model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QnIndicatorApproval) where?: Where<QnIndicatorApproval>,
  ): Promise<Count> {
    return this.qnIndicatorApprovalRepository.count(where);
  }

  @get('/qn-indicator-approvals')
  @response(200, {
    description: 'Array of QnIndicatorApproval model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QnIndicatorApproval, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QnIndicatorApproval) filter?: Filter<QnIndicatorApproval>,
  ): Promise<QnIndicatorApproval[]> {
    return this.qnIndicatorApprovalRepository.find(filter);
  }

  @patch('/qn-indicator-approvals')
  @response(200, {
    description: 'QnIndicatorApproval PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QnIndicatorApproval, {partial: true}),
        },
      },
    })
    qnIndicatorApproval: QnIndicatorApproval,
    @param.where(QnIndicatorApproval) where?: Where<QnIndicatorApproval>,
  ): Promise<Count> {
    return this.qnIndicatorApprovalRepository.updateAll(qnIndicatorApproval, where);
  }

  @get('/qn-indicator-approvals/{id}')
  @response(200, {
    description: 'QnIndicatorApproval model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QnIndicatorApproval, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QnIndicatorApproval, {exclude: 'where'}) filter?: FilterExcludingWhere<QnIndicatorApproval>
  ): Promise<QnIndicatorApproval> {
    return this.qnIndicatorApprovalRepository.findById(id, filter);
  }

  @patch('/qn-indicator-approvals/{id}')
  @response(204, {
    description: 'QnIndicatorApproval PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QnIndicatorApproval, {partial: true}),
        },
      },
    })
    qnIndicatorApproval: QnIndicatorApproval,
  ): Promise<void> {
    await this.qnIndicatorApprovalRepository.updateById(id, qnIndicatorApproval);
  }

  @put('/qn-indicator-approvals/{id}')
  @response(204, {
    description: 'QnIndicatorApproval PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() qnIndicatorApproval: QnIndicatorApproval,
  ): Promise<void> {
    await this.qnIndicatorApprovalRepository.replaceById(id, qnIndicatorApproval);
  }

  @del('/qn-indicator-approvals/{id}')
  @response(204, {
    description: 'QnIndicatorApproval DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.qnIndicatorApprovalRepository.deleteById(id);
  }
}
