import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  ValueChainSubmission,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileValueChainSubmissionController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/value-chain-submissions', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many ValueChainSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ValueChainSubmission)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ValueChainSubmission>,
  ): Promise<ValueChainSubmission[]> {
    return this.userProfileRepository.valueChainSubmissions(id).find(filter);
  }

  @post('/user-profiles/{id}/value-chain-submissions', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(ValueChainSubmission)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ValueChainSubmission, {
            title: 'NewValueChainSubmissionInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) valueChainSubmission: Omit<ValueChainSubmission, 'id'>,
  ): Promise<ValueChainSubmission> {
    return this.userProfileRepository.valueChainSubmissions(id).create(valueChainSubmission);
  }

  @patch('/user-profiles/{id}/value-chain-submissions', {
    responses: {
      '200': {
        description: 'UserProfile.ValueChainSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ValueChainSubmission, {partial: true}),
        },
      },
    })
    valueChainSubmission: Partial<ValueChainSubmission>,
    @param.query.object('where', getWhereSchemaFor(ValueChainSubmission)) where?: Where<ValueChainSubmission>,
  ): Promise<Count> {
    return this.userProfileRepository.valueChainSubmissions(id).patch(valueChainSubmission, where);
  }

  @del('/user-profiles/{id}/value-chain-submissions', {
    responses: {
      '200': {
        description: 'UserProfile.ValueChainSubmission DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(ValueChainSubmission)) where?: Where<ValueChainSubmission>,
  ): Promise<Count> {
    return this.userProfileRepository.valueChainSubmissions(id).delete(where);
  }
}
