import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewEfSubcategory4} from '../models';
import {NewEfSubcategory4Repository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfSubcategory4Controller {
  constructor(
    @repository(NewEfSubcategory4Repository)
    public newEfSubcategory4Repository : NewEfSubcategory4Repository,
  ) {}

  @post('/new-ef-subcategory4s')
  @response(200, {
    description: 'NewEfSubcategory4 model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEfSubcategory4)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory4, {
            title: 'NewNewEfSubcategory4',
            exclude: ['id'],
          }),
        },
      },
    })
    newEfSubcategory4: Omit<NewEfSubcategory4, 'id'>,
  ): Promise<NewEfSubcategory4> {
    return this.newEfSubcategory4Repository.create(newEfSubcategory4);
  }

  @get('/new-ef-subcategory4s/count')
  @response(200, {
    description: 'NewEfSubcategory4 model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEfSubcategory4) where?: Where<NewEfSubcategory4>,
  ): Promise<Count> {
    return this.newEfSubcategory4Repository.count(where);
  }

  @get('/new-ef-subcategory4s')
  @response(200, {
    description: 'Array of NewEfSubcategory4 model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEfSubcategory4, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEfSubcategory4) filter?: Filter<NewEfSubcategory4>,
  ): Promise<NewEfSubcategory4[]> {
    return this.newEfSubcategory4Repository.find(filter);
  }

  @patch('/new-ef-subcategory4s')
  @response(200, {
    description: 'NewEfSubcategory4 PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory4, {partial: true}),
        },
      },
    })
    newEfSubcategory4: NewEfSubcategory4,
    @param.where(NewEfSubcategory4) where?: Where<NewEfSubcategory4>,
  ): Promise<Count> {
    return this.newEfSubcategory4Repository.updateAll(newEfSubcategory4, where);
  }

  @get('/new-ef-subcategory4s/{id}')
  @response(200, {
    description: 'NewEfSubcategory4 model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEfSubcategory4, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEfSubcategory4, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEfSubcategory4>
  ): Promise<NewEfSubcategory4> {
    return this.newEfSubcategory4Repository.findById(id, filter);
  }

  @patch('/new-ef-subcategory4s/{id}')
  @response(204, {
    description: 'NewEfSubcategory4 PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory4, {partial: true}),
        },
      },
    })
    newEfSubcategory4: NewEfSubcategory4,
  ): Promise<void> {
    await this.newEfSubcategory4Repository.updateById(id, newEfSubcategory4);
  }

  @put('/new-ef-subcategory4s/{id}')
  @response(204, {
    description: 'NewEfSubcategory4 PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEfSubcategory4: NewEfSubcategory4,
  ): Promise<void> {
    await this.newEfSubcategory4Repository.replaceById(id, newEfSubcategory4);
  }

  @del('/new-ef-subcategory4s/{id}')
  @response(204, {
    description: 'NewEfSubcategory4 DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfSubcategory4Repository.deleteById(id);
  }
}
