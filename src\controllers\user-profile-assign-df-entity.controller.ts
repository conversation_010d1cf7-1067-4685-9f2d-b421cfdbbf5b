import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  AssignDfEntity,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileAssignDfEntityController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-df-entities', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDfEntity',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDfEntity)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDfEntity>,
  ): Promise<AssignDfEntity[]> {
    return this.userProfileRepository.assignDfEntities(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-df-entities', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDfEntity)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntity, {
            title: 'NewAssignDfEntityInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDfEntity: Omit<AssignDfEntity, 'id'>,
  ): Promise<AssignDfEntity> {
    return this.userProfileRepository.assignDfEntities(id).create(assignDfEntity);
  }

  @patch('/user-profiles/{id}/assign-df-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignDfEntity PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntity, {partial: true}),
        },
      },
    })
    assignDfEntity: Partial<AssignDfEntity>,
    @param.query.object('where', getWhereSchemaFor(AssignDfEntity)) where?: Where<AssignDfEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignDfEntities(id).patch(assignDfEntity, where);
  }

  @del('/user-profiles/{id}/assign-df-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignDfEntity DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignDfEntity)) where?: Where<AssignDfEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignDfEntities(id).delete(where);
  }
}
