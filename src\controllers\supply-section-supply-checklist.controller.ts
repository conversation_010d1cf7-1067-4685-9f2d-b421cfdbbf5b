import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SupplySection,
  SupplyChecklist,
} from '../models';
import {SupplySectionRepository} from '../repositories';

export class SupplySectionSupplyChecklistController {
  constructor(
    @repository(SupplySectionRepository) protected supplySectionRepository: SupplySectionRepository,
  ) { }

  @get('/supply-sections/{id}/supply-checklists', {
    responses: {
      '200': {
        description: 'Array of SupplySection has many SupplyChecklist',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplyChecklist)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SupplyChecklist>,
  ): Promise<SupplyChecklist[]> {
    return this.supplySectionRepository.supplyChecklists(id).find(filter);
  }

  @post('/supply-sections/{id}/supply-checklists', {
    responses: {
      '200': {
        description: 'SupplySection model instance',
        content: {'application/json': {schema: getModelSchemaRef(SupplyChecklist)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof SupplySection.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplyChecklist, {
            title: 'NewSupplyChecklistInSupplySection',
            exclude: ['id'],
            optional: ['supplySectionId']
          }),
        },
      },
    }) supplyChecklist: Omit<SupplyChecklist, 'id'>,
  ): Promise<SupplyChecklist> {
    return this.supplySectionRepository.supplyChecklists(id).create(supplyChecklist);
  }

  @patch('/supply-sections/{id}/supply-checklists', {
    responses: {
      '200': {
        description: 'SupplySection.SupplyChecklist PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplyChecklist, {partial: true}),
        },
      },
    })
    supplyChecklist: Partial<SupplyChecklist>,
    @param.query.object('where', getWhereSchemaFor(SupplyChecklist)) where?: Where<SupplyChecklist>,
  ): Promise<Count> {
    return this.supplySectionRepository.supplyChecklists(id).patch(supplyChecklist, where);
  }

  @del('/supply-sections/{id}/supply-checklists', {
    responses: {
      '200': {
        description: 'SupplySection.SupplyChecklist DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SupplyChecklist)) where?: Where<SupplyChecklist>,
  ): Promise<Count> {
    return this.supplySectionRepository.supplyChecklists(id).delete(where);
  }
}
