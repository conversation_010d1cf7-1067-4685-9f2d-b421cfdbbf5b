import {belongsTo, Entity, model, property} from '@loopback/repository';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';
import {VendorCode} from './vendor-code.model';

@model()
export class ValueChainSubmission extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;



  @property({
    type: 'number',
  })
  reviewed_by?: number;

  @property({
    type: 'string',
  })
  reviewed_on?: string;


  @property({
    type: 'number',
  })
  user_type?: number;
  @property({
    type: 'string',
  })
  vendorCode?: string;
  @property({
    type: 'number',
  })
  partnerType?: number;

  @property({
    type: 'number',
  })
  form_type?: number;

  @property({
    type: 'number',
  })
  locationId?: number;
  @property({
    type: 'number',
  })
  level?: number;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier0_id?: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier1_id?: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier2_id?: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier3_id?: number | null;

  @property({
    type: 'array',
    itemType: 'any',
  })
  reporting_period?: any[];

  @property({
    type: 'array',
    itemType: 'any',
  })
  response?: any[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  response2?: object[];

  @property({
    type: 'number',
  })
  type?: number;



  @property({
    type: 'any',
  })
  return_remarks?: any;


  @property({
    type: 'number',
  })
  edit?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  data1?: any[];

  @property({
    type: 'string',
  })
  submitted_on?: string;

  @property({
    type: 'string',
  })
  approver_modified_on?: string;
  @property({
    type: 'number',
  })
  approver_modified_by?: number;

  @property({
    type: 'string',
  })
  reviewer_modified_on?: string;
  @property({
    type: 'number',
  })
  reviewer_modified_by?: number;

  @property({
    type: 'string',
  })
  reporter_modified_on?: string;
  @property({
    type: 'number',
  })
  reporter_modified_by?: number;

  @property({
    type: 'string',
  })
  last_modified_on?: string;

  @property({
    type: 'number',
  })
  last_modified_by?: number;
  @property({
    type: 'number',
  })
  reject?: number;

  @property({
    type: 'string',
  })
  approved_on?: string;

  @property({
    type: 'string',
  })
  rejected_on?: string;

  @property({
    type: 'number',
  })
  frequency?: number;

  @property({
    type: 'number',
  })
  return_status?: number;

  @property({
    type: 'number',
  })
  approver_return?: number;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'number',
  })
  approved_by?: number;


  @property({
    type: 'number',
  })
  approve_return_by?: number;
  @property({
    type: 'string',
  })
  approve_return_on?: string;

  @property({
    type: 'boolean',
  })
  self?: boolean;

  @property({
    type: 'any',
  })
  entityUserAssId?: any;

  @property({
    type: 'any',
  })
  entityAssId?: any;

  @property({
    type: 'any',
  })
  documents?: any;

  @belongsTo(() => ConsolidateFormCollection)
  srfId: number;
  @property({
    type: 'number',
  })
  supplierId?: number;
  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => VendorCode)
  vendorId: number;

  constructor(data?: Partial<ValueChainSubmission>) {
    super(data);
  }
}

export interface ValueChainSubmissionRelations {
  // describe navigational properties here
}

export type ValueChainSubmissionWithRelations = ValueChainSubmission & ValueChainSubmissionRelations;
