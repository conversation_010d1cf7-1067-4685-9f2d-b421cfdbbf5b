import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignRfUsers, AssignRfUsersRelations} from '../models';

export class AssignRfUsersRepository extends DefaultCrudRepository<
  AssignRfUsers,
  typeof AssignRfUsers.prototype.id,
  AssignRfUsersRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AssignRfUsers, dataSource);
  }
}
