import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  ClientEfCategoryMapping,
  NewEfCategory,
} from '../models';
import {ClientEfCategoryMappingRepository} from '../repositories';

export class ClientEfCategoryMappingNewEfCategoryController {
  constructor(
    @repository(ClientEfCategoryMappingRepository)
    public clientEfCategoryMappingRepository: ClientEfCategoryMappingRepository,
  ) { }

  @get('/client-ef-category-mappings/{id}/new-ef-category', {
    responses: {
      '200': {
        description: 'NewEfCategory belonging to ClientEfCategoryMapping',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfCategory)},
          },
        },
      },
    },
  })
  async getNewEfCategory(
    @param.path.number('id') id: typeof ClientEfCategoryMapping.prototype.id,
  ): Promise<NewEfCategory> {
    return this.clientEfCategoryMappingRepository.efCategory(id);
  }
}
