import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignSrfUser} from '../models';
import {AssignSrfUserRepository} from '../repositories';

export class AssignSrfUserController {
  constructor(
    @repository(AssignSrfUserRepository)
    public assignSrfUserRepository: AssignSrfUserRepository,
  ) { }

  @post('/assign-srf-users')
  @response(200, {
    description: 'AssignSrfUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignSrfUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfUser, {
            title: 'NewAssignSrfUser',
            exclude: ['id'],
          }),
        },
      },
    })
    assignSrfUser: Omit<AssignSrfUser, 'id'>,
  ): Promise<AssignSrfUser> {
    return this.assignSrfUserRepository.create(assignSrfUser);
  }

  @get('/assign-srf-users/count')
  @response(200, {
    description: 'AssignSrfUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignSrfUser) where?: Where<AssignSrfUser>,
  ): Promise<Count> {
    return this.assignSrfUserRepository.count(where);
  }

  @get('/assign-srf-users')
  @response(200, {
    description: 'Array of AssignSrfUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignSrfUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignSrfUser) filter?: Filter<AssignSrfUser>,
  ): Promise<AssignSrfUser[]> {
    return this.assignSrfUserRepository.find(filter);
  }

  // @patch('/assign-srf-users')
  // @response(200, {
  //   description: 'AssignSrfUser PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignSrfUser, {partial: true}),
  //       },
  //     },
  //   })
  //   assignSrfUser: AssignSrfUser,
  //   @param.where(AssignSrfUser) where?: Where<AssignSrfUser>,
  // ): Promise<Count> {
  //   return this.assignSrfUserRepository.updateAll(assignSrfUser, where);
  // }

  @get('/assign-srf-users/{id}')
  @response(200, {
    description: 'AssignSrfUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignSrfUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignSrfUser, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignSrfUser>
  ): Promise<AssignSrfUser> {
    return this.assignSrfUserRepository.findById(id, filter);
  }

  @patch('/assign-srf-users/{id}')
  @response(204, {
    description: 'AssignSrfUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfUser, {partial: true}),
        },
      },
    })
    assignSrfUser: AssignSrfUser,
  ): Promise<void> {
    await this.assignSrfUserRepository.updateById(id, assignSrfUser);
  }

  @put('/assign-srf-users/{id}')
  @response(204, {
    description: 'AssignSrfUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignSrfUser: AssignSrfUser,
  ): Promise<void> {
    await this.assignSrfUserRepository.replaceById(id, assignSrfUser);
  }

  @del('/assign-srf-users/{id}')
  @response(204, {
    description: 'AssignSrfUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignSrfUserRepository.deleteById(id);
  }
}
