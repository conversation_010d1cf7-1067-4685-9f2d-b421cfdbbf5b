import {Entity, model, property} from '@loopback/repository';

@model()
export class AssignRfUsers extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  user_id?: number;

  @property({
    type: 'number',
  })
  rfid?: number;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'array',
    itemType: 'number',
  })
  reporter_ids?: number[];

  @property({
    type: 'number',
  })
  level?: number;

  @property({
    type: 'number',
  })
  coverage?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<AssignRfUsers>) {
    super(data);
  }
}

export interface AssignRfUsersRelations {
  // describe navigational properties here
}

export type AssignRfUsersWithRelations = AssignRfUsers & AssignRfUsersRelations;
