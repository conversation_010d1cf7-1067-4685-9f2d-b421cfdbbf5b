import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierSectionSubmission,
  AssessmentSubSection1,
} from '../models';
import {SupplierSectionSubmissionRepository} from '../repositories';

export class SupplierSectionSubmissionAssessmentSubSection1Controller {
  constructor(
    @repository(SupplierSectionSubmissionRepository)
    public supplierSectionSubmissionRepository: SupplierSectionSubmissionRepository,
  ) { }

  @get('/supplier-section-submissions/{id}/assessment-sub-section1', {
    responses: {
      '200': {
        description: 'AssessmentSubSection1 belonging to SupplierSectionSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssessmentSubSection1)},
          },
        },
      },
    },
  })
  async getAssessmentSubSection1(
    @param.path.string('id') id: typeof SupplierSectionSubmission.prototype.id,
  ): Promise<AssessmentSubSection1> {
    return this.supplierSectionSubmissionRepository.assessmentSubSection1(id);
  }
}
