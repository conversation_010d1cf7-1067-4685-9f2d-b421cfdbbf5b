import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ScopeThree} from '../models';
import {ScopeThreeRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ScopeThreeController {
  constructor(
    @repository(ScopeThreeRepository)
    public scopeThreeRepository : ScopeThreeRepository,
  ) {}

  @post('/scope-threes')
  @response(200, {
    description: 'ScopeThree model instance',
    content: {'application/json': {schema: getModelSchemaRef(ScopeThree)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeThree, {
            title: 'NewScopeThree',
            exclude: ['id'],
          }),
        },
      },
    })
    scopeThree: Omit<ScopeThree, 'id'>,
  ): Promise<ScopeThree> {
    return this.scopeThreeRepository.create(scopeThree);
  }

  @get('/scope-threes/count')
  @response(200, {
    description: 'ScopeThree model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ScopeThree) where?: Where<ScopeThree>,
  ): Promise<Count> {
    return this.scopeThreeRepository.count(where);
  }

  @get('/scope-threes')
  @response(200, {
    description: 'Array of ScopeThree model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ScopeThree, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ScopeThree) filter?: Filter<ScopeThree>,
  ): Promise<ScopeThree[]> {
    return this.scopeThreeRepository.find(filter);
  }

  // @patch('/scope-threes')
  // @response(200, {
  //   description: 'ScopeThree PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ScopeThree, {partial: true}),
  //       },
  //     },
  //   })
  //   scopeThree: ScopeThree,
  //   @param.where(ScopeThree) where?: Where<ScopeThree>,
  // ): Promise<Count> {
  //   return this.scopeThreeRepository.updateAll(scopeThree, where);
  // }

  @get('/scope-threes/{id}')
  @response(200, {
    description: 'ScopeThree model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ScopeThree, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ScopeThree, {exclude: 'where'}) filter?: FilterExcludingWhere<ScopeThree>
  ): Promise<ScopeThree> {
    return this.scopeThreeRepository.findById(id, filter);
  }

  @patch('/scope-threes/{id}')
  @response(204, {
    description: 'ScopeThree PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeThree, {partial: true}),
        },
      },
    })
    scopeThree: ScopeThree,
  ): Promise<void> {
    await this.scopeThreeRepository.updateById(id, scopeThree);
  }

  @put('/scope-threes/{id}')
  @response(204, {
    description: 'ScopeThree PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() scopeThree: ScopeThree,
  ): Promise<void> {
    await this.scopeThreeRepository.replaceById(id, scopeThree);
  }

  @del('/scope-threes/{id}')
  @response(204, {
    description: 'ScopeThree DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.scopeThreeRepository.deleteById(id);
  }
}
