import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ApplicationList, ApplicationListRelations, ApplicationRoles} from '../models';
import {ApplicationRolesRepository} from './application-roles.repository';

export class ApplicationListRepository extends DefaultCrudRepository<
  ApplicationList,
  typeof ApplicationList.prototype.id,
  ApplicationListRelations
> {

  public readonly applicationRoles: HasManyRepositoryFactory<ApplicationRoles, typeof ApplicationList.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ApplicationRolesRepository') protected applicationRolesRepositoryGetter: Getter<ApplicationRolesRepository>,
  ) {
    super(ApplicationList, dataSource);
    this.applicationRoles = this.createHasManyRepositoryFactoryFor('applicationRoles', applicationRolesRepositoryGetter,);
    this.registerInclusionResolver('applicationRoles', this.applicationRoles.inclusionResolver);
  }
}
