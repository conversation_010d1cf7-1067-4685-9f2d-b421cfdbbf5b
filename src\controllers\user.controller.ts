// Copyright IBM Corp. 2020. All Rights Reserved.
// Node module: @loopback/example-todo-jwt
// This file is licensed under the MIT License.
// License text available at https://opensource.org/licenses/MIT

import {TokenService} from '@loopback/authentication';
import {
  Credentials,
  MyUserService,
  OPERATION_SECURITY_SPEC,
  TokenServiceBindings,
  User,
  UserCredentialsRepository,
  UserRepository,
  UserServiceBindings,
} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {model, property, repository} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  post,
  put,
  requestBody,
  response,
  SchemaObject,
} from '@loopback/rest';
import {createTransport} from 'nodemailer';
import {SqsService} from '../services/sqs-service.service';

import {SecurityBindings, securityId, UserProfile} from '@loopback/security';
import {CognitoIdentityServiceProvider} from 'aws-sdk';
import axios from 'axios';
import {genSalt, hash} from 'bcryptjs';
import _ from 'lodash';
import {DateTime} from 'luxon';
import {v4 as uuidv4} from 'uuid';
import {
  AssignDcfEntityUserRepository,
  AssignQlEntityRepository,
  AssignQlEntityUserRepository,
  DealerAssessmentAssignmentRepository,
  IndicatorApproverAssignmentRepository,
  SupplierAssessmentAssignmentRepository,
  UserProfileRepository,
  UserRoleAuthorizationRepository,
  VendorCodeRepository
} from '../repositories';
const cognito = new CognitoIdentityServiceProvider({
  region: process.env.AWS_REGION,
  accessKeyId: process.env.AWS_COGNITO_ACCESS_KEY,
  secretAccessKey: process.env.AWS_COGNITO_SECRET_KEY
})

const MailService = createTransport({
  port: 465,               // true for 465, false for other ports
  host: "smtp.gmail.com",
  auth: {
    user: '<EMAIL>',
    pass: 'dgwbhnasqxxgnxmb',
  },
  secure: true,
})
@model()
export class NewUserRequest extends User {
  @property({
    type: 'string',
    required: true,
  })
  password: string;

  @property({
    type: 'string',
    required: true,
  })
  company: string;


}

const CredentialsSchema: SchemaObject = {
  type: 'object',
  required: ['email', 'password'],
  properties: {
    email: {
      type: 'string',
      format: 'email',
    },
    password: {
      type: 'string',
      minLength: 8,
    },
  },
};

export const CredentialsRequestBody = {
  description: 'The input of login function',
  required: true,
  content: {
    'application/json': {schema: CredentialsSchema},
  },
};

type ResetPasswordInit = {
  email: string
}
type keyAndPassword = {
  resetKey: string,
  password: string,
  confirmPassword: string
}
export class UserController {
  constructor(
    @inject(TokenServiceBindings.TOKEN_SERVICE)
    public jwtService: TokenService,
    @inject(UserServiceBindings.USER_SERVICE)
    public userService: MyUserService,
    @inject(SecurityBindings.USER, {optional: true})
    public user: UserProfile,
    @repository(UserCredentialsRepository) protected userCredentialsRepository: UserCredentialsRepository,
    @repository(UserRepository) protected userRepository: UserRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(VendorCodeRepository)
    public vendorCodeRepository: VendorCodeRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @repository(AssignDcfEntityUserRepository)
    public assignDcfEntityUserRepository: AssignDcfEntityUserRepository,
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
    @repository(DealerAssessmentAssignmentRepository)
    public dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @repository(IndicatorApproverAssignmentRepository)
    public indicatorApproverAssignmentRepository: IndicatorApproverAssignmentRepository,

  ) { }

  @post('/users/login', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },
            },
          },
        },
      },
    },
  })
  async login(
    @requestBody(CredentialsRequestBody) credentials: Credentials,
  ): Promise<{token: string}> {
    // ensure the user exists, and the password is correct
    const user = await this.userService.verifyCredentials(credentials);
    // convert a User object into a UserProfile object (reduced set of properties)
    const userProfile = this.userService.convertToUserProfile(user);

    // create a JSON Web Token based on the user profile
    const token = await this.jwtService.generateToken(userProfile);
    return {token};
  }
  @post('/id/login', {
    responses: {
      '200': {
        description: 'Token',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                token: {
                  type: 'string',
                },
              },

            },
          },
        },
      },
    },
  })
  async Idlogin(
    @requestBody({
      description: 'The input of login function',
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            required: ['supplierCode', 'password'],
            properties: {
              email: {
                type: 'string',
                nullable: false
              },
              password: {
                type: 'string',
                minLength: 8,
              },
            },
          }
        },
      },
    }) credentials: {vendorCode: string, password: string},
  ): Promise<{token: string}> {

    try {
      const vendorDetail = await this.vendorCodeRepository.findOne({where: {code: credentials.vendorCode}})
      if (vendorDetail) {
        const id = await this.userProfileRepository.findById(vendorDetail?.userProfileId)
        if (!id?.userId) {
          throw new Error('Invalid User Id');
        }
        const userDetail = await this.userRepository.findOne({where: {id: id?.userId}})

        if (!userDetail?.email) {
          throw new Error('Invalid User Email Id ');
        }
        console.log(userDetail.email, credentials.password)
        const user = await this.userService.verifyCredentials({email: userDetail.email, password: credentials.password});

        // convert a User object into a UserProfile object (reduced set of properties)
        const userProfile = this.userService.convertToUserProfile(user);

        // create a JSON Web Token based on the user profile
        const token = await this.jwtService.generateToken(userProfile);
        return {token};
      } else {
        throw new Error('Vendor Code Not Found');

      }

    } catch (e) {
      console.log(e)
      throw new Error('Something went wrong');
    }
    // ensure the user exists, and the password is correct

  }
  @get('/whoAmI', {
    responses: {
      '200': {
        description: 'Return current user',
        content: {
          'application/json': {
            schema: {
              type: 'string',
            },
          },
        },
      },
    },
  })
  async whoAmI(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
  ): Promise<string> {
    return currentUserProfile[securityId];
  }

  @post('/signup', {
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })
  async signUp(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewUserRequest, {
            title: 'NewUser',
          }),
        },
      },
    })
    newUserRequest: NewUserRequest,
  ): Promise<User> {

    const password = await hash(newUserRequest.password, await genSalt());

    const savedUser = await this.userRepository.create(
      _.omit(newUserRequest, ['password', 'company']),

    );

    await this.userRepository.userCredentials(savedUser.id).create({password});
    await this.userProfileRepository.create({company: newUserRequest.company, userId: savedUser.id})


    return savedUser;
  }

  @post('/reset-password/init')
  async resetPasswordInit(
    @requestBody() resetPasswordInit: ResetPasswordInit,
  ): Promise<string> {
    // checks whether email is valid as per regex pattern provided
    const email = await this.validateEmail(resetPasswordInit.email);

    // At this point we are dealing with valid email.
    // Lets check whether there is an associated account
    const foundUser = await this.userRepository.findOne({
      where: {email},
    });

    // No account found
    if (!foundUser) {
      throw new HttpErrors.NotFound(
        'No account associated with the provided email address.',
      );
    }

    // We generate unique reset key to associate with reset request
    foundUser.verificationToken = uuidv4();

    try {
      // Updates the user to store their reset key with error handling
      await this.userRepository.updateById(foundUser.id, foundUser);
    } catch (e) {
      return e;
    }
    // Send an email to the user's email address
    const mailData = {
      from: '<EMAIL>',  // sender address
      to: email,   // list of receivers
      subject: '',
      text: '',
      html: ` <div>
      <p>Hi there,</p>
      <p style="color: red;">We received a request to reset the password for your account</p>
      <p>To reset your password click on the link provided below</p>
      <a href="https://user.navigos.eisqr.com/reset-password-finish?resetKey=${foundUser.verificationToken}">Reset your password link</a>
      <p>If you didnt request to reset your password, please ignore this email or reset your password to protect your account.</p>
      <p>Thanks</p></div>`,
    };
    this.sqsService.sendEmail(email, 'Eisqr Account Password Reset', mailData.html, []).then((info) => {

      return true
    }).catch((err) => {

      return false
    })


    if (true) {
      return 'An email with password reset instructions has been sent to the provided email';
    }
    // throw new HttpErrors.InternalServerError(
    //   'Error sending reset password email',
    // );
  }
  async validateEmail(email: string): Promise<string> {
    const emailRegPattern = /\S+@\S+\.\S+/;
    if (!emailRegPattern.test(email)) {
      throw new HttpErrors.UnprocessableEntity('Invalid email address');
    }
    return email;
  }

  @put('/reset-password/finish')
  async resetPasswordFinish(
    @requestBody() keyAndPassword: keyAndPassword,
  ): Promise<string> {
    // Checks whether password and reset key meet minimum security requirements
    const {resetKey, password} = await this.validateKeyPassword(keyAndPassword);

    // Search for a user using reset key
    const foundUser = await this.userRepository.findOne({
      where: {verificationToken: resetKey},
    });

    // No user account found
    if (!foundUser) {
      throw new HttpErrors.NotFound(
        'No associated account for the provided reset key',
      );
    }

    // Encrypt password to avoid storing it as plain text
    const passwordHash = await hash(password, await genSalt());

    try {
      // Update user password with the newly provided password
      await this.userRepository
        .userCredentials(foundUser.id)
        .patch({password: passwordHash});

      // Remove reset key from database its no longer valid
      foundUser.verificationToken = '';

      // Update the user removing the reset key
      await this.userRepository.updateById(foundUser.id, foundUser);
    } catch (e) {
      return e;
    }

    return 'Password reset request completed successfully';
  }
  async validateKeyPassword(keyAndPassword: keyAndPassword): Promise<keyAndPassword> {
    if (!keyAndPassword.password || keyAndPassword.password.length < 8) {
      throw new HttpErrors.UnprocessableEntity(
        'Password must be minimum of 8 characters',
      );
    }

    if (keyAndPassword.password !== keyAndPassword.confirmPassword) {
      throw new HttpErrors.UnprocessableEntity(
        'Password and confirmation password do not match',
      );
    }

    if (
      keyAndPassword.resetKey.length === 0 ||
      keyAndPassword.resetKey.trim() === ''
    ) {
      throw new HttpErrors.UnprocessableEntity('Reset key is mandatory');
    }

    return keyAndPassword;
  }

  @get('/internal/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByInternal(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');

    console.log(formData)
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      customSearch = `"displayName:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }


    console.log(url)

    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: {id: string; mail: string;}) => {
        const whiteListed = users.find(b => b.email === user.mail);
        if (whiteListed) {

          return {...user, country: whiteListed.country, status: 'active'};
        }
        return {...user, status: 'inactive'};
      });
      return {data: updatedUsers, country: '', recordsTotal: usersResponse.data['@odata.count']}
    }
    catch (e) {
      console.log(e)
    }



  }

  @get('/india/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByIndia(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.INDIA_AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.INDIA_AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.INDIA_AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      customSearch = `"displayName:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }


    console.log(url)

    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: {id: string; mail: string;}) => {
        const whiteListed = users.find(b => b.email === user.mail);
        if (whiteListed) {
          return {...user, country: whiteListed.country, status: 'active'};
        }
        return {...user, country: '', status: 'inactive'};
      });
      return {data: updatedUsers, recordsTotal: usersResponse.data['@odata.count']}
    }
    catch (e) {
      console.log(e)
    }



  }
  @get('/uk/users/', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByUK(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.UK_AZURE_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.UK_AZURE_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.UK_AZURE_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      customSearch = `"displayName:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }



    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: {id: string; mail: string;}) => {
        const whiteListed = users.find(b => b.email === user.mail);
        if (whiteListed) {
          return {...user, country: whiteListed.country, status: 'active'};
        }
        return {...user, country: '', status: 'inactive'};
      });
      return {data: updatedUsers, recordsTotal: usersResponse.data['@odata.count']}
    }
    catch (e) {
      console.log(e)
    }



  }
  @get('/tvs/external', {
    security: OPERATION_SECURITY_SPEC,
    responses: {
      '200': {
        description: 'User',
        content: {
          'application/json': {
            schema: {
              'x-ts-type': User,
            },
          },
        },
      },
    },
  })


  async findByTVSExternal(
    @param.query.string('search') search: string,
    @param.query.number('length') limit: number,
    @param.query.number('start') offset: number
  ): Promise<any> {
    const formData = new URLSearchParams();
    formData.append('grant_type', 'client_credentials');
    formData.append('client_id', `${process.env.TVS_CLIENT_ID}`);
    formData.append('client_secret', `${process.env.TVS_CLIENT_SECRET}`);
    formData.append('resource', 'https://graph.microsoft.com/');
    const accessResponse = await axios.post(`https://login.microsoftonline.com/${process.env.TVS_TENANT_ID}/oauth2/token`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'

      }
    })

    console.log(search)
    let url = '';
    let customSearch = ''
    if (search) {
      const customSearch = `"displayName:${search}" OR "mail:${search}"`;
      url = `https://graph.microsoft.com/v1.0/users?$top=100&$search=${customSearch}&$count=true`;
    } else {
      customSearch = '';
      url = `https://graph.microsoft.com/v1.0/users?$top=50&$count=true`;
    }


    console.log(url)

    try {
      const usersResponse = await axios.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessResponse.data.access_token}`,
          'consistencylevel': 'eventual'
        }
      })
      const users = await this.userRepository.find();
      const updatedUsers = usersResponse.data.value.map((user: {id: string; mail: string;}) => {
        const whiteListed = users.find(b => b.email === user.mail);
        if (whiteListed) {
          return {...user, country: whiteListed.country, status: 'active'};
        }
        return {...user, country: '', status: 'inactive'};
      });
      return {data: updatedUsers, recordsTotal: usersResponse.data['@odata.count']}
    }
    catch (e) {
      console.log(e)
    }



  }

  @post('/delete-live-client-user-by-userids')
  async deleteUsersByuserId(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              ids: {
                type: 'array',
                items: {
                  type: 'string',
                },
              },
              date: {
                type: 'string'
              },
            },
            required: ['ids', 'date'],
          },
        },
      },
    })
    requestBody: {ids: string[], date: string},
  ): Promise<any> {
    const {ids, date} = requestBody;
    const dt = DateTime.utc().toFormat('yyyyLLLdd')

    try {
      if (date === dt) {

        const deleteResult = await this.userRepository.deleteAll({
          id: {inq: ids},
        });

        const deleteResult2 = await this.userProfileRepository.deleteAll({
          userId: {inq: ids},
        });
        const deleteResult3 = await this.userCredentialsRepository.deleteAll({
          userId: {inq: ids},
        });
        return {result: (deleteResult.count === deleteResult2.count && deleteResult2.count === deleteResult3.count), user: deleteResult, userProfile: deleteResult2, userCredentials: deleteResult3};
      } else {
        throw new HttpErrors.BadRequest('Invalidation');
      }


    } catch (e) {
      throw new HttpErrors.BadRequest(e);
    }

  }
  @post('/delete-individual-user-by-userids')
  async deleteIndividualUsersByuserId(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              user_id: {
                type: 'string'
              },
              date: {
                type: 'string'
              },
            },
            required: ['user_id', 'date'],
          },
        },
      },
    })
    requestBody: {user_id: string, date: string},
  ): Promise<any> {
    const {user_id, date} = requestBody;
    const dt = DateTime.utc().toFormat('yyyyLLLdd')

    try {
      if (date === dt) {

        const deleteResult = await this.userRepository.deleteAll({
          id: user_id
        });

        const deleteResult2 = await this.userProfileRepository.deleteAll({
          userId: user_id
        });
        const deleteResult3 = await this.userCredentialsRepository.deleteAll({
          userId: user_id,
        });
        return {result: (deleteResult.count === deleteResult2.count && deleteResult2.count === deleteResult3.count), user: deleteResult, userProfile: deleteResult2, userCredentials: deleteResult3};
      } else {
        throw new HttpErrors.BadRequest('Invalidation');
      }


    } catch (e) {
      throw new HttpErrors.BadRequest(e);
    }

  }

  @post('/delete-complete-user')
  async deleteIndividualUserByUPID(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {
                type: 'number'
              },
              requestUser: {
                type: 'number'
              }
            },
            required: ['userProfileId', 'requestUser'],
          },
        },
      },
    })
    requestBody: {userProfileId: number, requestUser: number},
  ): Promise<any> {
    const {userProfileId, requestUser} = requestBody;
    const requestedUser = await this.userProfileRepository.findOne({where: {id: requestUser}})
    const deleteResult2 = await this.userProfileRepository.findOne({where: {id: userProfileId}})
    try {
      console.log(requestedUser?.clientId, deleteResult2?.clientId, requestedUser?.id)
      if (((requestedUser?.clientId === deleteResult2?.clientId) || (requestedUser?.id === deleteResult2?.clientId)) && deleteResult2?.role !== 'clientadmin' && deleteResult2?.role !== 'eisqradmin' && deleteResult2?.userId) {

        const creds = await this.userRepository.userCredentials(deleteResult2.userId).get().catch(() => null);

        if (creds) {
          await this.userRepository.userCredentials(deleteResult2.userId).delete();

        }
        await this.userProfileRepository.deleteById(userProfileId);
        await this.userRepository.deleteById(deleteResult2.userId);

        return {result: true, message: "User Deleted Successfully"};
      } else {
        return {result: false, message: "Issue in deleting user"};
      }


    } catch (e) {
      return {result: false, message: e};
    }

  }

  @post('/block-user')
  @response(200, {
    description: 'Block user and remove all assignments',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            removedAssignments: {
              type: 'object',
              properties: {
                assignDcfEntityUser: {type: 'number'},
                assignQlEntity: {type: 'number'},
                assignQlEntityUser: {type: 'number'},
                supplierAssessmentAssignment: {type: 'number'},
                dealerAssessmentAssignment: {type: 'number'},
                userRoleAuthorization: {type: 'number'},
                indicatorApproverAssignment: {type: 'number'}
              }
            }
          }
        }
      }
    }
  })
  async blockUser(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              clientId: {type: 'number'},
              userProfileId: {type: 'number'},
              requesterId: {type: 'number'}
            },
            required: ['clientId', 'userProfileId', 'requesterId']
          }
        }
      }
    })
    data: {clientId: number; userProfileId: number; requesterId: number}
  ): Promise<{status: boolean; message: string; removedAssignments: any}> {
    const {clientId, userProfileId, requesterId} = data;

    try {
      // Step 1: Get client information by userProfileId (clientId is actually a userProfileId)
      const clientProfile = await this.userProfileRepository.findById(clientId);
      if (!clientProfile) {
        return {
          status: false,
          message: 'Client not found',
          removedAssignments: {}
        };
      }

      // Step 2: Block the user by updating their status
      await this.userProfileRepository.updateById(userProfileId, {
        active: false,
        blocked: true
      });

      const removedAssignments = {
        assignDcfEntityUser: 0,
        assignQlEntity: 0,
        assignQlEntityUser: 0,
        supplierAssessmentAssignment: 0,
        dealerAssessmentAssignment: 0,
        userRoleAuthorization: 0,
        indicatorApproverAssignment: 0
      };

      // Step 3: Remove from AssignDcfEntityUser (reporter_ids, reviewer_ids)
      const dcfEntityUsers = await this.assignDcfEntityUserRepository.execute(
        `SELECT * FROM AssignDcfEntityUser
         WHERE (reporter_ids IS NOT NULL AND JSON_LENGTH(reporter_ids) > 0 AND JSON_CONTAINS(reporter_ids, ?, '$'))
            OR (reviewer_ids IS NOT NULL AND JSON_LENGTH(reviewer_ids) > 0 AND JSON_CONTAINS(reviewer_ids, ?, '$'))`,
        [userProfileId, userProfileId]
      );

      for (const assignment of dcfEntityUsers as any[]) {
        const reporterIds = assignment.reporter_ids ? JSON.parse(assignment.reporter_ids) : [];
        const reviewerIds = assignment.reviewer_ids ? JSON.parse(assignment.reviewer_ids) : [];

        const updatedReporterIds = reporterIds.filter((id: number) => id !== userProfileId);
        const updatedReviewerIds = reviewerIds.filter((id: number) => id !== userProfileId);

        // TODO: Uncomment for actual update
        // await this.assignDcfEntityUserRepository.updateById(assignment.id, {
        //   reporter_ids: updatedReporterIds,
        //   reviewer_ids: updatedReviewerIds
        // });
        removedAssignments.assignDcfEntityUser++;
      }

      // Step 4: Remove from AssignQlEntity (consolidator_ids)
      const qlEntities = await this.assignQlEntityRepository.execute(
        `SELECT * FROM AssignQlEntity
         WHERE consolidator_ids IS NOT NULL
           AND JSON_LENGTH(consolidator_ids) > 0
           AND JSON_CONTAINS(consolidator_ids, ?, '$')`,
        [userProfileId]
      );

      for (const assignment of qlEntities as any[]) {
        const consolidatorIds = assignment.consolidator_ids ? JSON.parse(assignment.consolidator_ids) : [];
        const updatedConsolidatorIds = consolidatorIds.filter((id: number) => id !== userProfileId);

        await this.assignQlEntityRepository.updateById(assignment.id, {
          consolidator_ids: updatedConsolidatorIds
        });
        removedAssignments.assignQlEntity++;
      }

      // Step 5: Remove from AssignQlEntityUser (reporter_ids)
      const qlEntityUsers = await this.assignQlEntityUserRepository.execute(
        `SELECT * FROM AssignQlEntityUser
         WHERE reporter_ids IS NOT NULL
           AND JSON_LENGTH(reporter_ids) > 0
           AND JSON_CONTAINS(reporter_ids, ?, '$')`,
        [userProfileId]
      );

      for (const assignment of qlEntityUsers as any[]) {
        const reporterIds = assignment.reporter_ids ? JSON.parse(assignment.reporter_ids) : [];
        const updatedReporterIds = reporterIds.filter((id: number) => id !== userProfileId);

        await this.assignQlEntityUserRepository.updateById(assignment.id, {
          reporter_ids: updatedReporterIds
        });
        removedAssignments.assignQlEntityUser++;
      }

      // Step 6: Remove from SupplierAssessmentAssignment
      const supplierAssignments = await this.supplierAssessmentAssignmentRepository.execute(
        `SELECT * FROM SupplierAssessmentAssignment
         WHERE (reviewer_ids IS NOT NULL AND JSON_LENGTH(reviewer_ids) > 0 AND JSON_CONTAINS(reviewer_ids, ?, '$'))
            OR (auditor_ids IS NOT NULL AND JSON_LENGTH(auditor_ids) > 0 AND JSON_CONTAINS(auditor_ids, ?, '$'))
            OR (group1 IS NOT NULL AND JSON_EXTRACT(group1, '$.assessors') IS NOT NULL AND JSON_LENGTH(JSON_EXTRACT(group1, '$.assessors')) > 0 AND JSON_CONTAINS(JSON_EXTRACT(group1, '$.assessors'), ?, '$'))
            OR (group2 IS NOT NULL AND JSON_EXTRACT(group2, '$.assessors') IS NOT NULL AND JSON_LENGTH(JSON_EXTRACT(group2, '$.assessors')) > 0 AND JSON_CONTAINS(JSON_EXTRACT(group2, '$.assessors'), ?, '$'))
            OR (group3 IS NOT NULL AND JSON_EXTRACT(group3, '$.assessors') IS NOT NULL AND JSON_LENGTH(JSON_EXTRACT(group3, '$.assessors')) > 0 AND JSON_CONTAINS(JSON_EXTRACT(group3, '$.assessors'), ?, '$'))
            OR (group4 IS NOT NULL AND JSON_EXTRACT(group4, '$.assessors') IS NOT NULL AND JSON_LENGTH(JSON_EXTRACT(group4, '$.assessors')) > 0 AND JSON_CONTAINS(JSON_EXTRACT(group4, '$.assessors'), ?, '$'))`,
        [
          userProfileId, userProfileId,
          userProfileId, userProfileId,
          userProfileId, userProfileId
        ]
      );

      for (const assignment of supplierAssignments as any[]) {
        const reviewerIds = assignment.reviewer_ids ? JSON.parse(assignment.reviewer_ids) : [];
        const auditorIds = assignment.auditor_ids ? JSON.parse(assignment.auditor_ids) : [];

        const updatedReviewerIds = reviewerIds.filter((id: number) => id !== userProfileId);
        const updatedAuditorIds = auditorIds.filter((id: number) => id !== userProfileId);

        // Update group assessors
        const group1 = assignment.group1 ? JSON.parse(assignment.group1) : null;
        const group2 = assignment.group2 ? JSON.parse(assignment.group2) : null;
        const group3 = assignment.group3 ? JSON.parse(assignment.group3) : null;
        const group4 = assignment.group4 ? JSON.parse(assignment.group4) : null;

        const updatedGroup1 = group1 ? {
          ...group1,
          assessors: (group1.assessors || []).filter((id: number) => id !== userProfileId)
        } : group1;

        const updatedGroup2 = group2 ? {
          ...group2,
          assessors: (group2.assessors || []).filter((id: number) => id !== userProfileId)
        } : group2;

        const updatedGroup3 = group3 ? {
          ...group3,
          assessors: (group3.assessors || []).filter((id: number) => id !== userProfileId)
        } : group3;

        const updatedGroup4 = group4 ? {
          ...group4,
          assessors: (group4.assessors || []).filter((id: number) => id !== userProfileId)
        } : group4;

        await this.supplierAssessmentAssignmentRepository.updateById(assignment.id, {
          reviewer_ids: updatedReviewerIds,
          auditor_ids: updatedAuditorIds,
          group1: updatedGroup1,
          group2: updatedGroup2,
          group3: updatedGroup3,
          group4: updatedGroup4
        });
        removedAssignments.supplierAssessmentAssignment++;
      }

      // Step 7: Remove from DealerAssessmentAssignment (assessors)
      const dealerAssignments = await this.dealerAssessmentAssignmentRepository.execute(
        `SELECT * FROM DealerAssessmentAssignment
         WHERE assessors IS NOT NULL
           AND JSON_LENGTH(assessors) > 0
           AND JSON_SEARCH(assessors, 'one', ?, NULL, '$[*].id') IS NOT NULL`,
        [userProfileId]
      );

      for (const assignment of dealerAssignments as any[]) {
        const assessors = assignment.assessors ? JSON.parse(assignment.assessors) : [];
        const updatedAssessors = assessors.filter((assessor: any) =>
          assessor && assessor.id !== userProfileId
        );

        await this.dealerAssessmentAssignmentRepository.updateById(assignment.id, {
          assessors: updatedAssessors
        });
        removedAssignments.dealerAssessmentAssignment++;
      }

      // Step 8: Delete all UserRoleAuthorization rows by matching user_id
      const deletedRoles = await this.userRoleAuthorizationRepository.deleteAll({
        user_id: userProfileId
      });
      removedAssignments.userRoleAuthorization = deletedRoles.count || 0;

      // Step 9: Remove from IndicatorApproverAssignment (responsibility)
      const indicatorAssignments = await this.indicatorApproverAssignmentRepository.execute(
        `SELECT * FROM IndicatorApproverAssignment
         WHERE responsibility IS NOT NULL
           AND JSON_LENGTH(responsibility) > 0
           AND JSON_CONTAINS(responsibility, ?, '$')`,
        [userProfileId]
      );

      for (const assignment of indicatorAssignments as any[]) {
        const responsibility = assignment.responsibility ? JSON.parse(assignment.responsibility) : [];
        const updatedResponsibility = responsibility.filter((id: number) => id !== userProfileId);

        await this.indicatorApproverAssignmentRepository.updateById(assignment.id, {
          responsibility: updatedResponsibility
        });
        removedAssignments.indicatorApproverAssignment++;
      }

      return {
        status: true,
        message: `User ${userProfileId} has been successfully blocked and removed from assignments.`,
        removedAssignments
      };

    } catch (error) {
      console.error('Error blocking user:', error);
      return {
        status: false,
        message: `Failed to block user: ${error instanceof Error ? error.message : 'Unknown error'}`,
        removedAssignments: {}
      };
    }
  }
}
