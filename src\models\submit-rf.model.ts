import {Entity, model, property} from '@loopback/repository';

@model()
export class SubmitRf extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  rfid?: number;

  @property({
    type: 'array',
    itemType: 'number',
  })
  framework?: number[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  response?: object[];

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  edit?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  data1?: any[];

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  data2?: string;

  @property({
    type: 'number',
  })
  categoryId?: number;

  @property({
    type: 'number',
  })
  indicatorId?: number;

  @property({
    type: 'number',
  })
  topicId?: number;

  @property({
    type: 'number',
  })
  reject?: number;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<SubmitRf>) {
    super(data);
  }
}

export interface SubmitRfRelations {
  // describe navigational properties here
}

export type SubmitRfWithRelations = SubmitRf & SubmitRfRelations;
