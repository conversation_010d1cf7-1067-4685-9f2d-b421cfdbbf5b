import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewCategory} from '../models';
import {NewCategoryRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewCategoryController {
  constructor(
    @repository(NewCategoryRepository)
    public newCategoryRepository : NewCategoryRepository,
  ) {}

  @post('/new-categories')
  @response(200, {
    description: 'NewCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewCategory, {
            title: 'NewNewCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    newCategory: Omit<NewCategory, 'id'>,
  ): Promise<NewCategory> {
    const category = await this.newCategoryRepository.create(newCategory);
    const id = category.id
    const suffix = newCategory.suffix + id;
    category.suffix = suffix;
    await this.newCategoryRepository.updateById(id, {suffix: suffix});
    return category;
  }

  @get('/new-categories/count')
  @response(200, {
    description: 'NewCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewCategory) where?: Where<NewCategory>,
  ): Promise<Count> {
    return this.newCategoryRepository.count(where);
  }

  @get('/new-categories')
  @response(200, {
    description: 'Array of NewCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewCategory) filter?: Filter<NewCategory>,
  ): Promise<NewCategory[]> {
    return this.newCategoryRepository.find(filter);
  }

  // @patch('/new-categories')
  // @response(200, {
  //   description: 'NewCategory PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewCategory, {partial: true}),
  //       },
  //     },
  //   })
  //   newCategory: NewCategory,
  //   @param.where(NewCategory) where?: Where<NewCategory>,
  // ): Promise<Count> {
  //   return this.newCategoryRepository.updateAll(newCategory, where);
  // }

  @patch('/new-categories')
  @response(200, {
    description: 'NewCategory PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            properties: {
              newCategorys: {

                type: 'array',
                items: getModelSchemaRef(NewCategory, { partial: true }),
              },
            },
          },
        },
      },
    })
    newCategorys: NewCategory[],
    @param.where(NewCategory) where?: Where<NewCategory>,
  ): Promise<Count> {
    const updatePromises = newCategorys.map((newCategory) => {
      return this.newCategoryRepository.updateById(newCategory.id, {order: newCategory.order});
    });

    await Promise.all(updatePromises);

    const totalCount = newCategorys.length;
    return { count: totalCount };
  }



  @get('/new-categories/{id}')
  @response(200, {
    description: 'NewCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<NewCategory>
  ): Promise<NewCategory> {
    return this.newCategoryRepository.findById(id, filter);
  }

  @patch('/new-categories/{id}')
  @response(204, {
    description: 'NewCategory PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewCategory, {partial: true}),
        },
      },
    })
    newCategory: NewCategory,
  ): Promise<void> {
    await this.newCategoryRepository.updateById(id, newCategory);
  }

  @put('/new-categories/{id}')
  @response(204, {
    description: 'NewCategory PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newCategory: NewCategory,
  ): Promise<void> {
    await this.newCategoryRepository.replaceById(id, newCategory);
  }

  @del('/new-categories/{id}')
  @response(204, {
    description: 'NewCategory DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newCategoryRepository.deleteById(id);
  }
}
