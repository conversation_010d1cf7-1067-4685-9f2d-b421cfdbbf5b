import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class DpReport extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  user_type?: string;

  @property({
    type: 'number',
  })
  dcfId?: number;

  @property({
    type: 'string',
  })
  dpId?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  rp?: string[];

  @property({
    type: 'number',
  })
  site?: number;

  @property({
    type: 'number',
  })
  clientId?: number;

  @property({
    type: 'number',
  })
  reporter_id?: number;

  @property({
    type: 'number',
  })
  reviewer_id?: number;

  @property({
    type: 'number',
    itemType: 'number',
    required: false,
    mysql: {
      dataType: "BIGINT"
    }
  })
  form_id?: number;

  @property({
    type: 'number',
  })
  form_type?: number;

  @property({
    type: 'any',
  })
  year?: any;

  @property({
    type: 'any',
  })
  value?: any;

  @property({
    type: 'any',
  })
  type?: any;

  @property({
    type: 'number',
  })
  submitId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<DpReport>) {
    super(data);
  }
}

export interface DpReportRelations {
  // describe navigational properties here
}

export type DpReportWithRelations = DpReport & DpReportRelations;
