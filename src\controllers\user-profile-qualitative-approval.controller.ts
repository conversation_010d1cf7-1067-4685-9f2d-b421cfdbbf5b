import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  QualitativeApproval,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileQualitativeApprovalController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/qualitative-approvals', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many QualitativeApproval',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QualitativeApproval)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QualitativeApproval>,
  ): Promise<QualitativeApproval[]> {
    return this.userProfileRepository.qualitativeApprovals(id).find(filter);
  }

  @post('/user-profiles/{id}/qualitative-approvals', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(QualitativeApproval)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeApproval, {
            title: 'NewQualitativeApprovalInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) qualitativeApproval: Omit<QualitativeApproval, 'id'>,
  ): Promise<QualitativeApproval> {
    return this.userProfileRepository.qualitativeApprovals(id).create(qualitativeApproval);
  }

  @patch('/user-profiles/{id}/qualitative-approvals', {
    responses: {
      '200': {
        description: 'UserProfile.QualitativeApproval PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeApproval, {partial: true}),
        },
      },
    })
    qualitativeApproval: Partial<QualitativeApproval>,
    @param.query.object('where', getWhereSchemaFor(QualitativeApproval)) where?: Where<QualitativeApproval>,
  ): Promise<Count> {
    return this.userProfileRepository.qualitativeApprovals(id).patch(qualitativeApproval, where);
  }

  @del('/user-profiles/{id}/qualitative-approvals', {
    responses: {
      '200': {
        description: 'UserProfile.QualitativeApproval DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(QualitativeApproval)) where?: Where<QualitativeApproval>,
  ): Promise<Count> {
    return this.userProfileRepository.qualitativeApprovals(id).delete(where);
  }
}
