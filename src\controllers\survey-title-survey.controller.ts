import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SurveyTitle,
  Survey,
} from '../models';
import {SurveyTitleRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SurveyTitleSurveyController {
  constructor(
    @repository(SurveyTitleRepository) protected surveyTitleRepository: SurveyTitleRepository,
  ) { }

  @get('/survey-titles/{id}/surveys', {
    responses: {
      '200': {
        description: 'Array of SurveyTitle has many Survey',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Survey)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Survey>,
  ): Promise<Survey[]> {
    return this.surveyTitleRepository.surveys(id).find(filter);
  }

  @post('/survey-titles/{id}/surveys', {
    responses: {
      '200': {
        description: 'SurveyTitle model instance',
        content: {'application/json': {schema: getModelSchemaRef(Survey)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof SurveyTitle.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Survey, {
            title: 'NewSurveyInSurveyTitle',
            exclude: ['id'],
            optional: ['surveyTitleId']
          }),
        },
      },
    }) survey: Omit<Survey, 'id'>,
  ): Promise<Survey> {
    return this.surveyTitleRepository.surveys(id).create(survey);
  }

  @patch('/survey-titles/{id}/surveys', {
    responses: {
      '200': {
        description: 'SurveyTitle.Survey PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Survey, {partial: true}),
        },
      },
    })
    survey: Partial<Survey>,
    @param.query.object('where', getWhereSchemaFor(Survey)) where?: Where<Survey>,
  ): Promise<Count> {
    return this.surveyTitleRepository.surveys(id).patch(survey, where);
  }

  @del('/survey-titles/{id}/surveys', {
    responses: {
      '200': {
        description: 'SurveyTitle.Survey DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Survey)) where?: Where<Survey>,
  ): Promise<Count> {
    return this.surveyTitleRepository.surveys(id).delete(where);
  }
}
