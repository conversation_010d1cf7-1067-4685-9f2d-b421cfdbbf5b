import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {StructuredResponse} from '../models';
import {StructuredResponseRepository} from '../repositories';

export class StructuredResponseController {
  constructor(
    @repository(StructuredResponseRepository)
    public structuredResponseRepository : StructuredResponseRepository,
  ) {}

  @post('/structured-responses')
  @response(200, {
    description: 'StructuredResponse model instance',
    content: {'application/json': {schema: getModelSchemaRef(StructuredResponse)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StructuredResponse, {
            title: 'NewStructuredResponse',
            exclude: ['id'],
          }),
        },
      },
    })
    structuredResponse: Omit<StructuredResponse, 'id'>,
  ): Promise<StructuredResponse> {
    return this.structuredResponseRepository.create(structuredResponse);
  }

  @get('/structured-responses/count')
  @response(200, {
    description: 'StructuredResponse model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(StructuredResponse) where?: Where<StructuredResponse>,
  ): Promise<Count> {
    return this.structuredResponseRepository.count(where);
  }

  @get('/structured-responses')
  @response(200, {
    description: 'Array of StructuredResponse model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(StructuredResponse, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(StructuredResponse) filter?: Filter<StructuredResponse>,
  ): Promise<StructuredResponse[]> {
    return this.structuredResponseRepository.find(filter);
  }

  @patch('/structured-responses')
  @response(200, {
    description: 'StructuredResponse PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StructuredResponse, {partial: true}),
        },
      },
    })
    structuredResponse: StructuredResponse,
    @param.where(StructuredResponse) where?: Where<StructuredResponse>,
  ): Promise<Count> {
    return this.structuredResponseRepository.updateAll(structuredResponse, where);
  }

  @get('/structured-responses/{id}')
  @response(200, {
    description: 'StructuredResponse model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(StructuredResponse, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(StructuredResponse, {exclude: 'where'}) filter?: FilterExcludingWhere<StructuredResponse>
  ): Promise<StructuredResponse> {
    return this.structuredResponseRepository.findById(id, filter);
  }

  @patch('/structured-responses/{id}')
  @response(204, {
    description: 'StructuredResponse PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StructuredResponse, {partial: true}),
        },
      },
    })
    structuredResponse: StructuredResponse,
  ): Promise<void> {
    await this.structuredResponseRepository.updateById(id, structuredResponse);
  }

  @put('/structured-responses/{id}')
  @response(204, {
    description: 'StructuredResponse PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() structuredResponse: StructuredResponse,
  ): Promise<void> {
    await this.structuredResponseRepository.replaceById(id, structuredResponse);
  }

  @del('/structured-responses/{id}')
  @response(204, {
    description: 'StructuredResponse DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.structuredResponseRepository.deleteById(id);
  }
}
