import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {Standard, StandardRelations} from '../models';

export class StandardRepository extends DefaultCrudRepository<
  Standard,
  typeof Standard.prototype.id,
  StandardRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(Standard, dataSource);
  }
}
