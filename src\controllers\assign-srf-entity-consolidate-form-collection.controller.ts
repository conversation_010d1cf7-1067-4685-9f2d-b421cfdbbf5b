import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignSrfEntity,
  ConsolidateFormCollection,
} from '../models';
import {AssignSrfEntityRepository} from '../repositories';

export class AssignSrfEntityConsolidateFormCollectionController {
  constructor(
    @repository(AssignSrfEntityRepository)
    public assignSrfEntityRepository: AssignSrfEntityRepository,
  ) { }

  @get('/assign-srf-entities/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to AssignSrfEntity',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ConsolidateFormCollection),
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.number('id') id: typeof AssignSrfEntity.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.assignSrfEntityRepository.srf(id);
  }
}
