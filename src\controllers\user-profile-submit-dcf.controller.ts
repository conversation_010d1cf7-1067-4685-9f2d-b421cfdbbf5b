import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  SubmitDcf,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class UserProfileSubmitDcfController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/submit-dcfs', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SubmitDcf',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SubmitDcf)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SubmitDcf>,
  ): Promise<SubmitDcf[]> {
    return this.userProfileRepository.submitDcfs(id).find(filter);
  }

  @post('/user-profiles/{id}/submit-dcfs', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SubmitDcf)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitDcf, {
            title: 'NewSubmitDcfInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) submitDcf: Omit<SubmitDcf, 'id'>,
  ): Promise<SubmitDcf> {
    return this.userProfileRepository.submitDcfs(id).create(submitDcf);
  }

  // @patch('/user-profiles/{id}/submit-dcfs', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitDcf PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitDcf, {partial: true}),
  //       },
  //     },
  //   })
  //   submitDcf: Partial<SubmitDcf>,
  //   @param.query.object('where', getWhereSchemaFor(SubmitDcf)) where?: Where<SubmitDcf>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitDcfs(id).patch(submitDcf, where);
  // }

  // @del('/user-profiles/{id}/submit-dcfs', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.SubmitDcf DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(SubmitDcf)) where?: Where<SubmitDcf>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.submitDcfs(id).delete(where);
  // }
  
}
