import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {AssessmentSubSection3} from './assessment-sub-section3.model';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';

@model()
export class AssessmentSubSection2 extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
  })
  title?: string;
  @property({
    type: 'boolean',
    default: false,
  })
  isRoot?: boolean;
  @property({
    type: 'any',
  })
  order?: any;
  @property({
    type: 'string',
  })
  assessmentSubSection1Id?: string;

  @hasMany(() => AssessmentSubSection3)
  assessmentSubSection3s: AssessmentSubSection3[];

  @belongsTo(() => ConsolidateFormCollection)
  formId: number;

  constructor(data?: Partial<AssessmentSubSection2>) {
    super(data);
  }
}

export interface AssessmentSubSection2Relations {
  // describe navigational properties here
}

export type AssessmentSubSection2WithRelations = AssessmentSubSection2 & AssessmentSubSection2Relations;
