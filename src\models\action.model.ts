import {Entity, model, property} from '@loopback/repository';

@model()
export class Action extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  application?: string;

  @property({
    type: 'object',
  })
  applicationDetails?: object;

  @property({
    type: 'string',
  })
  applicationId?: string;

  @property({
    type: 'string',
  })
  category?: string;

  @property({
    type: 'string',
  })
  uniqueId?: string;


  @property({
    type: 'number',
  })
  appId?: number;

  @property({
    type: 'string',
  })
  actionType?: string;

  @property({
    type: 'string',
  })
  actionToBeTaken?: string;

  @property({
    type: 'string',
  })
  actionTaken?: string;

  @property({
    type: 'string',
  })
  maskId?: string;

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'string',
  })
  remarks?: string;

  @property({
    type: 'string',
  })
  trackId?: string;

  @property({
    type: 'array',
    itemType: 'string',
  })
  uploads?: string[];


  @property({
    type: 'array',
    itemType: 'string',
  })
  evidence?: string[];

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  dueDate?: string;

  @property({
    type: 'string',
  })
  status?: 'Initiated' | 'In Progress' | 'Rejected' | 'Completed' | 'Deleted' | 'Archived';

  @property({
    type: 'string',
  })
  sequence?: string;

  @property({
    type: 'string',
  })
  prefix?: string;

  @property({
    type: 'string',
  })
  createdDate?: string;

  @property({
    type: 'string',
  })
  objectId?: string;
  @property({
    type: 'array',
    itemType: 'string'
  })
  assignedToId?: string[];

  @property({
    type: 'string',
  })
  vendorId?: string;

  @property({
    type: 'string',
  })
  submittedBy?: string;

  @property({
    type: 'string',
  })
  submitURL?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  created?: string;

  @property({
    type: 'date',
    default: () => new Date().toISOString(),
  })
  updated?: string;

  constructor(data?: Partial<Action>) {
    super(data);
  }
}

export interface ActionRelations {
  // describe navigational properties here
}

export type ActionWithRelations = Action & ActionRelations;
