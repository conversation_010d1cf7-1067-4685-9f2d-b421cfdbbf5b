import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewsCirculation, NewsCirculationRelations} from '../models';

export class NewsCirculationRepository extends DefaultCrudRepository<
  NewsCirculation,
  typeof NewsCirculation.prototype.id,
  NewsCirculationRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(NewsCirculation, dataSource);
  }
}
