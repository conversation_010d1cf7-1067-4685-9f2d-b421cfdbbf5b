import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewEfStd} from '../models';
import {NewEfStdRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfStdController {
  constructor(
    @repository(NewEfStdRepository)
    public newEfStdRepository : NewEfStdRepository,
  ) {}

  @post('/new-ef-stds')
  @response(200, {
    description: 'NewEfStd model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEfStd)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfStd, {
            title: 'NewNewEfStd',
            exclude: ['id'],
          }),
        },
      },
    })
    newEfStd: Omit<NewEfStd, 'id'>,
  ): Promise<NewEfStd> {
    return this.newEfStdRepository.create(newEfStd);
  }

  @get('/new-ef-stds/count')
  @response(200, {
    description: 'NewEfStd model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEfStd) where?: Where<NewEfStd>,
  ): Promise<Count> {
    return this.newEfStdRepository.count(where);
  }

  @get('/new-ef-stds')
  @response(200, {
    description: 'Array of NewEfStd model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEfStd, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEfStd) filter?: Filter<NewEfStd>,
  ): Promise<NewEfStd[]> {
    return this.newEfStdRepository.find(filter);
  }

  // @patch('/new-ef-stds')
  // @response(200, {
  //   description: 'NewEfStd PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfStd, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfStd: NewEfStd,
  //   @param.where(NewEfStd) where?: Where<NewEfStd>,
  // ): Promise<Count> {
  //   return this.newEfStdRepository.updateAll(newEfStd, where);
  // }

  @get('/new-ef-stds/{id}')
  @response(200, {
    description: 'NewEfStd model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEfStd, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEfStd, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEfStd>
  ): Promise<NewEfStd> {
    return this.newEfStdRepository.findById(id, filter);
  }

  @patch('/new-ef-stds/{id}')
  @response(204, {
    description: 'NewEfStd PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfStd, {partial: true}),
        },
      },
    })
    newEfStd: NewEfStd,
  ): Promise<void> {
    await this.newEfStdRepository.updateById(id, newEfStd);
  }

  @put('/new-ef-stds/{id}')
  @response(204, {
    description: 'NewEfStd PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEfStd: NewEfStd,
  ): Promise<void> {
    await this.newEfStdRepository.replaceById(id, newEfStd);
  }

  @del('/new-ef-stds/{id}')
  @response(204, {
    description: 'NewEfStd DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfStdRepository.deleteById(id);
  }
}
