import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEfCategory, NewEfCategoryRelations, NewEfSubcategory1} from '../models';
import {NewEfSubcategory1Repository} from './new-ef-subcategory1.repository';

export class NewEfCategoryRepository extends DefaultCrudRepository<
  NewEfCategory,
  typeof NewEfCategory.prototype.id,
  NewEfCategoryRelations
> {

  public readonly newEfSubcategory1s: HasManyRepositoryFactory<NewEfSubcategory1, typeof NewEfCategory.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfSubcategory1Repository') protected newEfSubcategory1RepositoryGetter: Getter<NewEfSubcategory1Repository>,
  ) {
    super(NewEfCategory, dataSource);
    this.newEfSubcategory1s = this.createHasManyRepositoryFactoryFor('newEfSubcategory1s', newEfSubcategory1RepositoryGetter,);
    this.registerInclusionResolver('newEfSubcategory1s', this.newEfSubcategory1s.inclusionResolver);
  }
}
