import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {Question, QuestionRelations, SubQuestion} from '../models';
import {SubQuestionRepository} from './sub-question.repository';

export class QuestionRepository extends DefaultCrudRepository<
  Question,
  typeof Question.prototype.id,
  QuestionRelations
> {

  public readonly subQuestions: HasManyRepositoryFactory<SubQuestion, typeof Question.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('SubQuestionRepository') protected subQuestionRepositoryGetter: Getter<SubQuestionRepository>,
  ) {
    super(Question, dataSource);
    this.subQuestions = this.createHasManyRepositoryFactoryFor('subQuestions', subQuestionRepositoryGetter,);
    this.registerInclusionResolver('subQuestions', this.subQuestions.inclusionResolver);
  }
}
