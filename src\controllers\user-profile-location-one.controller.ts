import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  LocationOne,
} from '../models';
import {UserProfileRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';



export class UserProfileLocationOneController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/location-ones', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many LocationOne',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationOne)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<LocationOne>,
  ): Promise<LocationOne[]> {
    return this.userProfileRepository.locationOnes(id).find(filter);
  }

  @post('/user-profiles/{id}/location-ones', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(LocationOne)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationOne, {
            title: 'NewLocationOneInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) locationOne: Omit<LocationOne, 'id'>,
  ): Promise<LocationOne> {
    return this.userProfileRepository.locationOnes(id).create(locationOne);
  }

  // @patch('/user-profiles/{id}/location-ones', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.LocationOne PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(LocationOne, {partial: true}),
  //       },
  //     },
  //   })
  //   locationOne: Partial<LocationOne>,
  //   @param.query.object('where', getWhereSchemaFor(LocationOne)) where?: Where<LocationOne>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.locationOnes(id).patch(locationOne, where);
  // }

  // @del('/user-profiles/{id}/location-ones', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.LocationOne DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(LocationOne)) where?: Where<LocationOne>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.locationOnes(id).delete(where);
  // }
  
}
