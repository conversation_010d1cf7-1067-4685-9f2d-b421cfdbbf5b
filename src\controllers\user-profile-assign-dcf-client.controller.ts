import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  AssignDcfClient,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';





export class UserProfileAssignDcfClientController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-dcf-clients', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDcfClient',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDcfClient)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDcfClient>,
  ): Promise<AssignDcfClient[]> {
    return this.userProfileRepository.assignDcfClients(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-dcf-clients', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDcfClient)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfClient, {
            title: 'NewAssignDcfClientInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDcfClient: Omit<AssignDcfClient, 'id'>,
  ): Promise<AssignDcfClient> {
    return this.userProfileRepository.assignDcfClients(id).create(assignDcfClient);
  }

  // @patch('/user-profiles/{id}/assign-dcf-clients', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDcfClient PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfClient, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfClient: Partial<AssignDcfClient>,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfClient)) where?: Where<AssignDcfClient>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDcfClients(id).patch(assignDcfClient, where);
  // }

  // @del('/user-profiles/{id}/assign-dcf-clients', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignDcfClient DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfClient)) where?: Where<AssignDcfClient>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignDcfClients(id).delete(where);
  // }
  
}
