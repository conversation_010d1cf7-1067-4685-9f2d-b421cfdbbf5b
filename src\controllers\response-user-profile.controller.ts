import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Response,
  UserProfile,
} from '../models';
import {ResponseRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ResponseUserProfileController {
  constructor(
    @repository(ResponseRepository)
    public responseRepository: ResponseRepository,
  ) { }

  @get('/responses/{id}/user-profile', {
    responses: {
      '200': {
        description: 'UserProfile belonging to Response',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UserProfile)},
          },
        },
      },
    },
  })
  async getUserProfile(
    @param.path.number('id') id: typeof Response.prototype.id,
  ): Promise<UserProfile> {
    return this.responseRepository.userProfile(id);
  }
}
