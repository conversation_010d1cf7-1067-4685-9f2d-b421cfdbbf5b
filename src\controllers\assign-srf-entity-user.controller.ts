import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignSrfEntityUser} from '../models';
import {AssignSrfEntityUserRepository, UserProfileRepository} from '../repositories';

export class AssignSrfEntityUserController {
  constructor(
    @repository(AssignSrfEntityUserRepository)
    public assignSrfEntityUserRepository: AssignSrfEntityUserRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
  ) { }

  @post('/assign-srf-entity-users')
  @response(200, {
    description: 'AssignSrfEntityUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignSrfEntityUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {
            title: 'NewAssignSrfEntityUser',
            exclude: ['id'],
          }),
        },
      },
    })
    assignSrfEntityUser: Omit<AssignSrfEntityUser, 'id'>,
  ): Promise<AssignSrfEntityUser> {
    return this.assignSrfEntityUserRepository.create(assignSrfEntityUser);
  }

  @get('/assign-srf-entity-users/count')
  @response(200, {
    description: 'AssignSrfEntityUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignSrfEntityUser) where?: Where<AssignSrfEntityUser>,
  ): Promise<Count> {
    return this.assignSrfEntityUserRepository.count(where);
  }

  @get('/assign-srf-entity-users')
  @response(200, {
    description: 'Array of AssignSrfEntityUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignSrfEntityUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignSrfEntityUser) filter?: Filter<AssignSrfEntityUser>,
  ): Promise<AssignSrfEntityUser[]> {
    return this.assignSrfEntityUserRepository.find(filter);
  }

  @patch('/assign-srf-entity-users')
  @response(200, {
    description: 'AssignSrfEntityUser PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {partial: true}),
        },
      },
    })
    assignSrfEntityUser: AssignSrfEntityUser,
    @param.where(AssignSrfEntityUser) where?: Where<AssignSrfEntityUser>,
  ): Promise<Count> {
    return this.assignSrfEntityUserRepository.updateAll(assignSrfEntityUser, where);
  }

  @get('/assign-srf-entity-users/{id}')
  @response(200, {
    description: 'AssignSrfEntityUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignSrfEntityUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignSrfEntityUser, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignSrfEntityUser>
  ): Promise<AssignSrfEntityUser> {
    return this.assignSrfEntityUserRepository.findById(id, filter);
  }

  @patch('/assign-srf-entity-users/{id}')
  @response(204, {
    description: 'AssignSrfEntityUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntityUser, {partial: true}),
        },
      },
    })
    assignSrfEntityUser: AssignSrfEntityUser,
  ): Promise<void> {
    await this.assignSrfEntityUserRepository.updateById(id, assignSrfEntityUser);
  }

  @put('/assign-srf-entity-users/{id}')
  @response(204, {
    description: 'AssignSrfEntityUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignSrfEntityUser: AssignSrfEntityUser,
  ): Promise<void> {
    await this.assignSrfEntityUserRepository.replaceById(id, assignSrfEntityUser);
  }

  @del('/assign-srf-entity-users/{id}')
  @response(204, {
    description: 'AssignSrfEntityUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignSrfEntityUserRepository.deleteById(id);
  }
}
