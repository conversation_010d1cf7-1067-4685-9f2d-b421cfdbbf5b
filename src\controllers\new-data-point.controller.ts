import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewDataPoint} from '../models';
import {NewDataPointRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewDataPointController {
  constructor(
    @repository(NewDataPointRepository)
    public newDataPointRepository : NewDataPointRepository,
  ) {}

  @post('/new-data-points')
  @response(200, {
    description: 'NewDataPoint model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewDataPoint)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewDataPoint, {
            title: 'NewDataPoint',
            exclude: ['id'],
          }),
        },
      },
    })
    newDataPoint: Omit<NewDataPoint, 'id'>,
  ): Promise<NewDataPoint> {
    const DataPoint = await this.newDataPointRepository.create(newDataPoint);
    const id = DataPoint.id;
    const suffix = newDataPoint.suffix + id;
    DataPoint.suffix = suffix;
    await this.newDataPointRepository.updateById(id, {suffix: suffix});
    return DataPoint;
  }

  @get('/new-data-points/count')
  @response(200, {
    description: 'NewDataPoint model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewDataPoint) where?: Where<NewDataPoint>,
  ): Promise<Count> {
    return this.newDataPointRepository.count(where);
  }

  @get('/new-data-points')
  @response(200, {
    description: 'Array of NewDataPoint model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewDataPoint, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewDataPoint) filter?: Filter<NewDataPoint>,
  ): Promise<NewDataPoint[]> {
    return this.newDataPointRepository.find(filter);
  }

  // @patch('/new-data-points')
  // @response(200, {
  //   description: 'NewDataPoint PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewDataPoint, {partial: true}),
  //       },
  //     },
  //   })
  //   newDataPoint: NewDataPoint,
  //   @param.where(NewDataPoint) where?: Where<NewDataPoint>,
  // ): Promise<Count> {
  //   return this.newDataPointRepository.updateAll(newDataPoint, where);
  // }


  @patch('/new-data-points')
  @response(200, {
    description: 'NewDataPoint PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'array',
            properties: {
              newDataPoints: {

                type: 'array',
                items: getModelSchemaRef(NewDataPoint, { partial: true }),
              },
            },
          },
        },
      },
    })
    newDataPoints: NewDataPoint[],
    @param.where(NewDataPoint) where?: Where<NewDataPoint>,
  ): Promise<Count> {
    const updatePromises = newDataPoints.map((newDataPoint) => {
      return this.newDataPointRepository.updateById(newDataPoint.id, {order: newDataPoint.order});
    });

    await Promise.all(updatePromises);

    const totalCount = newDataPoints.length;
    return { count: totalCount };
  }

  @get('/new-data-points/{id}')
  @response(200, {
    description: 'NewDataPoint model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewDataPoint, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewDataPoint, {exclude: 'where'}) filter?: FilterExcludingWhere<NewDataPoint>
  ): Promise<NewDataPoint> {
    return this.newDataPointRepository.findById(id, filter);
  }

  @patch('/new-data-points/{id}')
  @response(204, {
    description: 'NewDataPoint PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewDataPoint, {partial: true}),
        },
      },
    })
    newDataPoint: NewDataPoint,
  ): Promise<void> {
    await this.newDataPointRepository.updateById(id, newDataPoint);
  }

  @put('/new-data-points/{id}')
  @response(204, {
    description: 'NewDataPoint PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newDataPoint: NewDataPoint,
  ): Promise<void> {
    await this.newDataPointRepository.replaceById(id, newDataPoint);
  }

  @del('/new-data-points/{id}')
  @response(204, {
    description: 'NewDataPoint DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newDataPointRepository.deleteById(id);
  }
}
