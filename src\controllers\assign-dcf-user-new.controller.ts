import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignDcfUserNew} from '../models';
import {AssignDcfUserNewRepository, AssignDcfUserRepository} from '../repositories';

export class AssignDcfUserNewController {
  constructor(
    @repository(AssignDcfUserNewRepository)
    public assignDcfUserNewRepository: AssignDcfUserNewRepository,

    @repository(AssignDcfUserRepository)
    public assignDcfUserRepository: AssignDcfUserRepository,
  ) { }

  @post('/assign-dcf-user-news')
  @response(200, {
    description: 'AssignDcfUserNew model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDcfUserNew)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfUserNew, {
            title: 'NewAssignDcfUserNew',
            exclude: ['id'],
          }),
        },
      },
    })
    AssignDcfUserNew: Omit<AssignDcfUserNew, 'id'>,
  ): Promise<AssignDcfUserNew> {
    return this.assignDcfUserNewRepository.create(AssignDcfUserNew);
  }

  @get('/assign-dcf-user-news/count')
  @response(200, {
    description: 'AssignDcfUserNew model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDcfUserNew) where?: Where<AssignDcfUserNew>,
  ): Promise<Count> {
    return this.assignDcfUserNewRepository.count(where);
  }

  @get('/assign-dcf-user-news')
  @response(200, {
    description: 'Array of AssignDcfUserNew model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDcfUserNew, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDcfUserNew) filter?: Filter<AssignDcfUserNew>,
  ): Promise<AssignDcfUserNew[]> {
    return this.assignDcfUserNewRepository.find(filter);
  }

  // @patch('/assign-dcf-user-news')
  // @response(200, {
  //   description: 'AssignDcfUserNew PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfUserNew, {partial: true}),
  //       },
  //     },
  //   })
  //   AssignDcfUserNew: AssignDcfUserNew,
  //   @param.where(AssignDcfUserNew) where?: Where<AssignDcfUserNew>,
  // ): Promise<Count> {
  //   return this.assignDcfUserNewRepository.updateAll(AssignDcfUserNew, where);
  // }

  @get('/assign-dcf-user-news/{id}')
  @response(200, {
    description: 'AssignDcfUserNew model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDcfUserNew, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDcfUserNew, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDcfUserNew>
  ): Promise<AssignDcfUserNew> {
    return this.assignDcfUserNewRepository.findById(id, filter);
  }

  @patch('/assign-dcf-user-news/{id}')
  @response(204, {
    description: 'AssignDcfUserNew PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfUserNew, {partial: true}),
        },
      },
    })
    AssignDcfUserNew: AssignDcfUserNew,
  ): Promise<void> {
    await this.assignDcfUserNewRepository.updateById(id, AssignDcfUserNew);
  }

  @put('/assign-dcf-user-news/{id}')
  @response(204, {
    description: 'AssignDcfUserNew PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() AssignDcfUserNew: AssignDcfUserNew,
  ): Promise<void> {
    await this.assignDcfUserNewRepository.replaceById(id, AssignDcfUserNew);
  }

  @del('/assign-dcf-user-news/{id}')
  @response(204, {
    description: 'AssignDcfUserNew DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDcfUserNewRepository.deleteById(id);
  }


  @post('/clone-assign-dcf-user')
  @response(200, {
    description: 'SubmitDcf PATCH success',
  })
  async clone(): Promise<Boolean> {
    try {
      let data = await this.assignDcfUserRepository.find({where: {userProfileId: 94}});

      // Use Promise.all() to await the creation of all objects in parallel
      await Promise.all(data.map(async (item) => {
        let obj = JSON.parse(JSON.stringify(item))
        delete obj.id
        delete obj.modifier_id
        delete obj.modified_on
        delete obj.formCollectionId
        delete obj.assignDcfClientId
        if (obj.end_date === null) {
          delete obj.end_date
        }

        obj.approver_id = [obj.approver_id]
        obj.reviewer_id = obj.user_id
        obj.site = obj.site[0]
        console.log(obj)
        await this.assignDcfUserNewRepository.create(obj)
      }));

      // If all creations are successful, return true
      return true;
    } catch (error) {
      // If an error occurs during creation, return false
      console.error('Error occurred:', error);
      return false;
    }
  }

}
