import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {LcaDataSubmission, LcaDataSubmissionRelations, VendorCode} from '../models';
import {VendorCodeRepository} from './vendor-code.repository';

export class LcaDataSubmissionRepository extends DefaultCrudRepository<
  LcaDataSubmission,
  typeof LcaDataSubmission.prototype.id,
  LcaDataSubmissionRelations
> {

  public readonly vendor: BelongsToAccessor<VendorCode, typeof LcaDataSubmission.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>,
  ) {
    super(LcaDataSubmission, dataSource);
    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
  }
}
