import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class NewInitiatives extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  responsibility?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  initiatives?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  status?: any[];

  @property({
    type: 'number',
  })
  indicator?: number;

  @property({
    type: 'object',
  })
  applicability?: object;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  newGoalsId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<NewInitiatives>) {
    super(data);
  }
}

export interface NewInitiativesRelations {
  // describe navigational properties here
}

export type NewInitiativesWithRelations = NewInitiatives & NewInitiativesRelations;
