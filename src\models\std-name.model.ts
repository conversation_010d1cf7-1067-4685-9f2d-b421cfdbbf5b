import {Entity, model, property, hasMany} from '@loopback/repository';
import {StdScope} from './std-scope.model';

@model()
export class StdName extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'number',
  })
  stdYearId?: number;

  @hasMany(() => StdScope)
  stdScopes: StdScope[];

  constructor(data?: Partial<StdName>) {
    super(data);
  }
}

export interface StdNameRelations {
  // describe navigational properties here
}

export type StdNameWithRelations = StdName & StdNameRelations;
