import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SupplierAssignmentSubmission} from '../models';
import {SupplierAssignmentSubmissionRepository} from '../repositories';

export class SupplierAssessmentSubmissionController {
  constructor(
    @repository(SupplierAssignmentSubmissionRepository)
    public supplierAssignmentSubmissionRepository : SupplierAssignmentSubmissionRepository,
  ) {}

  @post('/supplier-assignment-submissions')
  @response(200, {
    description: 'SupplierAssignmentSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierAssignmentSubmission)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssignmentSubmission, {
            title: 'NewSupplierAssignmentSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    supplierAssignmentSubmission: Omit<SupplierAssignmentSubmission, 'id'>,
  ): Promise<SupplierAssignmentSubmission> {
    return this.supplierAssignmentSubmissionRepository.create(supplierAssignmentSubmission);
  }

  @get('/supplier-assignment-submissions/count')
  @response(200, {
    description: 'SupplierAssignmentSubmission model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplierAssignmentSubmission) where?: Where<SupplierAssignmentSubmission>,
  ): Promise<Count> {
    return this.supplierAssignmentSubmissionRepository.count(where);
  }

  @get('/supplier-assignment-submissions')
  @response(200, {
    description: 'Array of SupplierAssignmentSubmission model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplierAssignmentSubmission, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplierAssignmentSubmission) filter?: Filter<SupplierAssignmentSubmission>,
  ): Promise<SupplierAssignmentSubmission[]> {
    return this.supplierAssignmentSubmissionRepository.find(filter);
  }

  @patch('/supplier-assignment-submissions')
  @response(200, {
    description: 'SupplierAssignmentSubmission PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssignmentSubmission, {partial: true}),
        },
      },
    })
    supplierAssignmentSubmission: SupplierAssignmentSubmission,
    @param.where(SupplierAssignmentSubmission) where?: Where<SupplierAssignmentSubmission>,
  ): Promise<Count> {
    return this.supplierAssignmentSubmissionRepository.updateAll(supplierAssignmentSubmission, where);
  }

  @get('/supplier-assignment-submissions/{id}')
  @response(200, {
    description: 'SupplierAssignmentSubmission model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplierAssignmentSubmission, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SupplierAssignmentSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplierAssignmentSubmission>
  ): Promise<SupplierAssignmentSubmission> {
    return this.supplierAssignmentSubmissionRepository.findById(id, filter);
  }

  @patch('/supplier-assignment-submissions/{id}')
  @response(204, {
    description: 'SupplierAssignmentSubmission PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssignmentSubmission, {partial: true}),
        },
      },
    })
    supplierAssignmentSubmission: SupplierAssignmentSubmission,
  ): Promise<void> {
    await this.supplierAssignmentSubmissionRepository.updateById(id, supplierAssignmentSubmission);
  }

  @put('/supplier-assignment-submissions/{id}')
  @response(204, {
    description: 'SupplierAssignmentSubmission PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() supplierAssignmentSubmission: SupplierAssignmentSubmission,
  ): Promise<void> {
    await this.supplierAssignmentSubmissionRepository.replaceById(id, supplierAssignmentSubmission);
  }

  @del('/supplier-assignment-submissions/{id}')
  @response(204, {
    description: 'SupplierAssignmentSubmission DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.supplierAssignmentSubmissionRepository.deleteById(id);
  }
}
