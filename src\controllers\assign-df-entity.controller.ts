import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignDfEntity} from '../models';
import {AssignDfEntityRepository} from '../repositories';

export class AssignDfEntityController {
  constructor(
    @repository(AssignDfEntityRepository)
    public assignDfEntityRepository: AssignDfEntityRepository,
  ) { }

  @post('/assign-df-entities')
  @response(200, {
    description: 'AssignDfEntity model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDfEntity)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntity, {
            title: 'NewAssignDfEntity',
            exclude: ['id'],
          }),
        },
      },
    })
    assignDfEntity: Omit<AssignDfEntity, 'id'>,
  ): Promise<AssignDfEntity> {
    return this.assignDfEntityRepository.create(assignDfEntity);
  }

  @get('/assign-df-entities/count')
  @response(200, {
    description: 'AssignDfEntity model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDfEntity) where?: Where<AssignDfEntity>,
  ): Promise<Count> {
    return this.assignDfEntityRepository.count(where);
  }

  @get('/assign-df-entities')
  @response(200, {
    description: 'Array of AssignDfEntity model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDfEntity, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDfEntity) filter?: Filter<AssignDfEntity>,
  ): Promise<AssignDfEntity[]> {
    return this.assignDfEntityRepository.find(filter);
  }

  @patch('/assign-df-entities')
  @response(200, {
    description: 'AssignDfEntity PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntity, {partial: true}),
        },
      },
    })
    assignDfEntity: AssignDfEntity,
    @param.where(AssignDfEntity) where?: Where<AssignDfEntity>,
  ): Promise<Count> {
    return this.assignDfEntityRepository.updateAll(assignDfEntity, where);
  }

  @get('/assign-df-entities/{id}')
  @response(200, {
    description: 'AssignDfEntity model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDfEntity, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDfEntity, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDfEntity>
  ): Promise<AssignDfEntity> {
    return this.assignDfEntityRepository.findById(id, filter);
  }

  @patch('/assign-df-entities/{id}')
  @response(204, {
    description: 'AssignDfEntity PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntity, {partial: true}),
        },
      },
    })
    assignDfEntity: AssignDfEntity,
  ): Promise<void> {
    await this.assignDfEntityRepository.updateById(id, assignDfEntity);
  }

  @put('/assign-df-entities/{id}')
  @response(204, {
    description: 'AssignDfEntity PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignDfEntity: AssignDfEntity,
  ): Promise<void> {
    await this.assignDfEntityRepository.replaceById(id, assignDfEntity);
  }

  @del('/assign-df-entities/{id}')
  @response(204, {
    description: 'AssignDfEntity DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDfEntityRepository.deleteById(id);
  }
}
