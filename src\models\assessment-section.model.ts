import {Entity, hasMany, model, property} from '@loopback/repository';
import {AssessmentSubSection1} from './assessment-sub-section1.model';

@model()
export class AssessmentSection extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
  })
  title?: string;
  @property({
    type: 'any',
  })
  order?: any;
  @hasMany(() => AssessmentSubSection1)
  assessmentSubSection1s: AssessmentSubSection1[];

  constructor(data?: Partial<AssessmentSection>) {
    super(data);
  }
}

export interface AssessmentSectionRelations {
  // describe navigational properties here
}

export type AssessmentSectionWithRelations = AssessmentSection & AssessmentSectionRelations;
