import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  IndicatorApproverAssignment,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileIndicatorApproverAssignmentController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/indicator-approver-assignments', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many IndicatorApproverAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IndicatorApproverAssignment)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<IndicatorApproverAssignment>,
  ): Promise<IndicatorApproverAssignment[]> {
    return this.userProfileRepository.indicatorApproverAssignments(id).find(filter);
  }

  @post('/user-profiles/{id}/indicator-approver-assignments', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(IndicatorApproverAssignment)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorApproverAssignment, {
            title: 'NewIndicatorApproverAssignmentInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) indicatorApproverAssignment: Omit<IndicatorApproverAssignment, 'id'>,
  ): Promise<IndicatorApproverAssignment> {
    return this.userProfileRepository.indicatorApproverAssignments(id).create(indicatorApproverAssignment);
  }

  @patch('/user-profiles/{id}/indicator-approver-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.IndicatorApproverAssignment PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorApproverAssignment, {partial: true}),
        },
      },
    })
    indicatorApproverAssignment: Partial<IndicatorApproverAssignment>,
    @param.query.object('where', getWhereSchemaFor(IndicatorApproverAssignment)) where?: Where<IndicatorApproverAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.indicatorApproverAssignments(id).patch(indicatorApproverAssignment, where);
  }

  @del('/user-profiles/{id}/indicator-approver-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.IndicatorApproverAssignment DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(IndicatorApproverAssignment)) where?: Where<IndicatorApproverAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.indicatorApproverAssignments(id).delete(where);
  }
}
