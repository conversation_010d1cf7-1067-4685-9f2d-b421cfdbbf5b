import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ClientInitiative} from '../models';
import {ClientInitiativeRepository} from '../repositories';

export class ClientInitiativesController {
  constructor(
    @repository(ClientInitiativeRepository)
    public clientInitiativeRepository : ClientInitiativeRepository,
  ) {}

  @post('/client-initiatives')
  @response(200, {
    description: 'ClientInitiative model instance',
    content: {'application/json': {schema: getModelSchemaRef(ClientInitiative)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientInitiative, {
            title: 'NewClientInitiative',
            exclude: ['id'],
          }),
        },
      },
    })
    clientInitiative: Omit<ClientInitiative, 'id'>,
  ): Promise<ClientInitiative> {
    return this.clientInitiativeRepository.create(clientInitiative);
  }

  @get('/client-initiatives/count')
  @response(200, {
    description: 'ClientInitiative model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ClientInitiative) where?: Where<ClientInitiative>,
  ): Promise<Count> {
    return this.clientInitiativeRepository.count(where);
  }

  @get('/client-initiatives')
  @response(200, {
    description: 'Array of ClientInitiative model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ClientInitiative, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ClientInitiative) filter?: Filter<ClientInitiative>,
  ): Promise<ClientInitiative[]> {
    return this.clientInitiativeRepository.find(filter);
  }

  @patch('/client-initiatives')
  @response(200, {
    description: 'ClientInitiative PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientInitiative, {partial: true}),
        },
      },
    })
    clientInitiative: ClientInitiative,
    @param.where(ClientInitiative) where?: Where<ClientInitiative>,
  ): Promise<Count> {
    return this.clientInitiativeRepository.updateAll(clientInitiative, where);
  }

  @get('/client-initiatives/{id}')
  @response(200, {
    description: 'ClientInitiative model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ClientInitiative, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ClientInitiative, {exclude: 'where'}) filter?: FilterExcludingWhere<ClientInitiative>
  ): Promise<ClientInitiative> {
    return this.clientInitiativeRepository.findById(id, filter);
  }

  @patch('/client-initiatives/{id}')
  @response(204, {
    description: 'ClientInitiative PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientInitiative, {partial: true}),
        },
      },
    })
    clientInitiative: ClientInitiative,
  ): Promise<void> {
    await this.clientInitiativeRepository.updateById(id, clientInitiative);
  }

  @put('/client-initiatives/{id}')
  @response(204, {
    description: 'ClientInitiative PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() clientInitiative: ClientInitiative,
  ): Promise<void> {
    await this.clientInitiativeRepository.replaceById(id, clientInitiative);
  }

  @del('/client-initiatives/{id}')
  @response(204, {
    description: 'ClientInitiative DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.clientInitiativeRepository.deleteById(id);
  }
}
