import {Entity, model, property} from '@loopback/repository';

@model()
export class Helper extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'array',
    itemType: 'number',
  })
  shortlist_supplier_self?: number[];
  @property({
    type: 'array',
    itemType: 'number',
  })
  shortlist_dealer_self?: number[];

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<Helper>) {
    super(data);
  }
}

export interface HelperRelations {
  // describe navigational properties here
}

export type HelperWithRelations = Helper & HelperRelations;
