import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewInitiatives, NewInitiativesRelations} from '../models';

export class NewInitiativesRepository extends DefaultCrudRepository<
  NewInitiatives,
  typeof NewInitiatives.prototype.id,
  NewInitiativesRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(NewInitiatives, dataSource);
  }
}
