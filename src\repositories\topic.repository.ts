import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {Topic, TopicRelations, SubTopic} from '../models';
import {SubTopicRepository} from './sub-topic.repository';

export class TopicRepository extends DefaultCrudRepository<
  Topic,
  typeof Topic.prototype.id,
  TopicRelations
> {

  public readonly subTopics: HasManyRepositoryFactory<SubTopic, typeof Topic.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('SubTopicRepository') protected subTopicRepositoryGetter: Getter<SubTopicRepository>,
  ) {
    super(Topic, dataSource);
    this.subTopics = this.createHasManyRepositoryFactoryFor('subTopics', subTopicRepositoryGetter,);
    this.registerInclusionResolver('subTopics', this.subTopics.inclusionResolver);
  }
}
