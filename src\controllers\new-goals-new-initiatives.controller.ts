import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewGoals,
  NewInitiatives,
} from '../models';
import {NewGoalsRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewGoalsNewInitiativesController {
  constructor(
    @repository(NewGoalsRepository) protected newGoalsRepository: NewGoalsRepository,
  ) { }

  @get('/new-goals/{id}/new-initiatives', {
    responses: {
      '200': {
        description: 'Array of NewGoals has many NewInitiatives',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewInitiatives)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewInitiatives>,
  ): Promise<NewInitiatives[]> {
    return this.newGoalsRepository.newInitiatives(id).find(filter);
  }

  @post('/new-goals/{id}/new-initiatives', {
    responses: {
      '200': {
        description: 'NewGoals model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewInitiatives)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewGoals.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewInitiatives, {
            title: 'NewNewInitiativesInNewGoals',
            exclude: ['id'],
            optional: ['newGoalsId']
          }),
        },
      },
    }) newInitiatives: Omit<NewInitiatives, 'id'>,
  ): Promise<NewInitiatives> {
    return this.newGoalsRepository.newInitiatives(id).create(newInitiatives);
  }

  // @patch('/new-goals/{id}/new-initiatives', {
  //   responses: {
  //     '200': {
  //       description: 'NewGoals.NewInitiatives PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewInitiatives, {partial: true}),
  //       },
  //     },
  //   })
  //   newInitiatives: Partial<NewInitiatives>,
  //   @param.query.object('where', getWhereSchemaFor(NewInitiatives)) where?: Where<NewInitiatives>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.newInitiatives(id).patch(newInitiatives, where);
  // }

  // @del('/new-goals/{id}/new-initiatives', {
  //   responses: {
  //     '200': {
  //       description: 'NewGoals.NewInitiatives DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewInitiatives)) where?: Where<NewInitiatives>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.newInitiatives(id).delete(where);
  // }
  
}
