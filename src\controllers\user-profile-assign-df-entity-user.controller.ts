import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  AssignDfEntityUser,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileAssignDfEntityUserController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-df-entity-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDfEntityUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDfEntityUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDfEntityUser>,
  ): Promise<AssignDfEntityUser[]> {
    return this.userProfileRepository.assignDfEntityUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-df-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDfEntityUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntityUser, {
            title: 'NewAssignDfEntityUserInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDfEntityUser: Omit<AssignDfEntityUser, 'id'>,
  ): Promise<AssignDfEntityUser> {
    return this.userProfileRepository.assignDfEntityUsers(id).create(assignDfEntityUser);
  }

  @patch('/user-profiles/{id}/assign-df-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignDfEntityUser PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDfEntityUser, {partial: true}),
        },
      },
    })
    assignDfEntityUser: Partial<AssignDfEntityUser>,
    @param.query.object('where', getWhereSchemaFor(AssignDfEntityUser)) where?: Where<AssignDfEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignDfEntityUsers(id).patch(assignDfEntityUser, where);
  }

  @del('/user-profiles/{id}/assign-df-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignDfEntityUser DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignDfEntityUser)) where?: Where<AssignDfEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignDfEntityUsers(id).delete(where);
  }
}
