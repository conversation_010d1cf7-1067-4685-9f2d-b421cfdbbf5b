import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {QlListingFilter} from '../models';
import {QlListingFilterRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class QlListingFilterController {
  constructor(
    @repository(QlListingFilterRepository)
    public qlListingFilterRepository : QlListingFilterRepository,
  ) {}

  @post('/ql-listing-filters')
  @response(200, {
    description: 'QlListingFilter model instance',
    content: {'application/json': {schema: getModelSchemaRef(QlListingFilter)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QlListingFilter, {
            title: 'NewQlListingFilter',
            exclude: ['id'],
          }),
        },
      },
    })
    qlListingFilter: Omit<QlListingFilter, 'id'>,
  ): Promise<QlListingFilter> {
    return this.qlListingFilterRepository.create(qlListingFilter);
  }

  @get('/ql-listing-filters/count')
  @response(200, {
    description: 'QlListingFilter model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QlListingFilter) where?: Where<QlListingFilter>,
  ): Promise<Count> {
    return this.qlListingFilterRepository.count(where);
  }

  @get('/ql-listing-filters')
  @response(200, {
    description: 'Array of QlListingFilter model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QlListingFilter, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QlListingFilter) filter?: Filter<QlListingFilter>,
  ): Promise<QlListingFilter[]> {
    return this.qlListingFilterRepository.find(filter);
  }

  // @patch('/ql-listing-filters')
  // @response(200, {
  //   description: 'QlListingFilter PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(QlListingFilter, {partial: true}),
  //       },
  //     },
  //   })
  //   qlListingFilter: QlListingFilter,
  //   @param.where(QlListingFilter) where?: Where<QlListingFilter>,
  // ): Promise<Count> {
  //   return this.qlListingFilterRepository.updateAll(qlListingFilter, where);
  // }

  @get('/ql-listing-filters/{id}')
  @response(200, {
    description: 'QlListingFilter model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QlListingFilter, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(QlListingFilter, {exclude: 'where'}) filter?: FilterExcludingWhere<QlListingFilter>
  ): Promise<QlListingFilter> {
    return this.qlListingFilterRepository.findById(id, filter);
  }

  @patch('/ql-listing-filters/{id}')
  @response(204, {
    description: 'QlListingFilter PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QlListingFilter, {partial: true}),
        },
      },
    })
    qlListingFilter: QlListingFilter,
  ): Promise<void> {
    await this.qlListingFilterRepository.updateById(id, qlListingFilter);
  }

  @put('/ql-listing-filters/{id}')
  @response(204, {
    description: 'QlListingFilter PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() qlListingFilter: QlListingFilter,
  ): Promise<void> {
    await this.qlListingFilterRepository.replaceById(id, qlListingFilter);
  }

  @del('/ql-listing-filters/{id}')
  @response(204, {
    description: 'QlListingFilter DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.qlListingFilterRepository.deleteById(id);
  }
}
