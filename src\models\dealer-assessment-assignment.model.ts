import {belongsTo, Entity, hasMany, hasOne, model, property} from '@loopback/repository';
import {Action} from './action.model';
import {DealerAuditorChecklistSubmission} from './dealer-auditor-checklist-submission.model';
import {DealerResponseForm} from './dealer-response-form.model';
import {UserProfile} from './user-profile.model';
import {VendorCode} from './vendor-code.model';

@model()
export class DealerAssessmentAssignment extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  assessors?: any[];

  @property({
    type: 'string',
  })
  assessmentStartDate?: string;

  @property({
    type: 'string',
  })
  auditStartDate?: string;
  @property({
    type: 'string',
  })
  created_on?: string;
  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  vendorCode?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_by?: number | null;

  @belongsTo(() => UserProfile)
  dealerId: number;


  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => DealerResponseForm)
  formId: number;

  @hasOne(() => DealerAuditorChecklistSubmission)
  dealerAuditorChecklistSubmission: DealerAuditorChecklistSubmission;

  @belongsTo(() => VendorCode)
  vendorId: number;

  @hasMany(() => Action, {keyTo: 'appId'})
  actions: Action[];

  constructor(data?: Partial<DealerAssessmentAssignment>) {
    super(data);
  }
}

export interface DealerAssessmentAssignmentRelations {
  // describe navigational properties here
}

export type DealerAssessmentAssignmentWithRelations = DealerAssessmentAssignment & DealerAssessmentAssignmentRelations;
