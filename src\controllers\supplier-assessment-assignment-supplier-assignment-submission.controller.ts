import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  SupplierAssessmentAssignment,
  SupplierAssignmentSubmission,
} from '../models';
import {SupplierAssessmentAssignmentRepository} from '../repositories';

export class SupplierAssessmentAssignmentSupplierAssignmentSubmissionController {
  constructor(
    @repository(SupplierAssessmentAssignmentRepository) protected supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
  ) { }
  @get('/supplier-assessment-assignments/{id}/supplier-assignment-submission', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment has one SupplierAssignmentSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(SupplierAssignmentSubmission),
          },
        },
      },
    },
  })
  async get(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<SupplierAssignmentSubmission>,
  ): Promise<SupplierAssignmentSubmission> {
    return this.supplierAssessmentAssignmentRepository.supplierAssignmentSubmission(id).get(filter);
  }

  @post('/supplier-assessment-assignments/{id}/supplier-assignment-submission', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(SupplierAssignmentSubmission)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof SupplierAssessmentAssignment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssignmentSubmission, {
            title: 'NewSupplierAssignmentSubmissionInSupplierAssessmentAssignment',
            exclude: ['id'],
            optional: ['supplierAssessmentAssignmentId']
          }),
        },
      },
    }) supplierAssignmentSubmission: Omit<SupplierAssignmentSubmission, 'id'>,
  ): Promise<SupplierAssignmentSubmission> {
    return this.supplierAssessmentAssignmentRepository.supplierAssignmentSubmission(id).create(supplierAssignmentSubmission);
  }

  @patch('/supplier-assessment-assignments/{id}/supplier-assignment-submission', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment.SupplierAssignmentSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssignmentSubmission, {partial: true}),
        },
      },
    })
    supplierAssignmentSubmission: Partial<SupplierAssignmentSubmission>,
    @param.query.object('where', getWhereSchemaFor(SupplierAssignmentSubmission)) where?: Where<SupplierAssignmentSubmission>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.supplierAssignmentSubmission(id).patch(supplierAssignmentSubmission, where);
  }

  @del('/supplier-assessment-assignments/{id}/supplier-assignment-submission', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment.SupplierAssignmentSubmission DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(SupplierAssignmentSubmission)) where?: Where<SupplierAssignmentSubmission>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.supplierAssignmentSubmission(id).delete(where);
  }
}
