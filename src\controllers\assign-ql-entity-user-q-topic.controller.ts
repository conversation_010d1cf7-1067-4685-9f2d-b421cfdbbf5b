import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignQlEntityUser,
  QTopic,
} from '../models';
import {AssignQlEntityUserRepository} from '../repositories';

export class AssignQlEntityUserQTopicController {
  constructor(
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
  ) { }

  @get('/assign-ql-entity-users/{id}/q-topic', {
    responses: {
      '200': {
        description: 'QTopic belonging to AssignQlEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(QTopic),
          },
        },
      },
    },
  })
  async getQTopic(
    @param.path.number('id') id: typeof AssignQlEntityUser.prototype.id,
  ): Promise<QTopic> {
    return this.assignQlEntityUserRepository.qTopic(id);
  }
}
