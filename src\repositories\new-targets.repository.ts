import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewTargets, NewTargetsRelations} from '../models';

export class NewTargetsRepository extends DefaultCrudRepository<
  NewTargets,
  typeof NewTargets.prototype.id,
  NewTargetsRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(NewTargets, dataSource);
  }
}
