import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDcfSuppliers, AssignDcfSuppliersRelations} from '../models';

export class AssignDcfSuppliersRepository extends DefaultCrudRepository<
  AssignDcfSuppliers,
  typeof AssignDcfSuppliers.prototype.id,
  AssignDcfSuppliersRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AssignDcfSuppliers, dataSource);
  }
}
