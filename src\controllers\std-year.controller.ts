
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {StdYear} from '../models';
import {StdYearRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdYearController {
  constructor(
    @repository(StdYearRepository)
    public stdYearRepository : StdYearRepository,
  ) {}

  @post('/std-years')
  @response(200, {
    description: 'StdYear model instance',
    content: {'application/json': {schema: getModelSchemaRef(StdYear)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdYear, {
            title: 'NewStdYear',
            exclude: ['id'],
          }),
        },
      },
    })
    stdYear: Omit<StdYear, 'id'>,
  ): Promise<StdYear> {
    return this.stdYearRepository.create(stdYear);
  }

  @get('/std-years/count')
  @response(200, {
    description: 'StdYear model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(StdYear) where?: Where<StdYear>,
  ): Promise<Count> {
    return this.stdYearRepository.count(where);
  }

  @get('/std-years')
  @response(200, {
    description: 'Array of StdYear model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(StdYear, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(StdYear) filter?: Filter<StdYear>,
  ): Promise<StdYear[]> {
    return this.stdYearRepository.find(filter);
  }

  // @patch('/std-years')
  // @response(200, {
  //   description: 'StdYear PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StdYear, {partial: true}),
  //       },
  //     },
  //   })
  //   stdYear: StdYear,
  //   @param.where(StdYear) where?: Where<StdYear>,
  // ): Promise<Count> {
  //   return this.stdYearRepository.updateAll(stdYear, where);
  // }

  @get('/std-years/{id}')
  @response(200, {
    description: 'StdYear model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(StdYear, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(StdYear, {exclude: 'where'}) filter?: FilterExcludingWhere<StdYear>
  ): Promise<StdYear> {
    return this.stdYearRepository.findById(id, filter);
  }

  @patch('/std-years/{id}')
  @response(204, {
    description: 'StdYear PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdYear, {partial: true}),
        },
      },
    })
    stdYear: StdYear,
  ): Promise<void> {
    await this.stdYearRepository.updateById(id, stdYear);
  }

  @put('/std-years/{id}')
  @response(204, {
    description: 'StdYear PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() stdYear: StdYear,
  ): Promise<void> {
    await this.stdYearRepository.replaceById(id, stdYear);
  }

  @del('/std-years/{id}')
  @response(204, {
    description: 'StdYear DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.stdYearRepository.deleteById(id);
  }
}
