import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  NewGoals,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';





export class UserProfileNewGoalsController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/new-goals', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many NewGoals',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewGoals)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewGoals>,
  ): Promise<NewGoals[]> {
    return this.userProfileRepository.newGoals(id).find(filter);
  }

  @post('/user-profiles/{id}/new-goals', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewGoals)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewGoals, {
            title: 'NewNewGoalsInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) newGoals: Omit<NewGoals, 'id'>,
  ): Promise<NewGoals> {
    return this.userProfileRepository.newGoals(id).create(newGoals);
  }

  // @patch('/user-profiles/{id}/new-goals', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.NewGoals PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewGoals, {partial: true}),
  //       },
  //     },
  //   })
  //   newGoals: Partial<NewGoals>,
  //   @param.query.object('where', getWhereSchemaFor(NewGoals)) where?: Where<NewGoals>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.newGoals(id).patch(newGoals, where);
  // }

  // @del('/user-profiles/{id}/new-goals', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.NewGoals DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewGoals)) where?: Where<NewGoals>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.newGoals(id).delete(where);
  // }
  
}
