import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, DefaultCrudRepository, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DealerAuditorChecklistSubmission, DealerAuditorChecklistSubmissionRelations, VendorCode} from '../models';
import {VendorCodeRepository} from './vendor-code.repository';

export class DealerAuditorChecklistSubmissionRepository extends DefaultCrudRepository<
  DealerAuditorChecklistSubmission,
  typeof DealerAuditorChecklistSubmission.prototype.id,
  DealerAuditorChecklistSubmissionRelations
> {

  public readonly vendor: BelongsToAccessor<VendorCode, typeof DealerAuditorChecklistSubmission.prototype.id>;



  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>,
  ) {
    super(DealerAuditorChecklistSubmission, dataSource);

    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
  }
}
