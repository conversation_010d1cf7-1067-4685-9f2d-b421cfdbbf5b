import {Entity, model, property} from '@loopback/repository';

@model()
export class ConsolidateFormCollection extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  data1?: string;

  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  tags?: any;
  @property({
    type: 'number',
  })
  type?: number;
  @property({
    type: 'number',
  })
  formType?: number;
  @property({
    type: 'array',
    itemType: 'any',
  })
  data2?: any[];

  @property({
    type: 'string',
  })
  comments?: string;

  @property({
    type: 'string',
  })
  purpose?: string;

  @property({
    type: 'number',
  })
  curator_id?: number;

  @property({
    type: 'number',
  })
  modifier_id?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  updated?: string;


  constructor(data?: Partial<ConsolidateFormCollection>) {
    super(data);
  }
}

export interface ConsolidateFormCollectionRelations {
  // describe navigational properties here
}

export type ConsolidateFormCollectionWithRelations = ConsolidateFormCollection & ConsolidateFormCollectionRelations;
