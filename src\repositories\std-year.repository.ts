import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {StdYear, StdYearRelations, StdName} from '../models';
import {StdNameRepository} from './std-name.repository';

export class StdYearRepository extends DefaultCrudRepository<
  StdYear,
  typeof StdYear.prototype.id,
  StdYearRelations
> {

  public readonly stdNames: HasManyRepositoryFactory<StdName, typeof StdYear.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('StdNameRepository') protected stdNameRepositoryGetter: Getter<StdNameRepository>,
  ) {
    super(StdYear, dataSource);
    this.stdNames = this.createHasManyRepositoryFactoryFor('stdNames', stdNameRepositoryGetter,);
    this.registerInclusionResolver('stdNames', this.stdNames.inclusionResolver);
  }
}
