import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewEfStd,
  GhgCategory,
} from '../models';
import {NewEfStdRepository} from '../repositories';

export class NewEfStdGhgCategoryController {
  constructor(
    @repository(NewEfStdRepository) protected newEfStdRepository: NewEfStdRepository,
  ) { }

  @get('/new-ef-stds/{id}/ghg-categories', {
    responses: {
      '200': {
        description: 'Array of NewEfStd has many GhgCategory',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(GhgCategory)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<GhgCategory>,
  ): Promise<GhgCategory[]> {
    return this.newEfStdRepository.ghgCategories(id).find(filter);
  }

  @post('/new-ef-stds/{id}/ghg-categories', {
    responses: {
      '200': {
        description: 'NewEfStd model instance',
        content: {'application/json': {schema: getModelSchemaRef(GhgCategory)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEfStd.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgCategory, {
            title: 'NewGhgCategoryInNewEfStd',
            exclude: ['id'],
            optional: ['newEfStdId']
          }),
        },
      },
    }) ghgCategory: Omit<GhgCategory, 'id'>,
  ): Promise<GhgCategory> {
    return this.newEfStdRepository.ghgCategories(id).create(ghgCategory);
  }

  @patch('/new-ef-stds/{id}/ghg-categories', {
    responses: {
      '200': {
        description: 'NewEfStd.GhgCategory PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgCategory, {partial: true}),
        },
      },
    })
    ghgCategory: Partial<GhgCategory>,
    @param.query.object('where', getWhereSchemaFor(GhgCategory)) where?: Where<GhgCategory>,
  ): Promise<Count> {
    return this.newEfStdRepository.ghgCategories(id).patch(ghgCategory, where);
  }

  @del('/new-ef-stds/{id}/ghg-categories', {
    responses: {
      '200': {
        description: 'NewEfStd.GhgCategory DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(GhgCategory)) where?: Where<GhgCategory>,
  ): Promise<Count> {
    return this.newEfStdRepository.ghgCategories(id).delete(where);
  }
}
