import {Entity, model, property} from '@loopback/repository';

@model()
export class UserRoleAuthorization extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'number',
  })
  tier1_id?: number;

  @property({
    type: 'any',
  })
  tier2_id?: any;

  @property({
    type: 'any',
  })
  tier3_id?: any;
  @property({
    type: 'array',
    itemType: 'any',
  })
  roles?: any[];
  @property({
    type: 'number',
  })
  user_id?: number;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;




  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;


  constructor(data?: Partial<UserRoleAuthorization>) {
    super(data);
  }
}

export interface UserRoleAuthorizationRelations {
  // describe navigational properties here
}

export type UserRoleAuthorizationWithRelations = UserRoleAuthorization & UserRoleAuthorizationRelations;
