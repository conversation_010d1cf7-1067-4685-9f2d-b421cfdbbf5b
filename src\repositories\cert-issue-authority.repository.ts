import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {CertIssueAuthority, CertIssueAuthorityRelations} from '../models';

export class CertIssueAuthorityRepository extends DefaultCrudRepository<
  CertIssueAuthority,
  typeof CertIssueAuthority.prototype.id,
  CertIssueAuthorityRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(CertIssueAuthority, dataSource);
  }
}
