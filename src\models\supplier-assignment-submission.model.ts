import {belongsTo, Entity, model, property} from '@loopback/repository';
import {VendorCode} from './vendor-code.model';

@model()
export class SupplierAssignmentSubmission extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;



  @property({
    type: 'number',
  })
  type?: number;



  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  response?: string;


  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  created_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  submitted_on?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  submitted_by?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  created_by?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_by?: number | null;


  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierMSIScore?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  status?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  vendorCode?: string | null;

  @property({
    type: 'number',
  })
  userProfileId?: number;


  @property({
    type: 'string',
  })
  supplierAssessmentAssignmentId?: string;

  @belongsTo(() => VendorCode)
  vendorId: number;

  constructor(data?: Partial<SupplierAssignmentSubmission>) {
    super(data);
  }
}

export interface SupplierAssignmentSubmissionRelations {
  // describe navigational properties here
}

export type SupplierAssignmentSubmissionWithRelations = SupplierAssignmentSubmission & SupplierAssignmentSubmissionRelations;
