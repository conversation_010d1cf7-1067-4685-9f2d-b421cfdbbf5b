import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SubmitCf} from '../models';
import {SubmitCfRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SubmitCfController {
  constructor(
    @repository(SubmitCfRepository)
    public submitCfRepository : SubmitCfRepository,
  ) {}

  @post('/submit-cfs')
  @response(200, {
    description: 'SubmitCf model instance',
    content: {'application/json': {schema: getModelSchemaRef(SubmitCf)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitCf, {
            title: 'NewSubmitCf',
            exclude: ['id'],
          }),
        },
      },
    })
    submitCf: Omit<SubmitCf, 'id'>,
  ): Promise<SubmitCf> {
    return this.submitCfRepository.create(submitCf);
  }

  @get('/submit-cfs/count')
  @response(200, {
    description: 'SubmitCf model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SubmitCf) where?: Where<SubmitCf>,
  ): Promise<Count> {
    return this.submitCfRepository.count(where);
  }

  @get('/submit-cfs')
  @response(200, {
    description: 'Array of SubmitCf model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SubmitCf, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SubmitCf) filter?: Filter<SubmitCf>,
  ): Promise<SubmitCf[]> {
    return this.submitCfRepository.find(filter);
  }

  // @patch('/submit-cfs')
  // @response(200, {
  //   description: 'SubmitCf PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitCf, {partial: true}),
  //       },
  //     },
  //   })
  //   submitCf: SubmitCf,
  //   @param.where(SubmitCf) where?: Where<SubmitCf>,
  // ): Promise<Count> {
  //   return this.submitCfRepository.updateAll(submitCf, where);
  // }

  @get('/submit-cfs/{id}')
  @response(200, {
    description: 'SubmitCf model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SubmitCf, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SubmitCf, {exclude: 'where'}) filter?: FilterExcludingWhere<SubmitCf>
  ): Promise<SubmitCf> {
    return this.submitCfRepository.findById(id, filter);
  }

  @patch('/submit-cfs/{id}')
  @response(204, {
    description: 'SubmitCf PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitCf, {partial: true}),
        },
      },
    })
    submitCf: SubmitCf,
  ): Promise<void> {
    await this.submitCfRepository.updateById(id, submitCf);
  }

  @put('/submit-cfs/{id}')
  @response(204, {
    description: 'SubmitCf PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() submitCf: SubmitCf,
  ): Promise<void> {
    await this.submitCfRepository.replaceById(id, submitCf);
  }

  @del('/submit-cfs/{id}')
  @response(204, {
    description: 'SubmitCf DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.submitCfRepository.deleteById(id);
  }
}
