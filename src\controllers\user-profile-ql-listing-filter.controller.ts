import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  QlListingFilter,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class UserProfileQlListingFilterController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/ql-listing-filters', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many QlListingFilter',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QlListingFilter)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QlListingFilter>,
  ): Promise<QlListingFilter[]> {
    return this.userProfileRepository.qlListingFilters(id).find(filter);
  }

  @post('/user-profiles/{id}/ql-listing-filters', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(QlListingFilter)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QlListingFilter, {
            title: 'NewQlListingFilterInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) qlListingFilter: Omit<QlListingFilter, 'id'>,
  ): Promise<QlListingFilter> {
    return this.userProfileRepository.qlListingFilters(id).create(qlListingFilter);
  }

  // @patch('/user-profiles/{id}/ql-listing-filters', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.QlListingFilter PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(QlListingFilter, {partial: true}),
  //       },
  //     },
  //   })
  //   qlListingFilter: Partial<QlListingFilter>,
  //   @param.query.object('where', getWhereSchemaFor(QlListingFilter)) where?: Where<QlListingFilter>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.qlListingFilters(id).patch(qlListingFilter, where);
  // }

  // @del('/user-profiles/{id}/ql-listing-filters', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.QlListingFilter DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(QlListingFilter)) where?: Where<QlListingFilter>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.qlListingFilters(id).delete(where);
  // }
  
}
