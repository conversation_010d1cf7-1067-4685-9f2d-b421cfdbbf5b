import { Entity, model, property } from '@loopback/repository';

@model({ settings: { strict: false } })
export class SubmitDcf extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  dcf?: number;

  @property({
    type: 'number',
  })
  user_type?: number;

  @property({
    type: 'number',
  })
  form_type?: number;

  @property({
    type: 'number',
  })
  site?: number;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  reporting_period?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  response?: any[];

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'any',
  })
  remarks?: any;

  @property({
    type: 'any',
  })
  return_remarks?: any;

  @property({
    type: 'any',
  })
  standard?: any;

  @property({
    type: 'number',
  })
  edit?: number;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];



  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  data2?: string;

  @property({
    type: 'string',
  })
  approved_on?: string;

  @property({
    type: 'string',
  })
  rejected_on?: string;

  @property({
    type: 'number',
  })
  frequencycd?: number;

  @property({
    type: 'number',
  })
  reject?: number;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'number',
  })
  approved_by?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<SubmitDcf>) {
    super(data);
  }
}

export interface SubmitDcfRelations {
  // describe navigational properties here
}

export type SubmitDcfWithRelations = SubmitDcf & SubmitDcfRelations;
