import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewEfSubcategory1} from '../models';
import {NewEfSubcategory1Repository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfSubcategory1Controller {
  constructor(
    @repository(NewEfSubcategory1Repository)
    public newEfSubcategory1Repository : NewEfSubcategory1Repository,
  ) {}

  @post('/new-ef-subcategory1s')
  @response(200, {
    description: 'NewEfSubcategory1 model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEfSubcategory1)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory1, {
            title: 'NewNewEfSubcategory1',
            exclude: ['id'],
          }),
        },
      },
    })
    newEfSubcategory1: Omit<NewEfSubcategory1, 'id'>,
  ): Promise<NewEfSubcategory1> {
    return this.newEfSubcategory1Repository.create(newEfSubcategory1);
  }

  @get('/new-ef-subcategory1s/count')
  @response(200, {
    description: 'NewEfSubcategory1 model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEfSubcategory1) where?: Where<NewEfSubcategory1>,
  ): Promise<Count> {
    return this.newEfSubcategory1Repository.count(where);
  }

  @get('/new-ef-subcategory1s')
  @response(200, {
    description: 'Array of NewEfSubcategory1 model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEfSubcategory1, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEfSubcategory1) filter?: Filter<NewEfSubcategory1>,
  ): Promise<NewEfSubcategory1[]> {
    return this.newEfSubcategory1Repository.find(filter);
  }

  @patch('/new-ef-subcategory1s')
  @response(200, {
    description: 'NewEfSubcategory1 PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory1, {partial: true}),
        },
      },
    })
    newEfSubcategory1: NewEfSubcategory1,
    @param.where(NewEfSubcategory1) where?: Where<NewEfSubcategory1>,
  ): Promise<Count> {
    return this.newEfSubcategory1Repository.updateAll(newEfSubcategory1, where);
  }

  @get('/new-ef-subcategory1s/{id}')
  @response(200, {
    description: 'NewEfSubcategory1 model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEfSubcategory1, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEfSubcategory1, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEfSubcategory1>
  ): Promise<NewEfSubcategory1> {
    return this.newEfSubcategory1Repository.findById(id, filter);
  }

  @patch('/new-ef-subcategory1s/{id}')
  @response(204, {
    description: 'NewEfSubcategory1 PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory1, {partial: true}),
        },
      },
    })
    newEfSubcategory1: NewEfSubcategory1,
  ): Promise<void> {
    await this.newEfSubcategory1Repository.updateById(id, newEfSubcategory1);
  }

  @put('/new-ef-subcategory1s/{id}')
  @response(204, {
    description: 'NewEfSubcategory1 PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEfSubcategory1: NewEfSubcategory1,
  ): Promise<void> {
    await this.newEfSubcategory1Repository.replaceById(id, newEfSubcategory1);
  }

  @del('/new-ef-subcategory1s/{id}')
  @response(204, {
    description: 'NewEfSubcategory1 DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfSubcategory1Repository.deleteById(id);
  }
}
