import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  Ticketing,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileTicketingController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/ticketings', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many Ticketing',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Ticketing)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Ticketing>,
  ): Promise<Ticketing[]> {
    return this.userProfileRepository.ticketings(id).find(filter);
  }

  @post('/user-profiles/{id}/ticketings', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(Ticketing)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ticketing, {
            title: 'NewTicketingInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) ticketing: Omit<Ticketing, 'id'>,
  ): Promise<Ticketing> {
    return this.userProfileRepository.ticketings(id).create(ticketing);
  }

  @patch('/user-profiles/{id}/ticketings', {
    responses: {
      '200': {
        description: 'UserProfile.Ticketing PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ticketing, {partial: true}),
        },
      },
    })
    ticketing: Partial<Ticketing>,
    @param.query.object('where', getWhereSchemaFor(Ticketing)) where?: Where<Ticketing>,
  ): Promise<Count> {
    return this.userProfileRepository.ticketings(id).patch(ticketing, where);
  }

  @del('/user-profiles/{id}/ticketings', {
    responses: {
      '200': {
        description: 'UserProfile.Ticketing DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Ticketing)) where?: Where<Ticketing>,
  ): Promise<Count> {
    return this.userProfileRepository.ticketings(id).delete(where);
  }
}
