import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {TopLevelComponent, TopLevelComponentRelations, ModuleName} from '../models';
import {ModuleNameRepository} from './module-name.repository';

export class TopLevelComponentRepository extends DefaultCrudRepository<
  TopLevelComponent,
  typeof TopLevelComponent.prototype.id,
  TopLevelComponentRelations
> {

  public readonly moduleNames: HasManyRepositoryFactory<ModuleName, typeof TopLevelComponent.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ModuleNameRepository') protected moduleNameRepositoryGetter: Getter<ModuleNameRepository>,
  ) {
    super(TopLevelComponent, dataSource);
    this.moduleNames = this.createHasManyRepositoryFactoryFor('moduleNames', moduleNameRepositoryGetter,);
    this.registerInclusionResolver('moduleNames', this.moduleNames.inclusionResolver);
  }
}
