import {Entity, model, property, hasOne, hasMany, belongsTo} from '@loopback/repository';
import {SaveSurvey} from './save-survey.model';
import {Response} from './response.model';
import {Category} from './category.model';
import {SubSurvey} from './sub-survey.model';


@model({settings: {strict: false}})
export class Survey extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  introduction?: string;


  @property({
    type: 'array',
    itemType: 'object'
  })
  category_selected?: object[];

  @property({
    type: 'string',
  })
  status?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @hasOne(() => SaveSurvey)
  saveSurvey: SaveSurvey;

  @hasMany(() => Response)
  responses: Response[];

  @property({
    type: 'number',
  })
  surveyTitleId?: number;

  @belongsTo(() => Category)
  categoryId: number;

  @hasMany(() => SubSurvey)
  subSurveys: SubSurvey[];

  @hasMany(() => Survey)
  surveys: Survey[];

  @property({
    type: 'number',
  })
  surveyId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Survey>) {
    super(data);
  }
}

export interface SurveyRelations {
  // describe navigational properties here
}

export type SurveyWithRelations = Survey & SurveyRelations;
