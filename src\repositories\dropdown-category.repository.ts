import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DdCategoryOne, DropDownCategory, DropDownCategoryRelations} from '../models';
import {DdCategoryOneRepository} from './dd-category-one.repository';

export class DropDownCategoryRepository extends DefaultCrudRepository<
  DropDownCategory,
  typeof DropDownCategory.prototype.id,
  DropDownCategoryRelations
> {

  public readonly ddCategoryOnes: HasManyRepositoryFactory<DdCategoryOne, typeof DropDownCategory.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('DdCategoryOneRepository') protected ddCategoryOneRepositoryGetter: Getter<DdCategoryOneRepository>,
  ) {
    super(DropDownCategory, dataSource);
    this.ddCategoryOnes = this.createHasManyRepositoryFactoryFor('ddCategoryOnes', ddCategoryOneRepositoryGetter,);
    this.registerInclusionResolver('ddCategoryOnes', this.ddCategoryOnes.inclusionResolver);
  }
}
