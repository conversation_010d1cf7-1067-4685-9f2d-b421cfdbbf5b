import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SapHazardous, SapHazardousRelations} from '../models';

export class SapHazardousRepository extends DefaultCrudRepository<
  SapHazardous,
  typeof SapHazardous.prototype.id,
  SapHazardousRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SapHazardous, dataSource);
  }
}
