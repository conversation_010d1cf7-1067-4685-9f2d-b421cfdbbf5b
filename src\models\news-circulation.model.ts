import {Entity, model, property} from '@loopback/repository';

@model()
export class NewsCirculation extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'any',
  })
  message?: any;
  @property({
    type: 'string',
  })
  title?: string;
  @property({
    type: 'any',
  })
  expiryDate?: any;

  @property({
    type: 'boolean',
  })
  pin?: boolean;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  status?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<NewsCirculation>) {
    super(data);
  }
}

export interface NewsCirculationRelations {
  // describe navigational properties here
}

export type NewsCirculationWithRelations = NewsCirculation & NewsCirculationRelations;
