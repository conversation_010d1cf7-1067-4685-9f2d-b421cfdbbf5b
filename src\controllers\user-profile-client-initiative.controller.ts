import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  ClientInitiative,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileClientInitiativeController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/client-initiatives', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many ClientInitiative',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ClientInitiative)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ClientInitiative>,
  ): Promise<ClientInitiative[]> {
    return this.userProfileRepository.clientInitiatives(id).find(filter);
  }

  @post('/user-profiles/{id}/client-initiatives', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(ClientInitiative)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientInitiative, {
            title: 'NewClientInitiativeInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) clientInitiative: Omit<ClientInitiative, 'id'>,
  ): Promise<ClientInitiative> {
    return this.userProfileRepository.clientInitiatives(id).create(clientInitiative);
  }

  @patch('/user-profiles/{id}/client-initiatives', {
    responses: {
      '200': {
        description: 'UserProfile.ClientInitiative PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientInitiative, {partial: true}),
        },
      },
    })
    clientInitiative: Partial<ClientInitiative>,
    @param.query.object('where', getWhereSchemaFor(ClientInitiative)) where?: Where<ClientInitiative>,
  ): Promise<Count> {
    return this.userProfileRepository.clientInitiatives(id).patch(clientInitiative, where);
  }

  @del('/user-profiles/{id}/client-initiatives', {
    responses: {
      '200': {
        description: 'UserProfile.ClientInitiative DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(ClientInitiative)) where?: Where<ClientInitiative>,
  ): Promise<Count> {
    return this.userProfileRepository.clientInitiatives(id).delete(where);
  }
}
