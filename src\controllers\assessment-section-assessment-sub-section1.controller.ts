import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AssessmentSection,
  AssessmentSubSection1,
} from '../models';
import {AssessmentSectionRepository} from '../repositories';

export class AssessmentSectionAssessmentSubSection1Controller {
  constructor(
    @repository(AssessmentSectionRepository) protected assessmentSectionRepository: AssessmentSectionRepository,
  ) { }

  @get('/assessment-sections/{id}/assessment-sub-section1s', {
    responses: {
      '200': {
        description: 'Array of AssessmentSection has many AssessmentSubSection1',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssessmentSubSection1)},
          },
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<AssessmentSubSection1>,
  ): Promise<AssessmentSubSection1[]> {
    return this.assessmentSectionRepository.assessmentSubSection1s(id).find(filter);
  }

  @post('/assessment-sections/{id}/assessment-sub-section1s', {
    responses: {
      '200': {
        description: 'AssessmentSection model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssessmentSubSection1)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof AssessmentSection.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection1, {
            title: 'NewAssessmentSubSection1InAssessmentSection',
            exclude: ['id'],
            optional: ['assessmentSectionId']
          }),
        },
      },
    }) assessmentSubSection1: Omit<AssessmentSubSection1, 'id'>,
  ): Promise<AssessmentSubSection1> {
    return this.assessmentSectionRepository.assessmentSubSection1s(id).create(assessmentSubSection1);
  }

  @patch('/assessment-sections/{id}/assessment-sub-section1s', {
    responses: {
      '200': {
        description: 'AssessmentSection.AssessmentSubSection1 PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection1, {partial: true}),
        },
      },
    })
    assessmentSubSection1: Partial<AssessmentSubSection1>,
    @param.query.object('where', getWhereSchemaFor(AssessmentSubSection1)) where?: Where<AssessmentSubSection1>,
  ): Promise<Count> {
    return this.assessmentSectionRepository.assessmentSubSection1s(id).patch(assessmentSubSection1, where);
  }

  @del('/assessment-sections/{id}/assessment-sub-section1s', {
    responses: {
      '200': {
        description: 'AssessmentSection.AssessmentSubSection1 DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(AssessmentSubSection1)) where?: Where<AssessmentSubSection1>,
  ): Promise<Count> {
    return this.assessmentSectionRepository.assessmentSubSection1s(id).delete(where);
  }
}
