import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEfSubcategory3, NewEfSubcategory3Relations, NewEfSubcategory4} from '../models';
import {NewEfSubcategory4Repository} from './new-ef-subcategory4.repository';

export class NewEfSubcategory3Repository extends DefaultCrudRepository<
  NewEfSubcategory3,
  typeof NewEfSubcategory3.prototype.id,
  NewEfSubcategory3Relations
> {

  public readonly newEfSubcategory4s: HasManyRepositoryFactory<NewEfSubcategory4, typeof NewEfSubcategory3.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfSubcategory4Repository') protected newEfSubcategory4RepositoryGetter: Getter<NewEfSubcategory4Repository>,
  ) {
    super(NewEfSubcategory3, dataSource);
    this.newEfSubcategory4s = this.createHasManyRepositoryFactoryFor('newEfSubcategory4s', newEfSubcategory4RepositoryGetter,);
    this.registerInclusionResolver('newEfSubcategory4s', this.newEfSubcategory4s.inclusionResolver);
  }
}
