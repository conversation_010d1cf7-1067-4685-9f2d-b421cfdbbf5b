import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ScopeOne} from '../models';
import {ScopeOneRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ScopeOneController {
  constructor(
    @repository(ScopeOneRepository)
    public scopeOneRepository : ScopeOneRepository,
  ) {}

  @post('/scope-ones')
  @response(200, {
    description: 'ScopeOne model instance',
    content: {'application/json': {schema: getModelSchemaRef(ScopeOne)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeOne, {
            title: 'NewScopeOne',
            exclude: ['id'],
          }),
        },
      },
    })
    scopeOne: Omit<ScopeOne, 'id'>,
  ): Promise<ScopeOne> {
    return this.scopeOneRepository.create(scopeOne);
  }

  @get('/scope-ones/count')
  @response(200, {
    description: 'ScopeOne model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ScopeOne) where?: Where<ScopeOne>,
  ): Promise<Count> {
    return this.scopeOneRepository.count(where);
  }

  @get('/scope-ones')
  @response(200, {
    description: 'Array of ScopeOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ScopeOne, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ScopeOne) filter?: Filter<ScopeOne>,
  ): Promise<ScopeOne[]> {
    return this.scopeOneRepository.find(filter);
  }

  // @patch('/scope-ones')
  // @response(200, {
  //   description: 'ScopeOne PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ScopeOne, {partial: true}),
  //       },
  //     },
  //   })
  //   scopeOne: ScopeOne,
  //   @param.where(ScopeOne) where?: Where<ScopeOne>,
  // ): Promise<Count> {
  //   return this.scopeOneRepository.updateAll(scopeOne, where);
  // }

  @get('/scope-ones/{id}')
  @response(200, {
    description: 'ScopeOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ScopeOne, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ScopeOne, {exclude: 'where'}) filter?: FilterExcludingWhere<ScopeOne>
  ): Promise<ScopeOne> {
    return this.scopeOneRepository.findById(id, filter);
  }

  @patch('/scope-ones/{id}')
  @response(204, {
    description: 'ScopeOne PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ScopeOne, {partial: true}),
        },
      },
    })
    scopeOne: ScopeOne,
  ): Promise<void> {
    await this.scopeOneRepository.updateById(id, scopeOne);
  }

  @put('/scope-ones/{id}')
  @response(204, {
    description: 'ScopeOne PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() scopeOne: ScopeOne,
  ): Promise<void> {
    await this.scopeOneRepository.replaceById(id, scopeOne);
  }

  @del('/scope-ones/{id}')
  @response(204, {
    description: 'ScopeOne DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.scopeOneRepository.deleteById(id);
  }
}
