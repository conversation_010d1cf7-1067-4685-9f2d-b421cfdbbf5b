import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SupplierAction, SupplierActionRelations, VendorCode, AssessmentSubSection2, SupplierActionHistory} from '../models';
import {VendorCodeRepository} from './vendor-code.repository';
import {AssessmentSubSection2Repository} from './assessment-sub-section2.repository';
import {SupplierActionHistoryRepository} from './supplier-action-history.repository';

export class SupplierActionRepository extends DefaultCrudRepository<
  SupplierAction,
  typeof SupplierAction.prototype.id,
  SupplierActionRelations
> {

  public readonly vendorCode: BelongsToAccessor<VendorCode, typeof SupplierAction.prototype.id>;

  public readonly assessmentSubSection2: BelongsToAccessor<AssessmentSubSection2, typeof SupplierAction.prototype.id>;

  public readonly supplierActionHistories: HasManyRepositoryFactory<SupplierActionHistory, typeof SupplierAction.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>, @repository.getter('AssessmentSubSection2Repository') protected assessmentSubSection2RepositoryGetter: Getter<AssessmentSubSection2Repository>, @repository.getter('SupplierActionHistoryRepository') protected supplierActionHistoryRepositoryGetter: Getter<SupplierActionHistoryRepository>,
  ) {
    super(SupplierAction, dataSource);
    this.supplierActionHistories = this.createHasManyRepositoryFactoryFor('supplierActionHistories', supplierActionHistoryRepositoryGetter,);
    this.registerInclusionResolver('supplierActionHistories', this.supplierActionHistories.inclusionResolver);
    this.assessmentSubSection2 = this.createBelongsToAccessorFor('assessmentSubSection2', assessmentSubSection2RepositoryGetter,);
    this.registerInclusionResolver('assessmentSubSection2', this.assessmentSubSection2.inclusionResolver);
    this.vendorCode = this.createBelongsToAccessorFor('vendorCode', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendorCode', this.vendorCode.inclusionResolver);
  }
}
