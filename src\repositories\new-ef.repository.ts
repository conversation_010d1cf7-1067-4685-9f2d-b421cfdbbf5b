import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEf, NewEfRelations, NewEfItem} from '../models';
import {NewEfItemRepository} from './new-ef-item.repository';

export class NewEfRepository extends DefaultCrudRepository<
  NewEf,
  typeof NewEf.prototype.id,
  NewEfRelations
> {

  public readonly newEfItems: HasManyRepositoryFactory<NewEfItem, typeof NewEf.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfItemRepository') protected newEfItemRepositoryGetter: Getter<NewEfItemRepository>,
  ) {
    super(NewEf, dataSource);
    this.newEfItems = this.createHasManyRepositoryFactoryFor('newEfItems', newEfItemRepositoryGetter,);
    this.registerInclusionResolver('newEfItems', this.newEfItems.inclusionResolver);
  }
}
