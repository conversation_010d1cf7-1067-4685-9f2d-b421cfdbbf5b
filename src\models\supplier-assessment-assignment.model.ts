import {belongsTo, Entity, hasMany, hasOne, model, property} from '@loopback/repository';
import {AuditorAssignmentSubmission} from './auditor-assignment-submission.model';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';
import {SupplierAction} from './supplier-action.model';
import {SupplierAssignmentSubmission} from './supplier-assignment-submission.model';
import {SupplierSectionSubmission} from './supplier-section-submission.model';
import {UserProfile} from './user-profile.model';
import {VendorCode} from './vendor-code.model';

@model()
export class SupplierAssessmentAssignment extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;
  @property({
    type: 'array',
    itemType: 'number'
  })
  auditor_ids?: number[];

  @belongsTo(() => ConsolidateFormCollection, {name: 'srf'})

  srfId?: number;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  assessmentStartDate?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  assessmentEndDate?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  auditStartDate?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  auditEndDate?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  supplierMSIScore?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  auditorMSIScore?: number | null;

  @property({
    type: 'string',
  })
  supplierSubmittedOn?: string;
  @property({
    type: 'string',
  })
  auditorSubmittedOn?: string;
  @property({
    type: 'string',
  })
  supplierModifiedOn?: string;
  @property({
    type: 'string',
  })
  auditorModifiedOn?: string;

  @property({
    type: 'number',
  })
  supplierSubmittedBy?: number;
  @property({
    type: 'number',
  })
  auditorSubmittedBy?: number;
  @property({
    type: 'number',
  })
  supplierModifiedBy?: number;
  @property({
    type: 'number',
  })
  auditorModifiedBy?: number;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  vendorCode?: string | null;

  @property({
    type: 'number'
  })
  created_by?: number;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_by?: number | null;

  @property({
    type: 'boolean',
    jsonSchema: {
      nullable: true,
    }
  })
  active?: boolean | null;

  @property({
    type: 'any',
  })
  attachment1?: any;

  @property({
    type: 'any',
  })
  attachment2?: any;
  @property({
    type: 'any',
  })
  attachment3?: any;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanType?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanSubmittedDate?: string | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanApprovedDate?: string | null;
  @property({
    type: 'any',
    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      anyOf: [
        {
          type: 'array',
          items: {
            type: 'integer'
          }
        },
        {
          type: 'null'
        }
      ]
    }
  })
  reviewer_ids?: number[] | null;

  @property({
    type: 'object',
    default: null,
    required: false,
  })

  group1: {
    sections: (number | null)[];
    assessors: (number | null)[];
    updated_on: string;
    updated_by: any;
  } | null;
  @property({
    type: 'object',
    default: null,
    required: false,
  })
  group2: {
    sections: (number | null)[];
    assessors: (number | null)[];
    updated_on: string;
    updated_by: any;
  } | null;
  @property({
    type: 'object',
    default: null,
    required: false,
  })
  group3: {
    sections: (number | null)[];
    assessors: (number | null)[];
    updated_on: string;
    updated_by: any;
  } | null;
  @property({
    type: 'object',
    default: null,
    required: false,
  })
  group4: {
    sections: (number | null)[];
    assessors: (number | null)[];
    updated_on: string;
    updated_by: any;
  } | null;


  @hasOne(() => SupplierAssignmentSubmission)
  supplierAssignmentSubmission: SupplierAssignmentSubmission;

  @hasOne(() => AuditorAssignmentSubmission)
  auditorAssignmentSubmission: AuditorAssignmentSubmission;

  @hasMany(() => SupplierSectionSubmission)
  supplierSectionSubmissions: SupplierSectionSubmission[];

  @hasMany(() => SupplierAction)
  supplierActions: SupplierAction[];

  @belongsTo(() => UserProfile)
  supplierId: number;

  @belongsTo(() => VendorCode)
  vendorId: number;

  constructor(data?: Partial<SupplierAssessmentAssignment>) {
    super(data);
  }
}

export interface SupplierAssessmentAssignmentRelations {
  // describe navigational properties here
}

export type SupplierAssessmentAssignmentWithRelations = SupplierAssessmentAssignment & SupplierAssessmentAssignmentRelations;
