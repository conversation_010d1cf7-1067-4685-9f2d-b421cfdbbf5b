import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewEfCategory} from '../models';
import {NewEfCategoryRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfCategoryController {
  constructor(
    @repository(NewEfCategoryRepository)
    public newEfCategoryRepository : NewEfCategoryRepository,
  ) {}

  @post('/new-ef-categories')
  @response(200, {
    description: 'NewEfCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEfCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfCategory, {
            title: 'NewNewEfCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    newEfCategory: Omit<NewEfCategory, 'id'>,
  ): Promise<NewEfCategory> {
    return this.newEfCategoryRepository.create(newEfCategory);
  }

  @get('/new-ef-categories/count')
  @response(200, {
    description: 'NewEfCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEfCategory) where?: Where<NewEfCategory>,
  ): Promise<Count> {
    return this.newEfCategoryRepository.count(where);
  }

  @get('/new-ef-categories')
  @response(200, {
    description: 'Array of NewEfCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEfCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEfCategory) filter?: Filter<NewEfCategory>,
  ): Promise<NewEfCategory[]> {
    return this.newEfCategoryRepository.find(filter);
  }

  // @patch('/new-ef-categories')
  // @response(200, {
  //   description: 'NewEfCategory PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfCategory, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfCategory: NewEfCategory,
  //   @param.where(NewEfCategory) where?: Where<NewEfCategory>,
  // ): Promise<Count> {
  //   return this.newEfCategoryRepository.updateAll(newEfCategory, where);
  // }

  @get('/new-ef-categories/{id}')
  @response(200, {
    description: 'NewEfCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEfCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEfCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEfCategory>
  ): Promise<NewEfCategory> {
    return this.newEfCategoryRepository.findById(id, filter);
  }

  @patch('/new-ef-categories/{id}')
  @response(204, {
    description: 'NewEfCategory PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfCategory, {partial: true}),
        },
      },
    })
    newEfCategory: NewEfCategory,
  ): Promise<void> {
    await this.newEfCategoryRepository.updateById(id, newEfCategory);
  }

  @put('/new-ef-categories/{id}')
  @response(204, {
    description: 'NewEfCategory PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEfCategory: NewEfCategory,
  ): Promise<void> {
    await this.newEfCategoryRepository.replaceById(id, newEfCategory);
  }

  @del('/new-ef-categories/{id}')
  @response(204, {
    description: 'NewEfCategory DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfCategoryRepository.deleteById(id);
  }
}
