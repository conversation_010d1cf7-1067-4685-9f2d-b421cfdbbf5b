import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  QnIndicatorApproval,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileQnIndicatorApprovalController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/qn-indicator-approvals', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many QnIndicatorApproval',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QnIndicatorApproval)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QnIndicatorApproval>,
  ): Promise<QnIndicatorApproval[]> {
    return this.userProfileRepository.qnIndicatorApprovals(id).find(filter);
  }

  @post('/user-profiles/{id}/qn-indicator-approvals', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(QnIndicatorApproval)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QnIndicatorApproval, {
            title: 'NewQnIndicatorApprovalInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) qnIndicatorApproval: Omit<QnIndicatorApproval, 'id'>,
  ): Promise<QnIndicatorApproval> {
    return this.userProfileRepository.qnIndicatorApprovals(id).create(qnIndicatorApproval);
  }

  @patch('/user-profiles/{id}/qn-indicator-approvals', {
    responses: {
      '200': {
        description: 'UserProfile.QnIndicatorApproval PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QnIndicatorApproval, {partial: true}),
        },
      },
    })
    qnIndicatorApproval: Partial<QnIndicatorApproval>,
    @param.query.object('where', getWhereSchemaFor(QnIndicatorApproval)) where?: Where<QnIndicatorApproval>,
  ): Promise<Count> {
    return this.userProfileRepository.qnIndicatorApprovals(id).patch(qnIndicatorApproval, where);
  }

  @del('/user-profiles/{id}/qn-indicator-approvals', {
    responses: {
      '200': {
        description: 'UserProfile.QnIndicatorApproval DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(QnIndicatorApproval)) where?: Where<QnIndicatorApproval>,
  ): Promise<Count> {
    return this.userProfileRepository.qnIndicatorApprovals(id).delete(where);
  }
}
