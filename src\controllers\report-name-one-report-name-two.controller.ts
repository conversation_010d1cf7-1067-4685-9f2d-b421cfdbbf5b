import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  ReportNameOne,
  ReportNameTwo,
} from '../models';
import {ReportNameOneRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ReportNameOneReportNameTwoController {
  constructor(
    @repository(ReportNameOneRepository) protected reportNameOneRepository: ReportNameOneRepository,
  ) { }

  @get('/report-name-ones/{id}/report-name-twos', {
    responses: {
      '200': {
        description: 'Array of ReportNameOne has many ReportNameTwo',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ReportNameTwo)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ReportNameTwo>,
  ): Promise<ReportNameTwo[]> {
    return this.reportNameOneRepository.reportNameTwos(id).find(filter);
  }

  @post('/report-name-ones/{id}/report-name-twos', {
    responses: {
      '200': {
        description: 'ReportNameOne model instance',
        content: {'application/json': {schema: getModelSchemaRef(ReportNameTwo)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof ReportNameOne.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportNameTwo, {
            title: 'NewReportNameTwoInReportNameOne',
            exclude: ['id'],
            optional: ['reportNameOneId']
          }),
        },
      },
    }) reportNameTwo: Omit<ReportNameTwo, 'id'>,
  ): Promise<ReportNameTwo> {
    return this.reportNameOneRepository.reportNameTwos(id).create(reportNameTwo);
  }

  // @patch('/report-name-ones/{id}/report-name-twos', {
  //   responses: {
  //     '200': {
  //       description: 'ReportNameOne.ReportNameTwo PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ReportNameTwo, {partial: true}),
  //       },
  //     },
  //   })
  //   reportNameTwo: Partial<ReportNameTwo>,
  //   @param.query.object('where', getWhereSchemaFor(ReportNameTwo)) where?: Where<ReportNameTwo>,
  // ): Promise<Count> {
  //   return this.reportNameOneRepository.reportNameTwos(id).patch(reportNameTwo, where);
  // }

  // @del('/report-name-ones/{id}/report-name-twos', {
  //   responses: {
  //     '200': {
  //       description: 'ReportNameOne.ReportNameTwo DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(ReportNameTwo)) where?: Where<ReportNameTwo>,
  // ): Promise<Count> {
  //   return this.reportNameOneRepository.reportNameTwos(id).delete(where);
  // }
}
