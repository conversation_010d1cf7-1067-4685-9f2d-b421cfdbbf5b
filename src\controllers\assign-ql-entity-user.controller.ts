import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {AssignQlEntityUser} from '../models';
import {AssignQlEntityRepository, AssignQlEntityUserRepository, LocationOneRepository, LocationThreeRepository, LocationTwoRepository, QSectionRepository, QTopicRepository, UserProfileRepository} from '../repositories';
import {Helper} from '../services';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';
import {UserRoleAuthorizationController} from './user-role-authorization.controller';

export class AssignQlEntityUserController {
  constructor(
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @inject('services.HelperProvider')
    public helper: Helper,
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @repository(QTopicRepository)
    public qTopicRepository: QTopicRepository,
    @repository(QSectionRepository)
    public qSectionRepository: QSectionRepository,
    @inject('controllers.UserRoleAuthorizationController')
    public userRoleAuthorizationController: UserRoleAuthorizationController
  ) { }

  @post('/assign-ql-entity-users')
  @response(200, {
    description: 'AssignQlEntityUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignQlEntityUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntityUser, {
            title: 'NewAssignQlEntityUser',
            exclude: ['id'],
          }),
        },
      },
    })
    assignQlEntityUser: Omit<AssignQlEntityUser, 'id'>,
  ): Promise<AssignQlEntityUser> {
    return this.assignQlEntityUserRepository.create(assignQlEntityUser);
  }

  @get('/assign-ql-entity-users/count')
  @response(200, {
    description: 'AssignQlEntityUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignQlEntityUser) where?: Where<AssignQlEntityUser>,
  ): Promise<Count> {
    return this.assignQlEntityUserRepository.count(where);
  }

  @get('/assign-ql-entity-users')
  @response(200, {
    description: 'Array of AssignQlEntityUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignQlEntityUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignQlEntityUser) filter?: Filter<AssignQlEntityUser>,
  ): Promise<AssignQlEntityUser[]> {
    return this.assignQlEntityUserRepository.find(filter);
  }

  @patch('/assign-ql-entity-users')
  @response(200, {
    description: 'AssignQlEntityUser PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntityUser, {partial: true}),
        },
      },
    })
    assignQlEntityUser: AssignQlEntityUser,
    @param.where(AssignQlEntityUser) where?: Where<AssignQlEntityUser>,
  ): Promise<Count> {
    return this.assignQlEntityUserRepository.updateAll(assignQlEntityUser, where);
  }

  @get('/assign-ql-entity-users/{id}')
  @response(200, {
    description: 'AssignQlEntityUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignQlEntityUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignQlEntityUser, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignQlEntityUser>
  ): Promise<AssignQlEntityUser> {
    return this.assignQlEntityUserRepository.findById(id, filter);
  }

  @patch('/assign-ql-entity-users/{id}')
  @response(204, {
    description: 'AssignQlEntityUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntityUser, {partial: true}),
        },
      },
    })
    assignQlEntityUser: AssignQlEntityUser,
  ): Promise<void> {
    // Step 1: Get old assignment data
    const oldData = await this.assignQlEntityUserRepository.findById(id);
    await this.assignQlEntityUserRepository.updateById(id, assignQlEntityUser);

    if (assignQlEntityUser.reporter_ids) {
      // Step 2: Determine new reporters
      const oldReporters = oldData.reporter_ids || [];
      const newReporters = assignQlEntityUser.reporter_ids || [];
      const addedReporters = newReporters.filter(id => !oldReporters.includes(id));

      const entityAssignment = await this.assignQlEntityRepository.findById(oldData.entityAssId)



      // consolidation list
      const consolidatorData = await this.userProfileController.filteredUP({where: {id: {inq: entityAssignment?.consolidator_ids || []}}})

      // Step 4: Fetch user info
      const addedReporterProfiles = await this.userProfileController.filteredUP({
        where: {id: {inq: addedReporters}}
      });


      const topic = (await this.qTopicRepository.findById(assignQlEntityUser.qTopicId || oldData.qTopicId)).name;
      const section = (await this.qSectionRepository.findById(assignQlEntityUser.qSectionId || oldData.qSectionId)).name;
      const adminObj = await this.userProfileRepository.findById(oldData.userProfileId);

      // Step 5: Determine entity name
      const level = assignQlEntityUser.level ?? oldData.level;
      const locationId = assignQlEntityUser.locationId ?? oldData.locationId;

      let entity: any = '';
      if (level === 0) {
        entity = 'Corporate'
      } else if (level === 1) {
        entity = (await this.locationOneRepository.findById(locationId)).name || 'NA'
      } else if (level === 2) {
        entity = (await this.locationTwoRepository.findById(locationId)).name || 'NA'
      } else if (level === 3) {
        entity = (await this.locationThreeRepository.findById(locationId)).name || 'NA'
      }



      // Combine added reporters and consolidators into one array with roles
      const addedUsers: {user: any, role: 'Reporter' | 'Consolidator'}[] = [];

      for (const user of addedReporterProfiles) {
        addedUsers.push({user, role: 'Reporter'});
      }


      // Create subject and body per user
      for (const {user, role} of addedUsers) {
        const body = `
  <p><strong>Dear ${user?.information?.empname || 'User'},</strong></p>
  <p style="margin: 5px 0px;">You have been assigned as the <strong>Data Reporter</strong> for the qualitative section
    <strong>"${section}"</strong> under the topic <strong>"${topic}"</strong> for the reporting entity
<strong>"${entity}"</strong> on the Navigos ESG platform.
  </p>

   <p style="margin: 5px 0px;"><strong>Please find the assignment details below:</strong></p>
    <ul>
      <li><strong>Assigned Consolidator(s):</strong> ${consolidatorData.map((x: any) => x.email).filter((x: any) => x)}</li>
       <li><strong>Submission Due Date:</strong> ${DateTime.fromISO(assignQlEntityUser.due_date || oldData.due_date || '', {zone: 'utc'}).plus({day: 1}).toFormat('dd-LLL-yyyy')}</li>

    </ul>

 ${adminObj?.userPortalUrl ? `<p style="margin: 5px 0px;">
      To access the form and begin your response, please log in to the platform using the following link:
      <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform</a>
    </p>` : ''}

    <p style="margin: 5px 0px;">
      If you have any questions or require assistance, you may raise a ticket through the platform or
      contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
    </p>

    <p style="margin: 5px 0px;">Thank you for your cooperation.</p>


`;

        const subject = `Assignment of Sustainability Qualitative Response Form – Navigos`;

        // try {
        //   const info = await this.sqsService.sendEmail(user.email, subject, body, []).then((info) => {
        //     console.log('mail sent')

        //   }).catch((err) => {
        //     console.log('error in sending')

        //   })
        // } catch (error) {
        //   console.error(`Error sending email to ${role}:`, error);
        //   throw new Error('Failed to send email');
        // }

        console.log(subject);
        console.log(body);
      }

    }


  }

  @put('/assign-ql-entity-users/{id}')
  @response(204, {
    description: 'AssignQlEntityUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignQlEntityUser: AssignQlEntityUser,
  ): Promise<void> {
    await this.assignQlEntityUserRepository.replaceById(id, assignQlEntityUser);
  }

  @del('/assign-ql-entity-users/{id}')
  @response(204, {
    description: 'AssignQlEntityUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignQlEntityUserRepository.deleteById(id);
  }

  @post('/assigned-ql-entity-users')
  @response(200, {
    description: 'AssignQlEntityUser  success',
  })
  async getAssignmentById(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            properties: {
              userId: {type: 'number'},
              userProfileId: {type: 'number'}
            }
          }
        },
      },
    })
    queryData: {userId: number, userProfileId: number},
  ): Promise<any> {
    const {userProfileId, userId} = queryData

    const entityAssignment = await this.userProfileRepository.assignQlEntities(userProfileId).find()
    if (entityAssignment.length) {
      const entityUserAssignment = (await this.userProfileRepository.assignQlEntityUsers(userProfileId).find({include: ['qCategory', 'qTopic', {relation: 'qSection', scope: {include: ['srf']}}]})).filter(x => x.reporter_ids?.includes(userId))
      const locations = await this.userProfileRepository.locationOnes(userProfileId).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})

      const locations0 = [0]
      const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
      const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
      const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
      const locationMap: any = {
        0: locations0,
        1: locations1,
        2: locations2,
        3: locations3
      };
      const filteredAssignments = entityUserAssignment.filter((assignment) => {
        return entityAssignment.some((ent: any) => {
          const tierKey = `tier${assignment.level}_ids`;
          const validLocations = locationMap[assignment?.level || 0] || [];

          // Filter out invalid IDs from ent[tierKey]
          const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
            validLocations.includes(Number(id))
          );

          // Check if assignment.locationId is present in the filtered IDs
          const isLocationMatch = filteredTierIds.includes(Number(assignment.locationId));

          const isBasicMatch =
            ent.qCategoryId === assignment.qCategoryId &&
            ent.qTopicId === assignment.qTopicId &&
            ent.qSectionId === assignment.qSectionId;
          return isBasicMatch && isLocationMatch;
        });
      });

      const roleAssignment = await this.userRoleAuthorizationController.getUsersByRolesDynamic(userProfileId, {roles: [1]});

      const [reporters] = roleAssignment;


      for (const item of filteredAssignments.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf)) {
        item.reporter_ids = item.reporter_ids?.filter((x: any) => reporters.includes(x) || x === userProfileId);
      }
      return filteredAssignments.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf);

    } else {
      return []
    }


  }
  @post('/assigned-ql-entity-consolidators')
  @response(200, {
    description: 'AssignQlEntityUser  success',
  })
  async getConsolidatorAssignmentById(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            properties: {
              userId: {type: 'number'},
              userProfileId: {type: 'number'}
            }
          }
        },
      },
    })
    queryData: {userId: number, userProfileId: number},
  ): Promise<any> {
    const {userProfileId, userId} = queryData

    const entityAssignment = await this.userProfileRepository.assignQlEntities(userProfileId).find({include: ['qCategory', 'qTopic', {relation: 'qSection', scope: {include: ['srf']}}]})
    const filteredEntityAssignment = entityAssignment.filter(x => userId === 0 || (x.consolidator_ids && x.consolidator_ids?.includes(userId)))
    if (filteredEntityAssignment.length) {
      const entityUserAssignment = (await this.userProfileRepository.assignQlEntityUsers(userProfileId).find({include: ['qCategory', 'qTopic', {relation: 'qSection', scope: {include: ['srf']}}]})).filter(x => filteredEntityAssignment.map(x => x.id).includes(x.entityAssId)).filter(x => (userId === 0 || x.reporter_ids?.includes(userId)))
      const locations = await this.userProfileRepository.locationOnes(userProfileId).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})

      const locations0 = [0]
      const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
      const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
      const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
      const locationMap: any = {
        0: locations0,
        1: locations1,
        2: locations2,
        3: locations3
      };
      const filteredAssignments = entityUserAssignment.filter((assignment) => {
        return filteredEntityAssignment.some((ent: any) => {
          const tierKey = `tier${assignment.level}_ids`;
          const validLocations = locationMap[assignment?.level || 0] || [];

          // Filter out invalid IDs from ent[tierKey]
          const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
            validLocations.includes(Number(id))
          );



          // Check if assignment.locationId is present in the filtered IDs
          const isLocationMatch = filteredTierIds.includes(Number(assignment.locationId));

          const isBasicMatch =
            ent.qCategoryId === assignment.qCategoryId &&
            ent.qTopicId === assignment.qTopicId &&
            ent.qSectionId === assignment.qSectionId;
          return isBasicMatch && isLocationMatch;
        });
      });
      const roleAssignment = await this.userRoleAuthorizationController.getUsersByRolesDynamic(userProfileId, {roles: [1, 3]});

      const [reporters, approvers] = roleAssignment;

      for (const item of filteredEntityAssignment.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf)) {
        item.consolidator_ids = item.consolidator_ids?.filter((x: any) => approvers.includes(x));
      }
      for (const item of filteredAssignments.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf)) {
        item.reporter_ids = item.reporter_ids?.filter((x: any) => reporters.includes(x) || x === userProfileId);
      }
      return {status: filteredEntityAssignment.length !== 0, consolidation: filteredEntityAssignment.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf), reporter: filteredAssignments.filter((x: any) => x.qCategory && x.qCategory && x.qTopic && x.qSection && x.qSection?.srf)};

    } else {
      return {status: false}
    }


  }

  @post('/assign-ql-entity-users/{id}/reject')
  @response(200, {
    description: 'AssignQlEntityUser rejection update success',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {
              type: 'object',
              description: 'Updated assignment data'
            }
          }
        }
      }
    }
  })
  async rejectAssignment(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              reporterId: {
                type: 'string',
                description: 'Reporter ID to be used as key in response object'
              },
              rejected_on: {
                type: 'string',
                description: 'Date and time when the assignment was rejected'
              },
              rejected_by: {
                type: 'number',
                description: 'User ID of the person who rejected the assignment'
              },
              reject: {
                type: 'number',
                description: 'Reject status'
              },
              type: {
                type: 'number',
                description: 'submission type'
              },
              return_remarks: {
                type: 'array',
                items: {
                  type: 'object'
                },
                nullable: true,
                description: 'Array of remarks objects for rejection or null'
              }
            },
            required: ['reporterId', 'rejected_on', 'rejected_by', 'return_remarks', 'type', 'reject']
          }
        }
      }
    })
    rejectionData: {
      reporterId: string;
      rejected_on: string;
      rejected_by: number;
      type: number;
      reject: number;
      return_remarks: any[] | null;
    }
  ): Promise<{status: boolean; message: string; data?: any}> {
    try {
      // Check if the assignment exists
      const existingAssignment = await this.assignQlEntityUserRepository.findById(id);

      if (!existingAssignment) {
        return {
          status: false,
          message: `Assignment with ID ${id} not found`
        };
      }

      // Get current response object or initialize as empty object
      const currentResponse = existingAssignment.response || {};

      // Get existing data for this specific reporter
      const existingReporterData = currentResponse[rejectionData.reporterId] || {};
      if (currentResponse[rejectionData.reporterId]) {

        // Create the update data for the specific reporterId
        const reporterUpdateData = {
          ...existingReporterData,
          type: rejectionData.type,
          reject: rejectionData.reject,
          rejected_on: rejectionData.rejected_on,
          rejected_by: rejectionData.rejected_by,
          return_remarks: existingReporterData.return_remarks && rejectionData.return_remarks
            ? [...existingReporterData.return_remarks, ...rejectionData.return_remarks]
            : rejectionData.return_remarks
        };

        // Update the response object with the reporterId as key
        const updatedResponse = {
          ...currentResponse,
          [rejectionData.reporterId]: reporterUpdateData
        };

        // Update the assignment with the new response object
        await this.assignQlEntityUserRepository.updateById(id, {
          response: updatedResponse
        });

        // Fetch the updated assignment
        const updatedAssignment = await this.assignQlEntityUserRepository.findById(id);

        // Send email notification to the reporter
        try {
          await this.sendRejectionEmail(id, parseInt(rejectionData.reporterId), rejectionData.rejected_by);
        } catch (emailError) {
          console.error('Error sending rejection email:', emailError);
          // Don't fail the entire operation if email fails
        }

        return {
          status: true,
          message: `Assignment rejected successfully for reporter`,
          data: updatedAssignment
        };
      } else {
        return {
          status: false,
          message: `Assignment not found for reporter`
        };
      }
    } catch (error) {
      console.error('Error rejecting assignment:', error);
      return {
        status: false,
        message: `Failed to reject assignment: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Send rejection email to reporter
   * @param assignmentId Assignment ID
   * @param reporterId Reporter user ID
   * @param rejectedBy User ID who rejected the assignment
   */
  private async sendRejectionEmail(assignmentId: number, reporterId: number, rejectedBy: number): Promise<void> {
    try {
      // Get assignment details
      const assignment = await this.assignQlEntityUserRepository.findById(assignmentId, {include: ['qCategory', 'qTopic', {relation: 'qSection', scope: {include: ['srf']}}]});
      if (!assignment) {
        return;
      }

      // Get entity assignment details
      const entityAssignment = await this.assignQlEntityRepository.findById(assignment.entityAssId);
      if (!entityAssignment) {
        return
      }

      // Get reporter details
      const reporterProfile = await this.userProfileController.filteredUP({
        where: {id: reporterId}
      });
      if (!reporterProfile || reporterProfile.length === 0) {
        return
      }
      const reporter = reporterProfile[0];

      // Get all consolidators from entity assignment for CC
      const consolidatorProfiles = await this.userProfileController.filteredUP({
        where: {id: {inq: entityAssignment?.consolidator_ids || []}}
      });

      // Get consolidator emails for CC
      const consolidatorEmails = consolidatorProfiles
        .map((profile: any) => profile.email)
        .filter((email: any) => email); // Filter out any null/undefined emails

      // Get consolidator names for display in email
      const consolidatorNames = consolidatorProfiles
        .map((profile: any) => profile?.information?.empname || 'Consolidator')
        .filter((name: any) => name)
        .join(', ') || 'Consolidator';

      // Get topic and section from relations (already loaded with assignment)
      const topic = (assignment as any).qTopic;
      const section = (assignment as any).qSection;

      // Get entity name based on level
      let entityName = '';
      if (assignment.level === 0) {
        entityName = 'Corporate';
      } else if (assignment.level === 1) {
        const entity = await this.locationOneRepository.findById(assignment.locationId);
        entityName = entity?.name || 'NA';
      } else if (assignment.level === 2) {
        const entity = await this.locationTwoRepository.findById(assignment.locationId);
        entityName = entity?.name || 'NA';
      } else if (assignment.level === 3) {
        const entity = await this.locationThreeRepository.findById(assignment.locationId);
        entityName = entity?.name || 'NA';
      }

      // Get admin details for portal URL
      const adminObj = await this.userProfileRepository.findById(assignment.userProfileId);

      // Create email content
      const reporterName = reporter?.information?.empname || 'Reporter';
      const topicName = topic?.name || 'N/A';
      const sectionName = section?.name || 'N/A';

      const subject = 'Resubmission Required – Qualitative Sustainability Response Form – Navigos';

      const body = `
        <p><strong>Dear ${reporterName},</strong></p>

        <p style="margin: 5px 0px;">This is to notify you that your submitted Qualitative Sustainability Response Form(s) have been <strong>reopened for resubmission</strong> based on inputs from the assigned Consolidator.</p>

        <p style="margin: 5px 0px;">The following form(s) require your attention. Please update the responses as per the inputs received to ensure consistency and accuracy in our sustainability data reporting.</p>

        <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; width: 100%; margin: 10px 0;">
          <thead>
            <tr style="background-color: #f2f2f2;">
              <th>Topic</th>
              <th>Section</th>
              <th>Reporting Entity</th>
              <th>Consolidator Name</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>${topicName}</td>
              <td>${sectionName}</td>
              <td>${entityName}</td>
              <td>${consolidatorNames}</td>
              <td><strong>Resubmission Required</strong></td>
            </tr>
          </tbody>
        </table>

        ${adminObj?.userPortalUrl ? `<p style="margin: 5px 0px;">Please log in to the EiSqr – ESG Platform to revise and submit the required responses along with any necessary supporting documents before the submission deadline.</p>
        <p style="margin: 5px 0px;"><a href="${adminObj.userPortalUrl}" target="_blank">EiSqr – ESG Platform</a></p>` : '<p style="margin: 5px 0px;">Please log in to the EiSqr – ESG Platform to revise and submit the required responses along with any necessary supporting documents before the submission deadline.</p>'}

        <p style="margin: 5px 0px;">If you have already updated and resubmitted the form, please disregard this message.</p>

        <p style="margin: 5px 0px;">For any assistance, contact <a href="mailto:<EMAIL>"><EMAIL></a>. If you face issues such as incorrect assignments or technical errors, please report them immediately. Thank you for your prompt attention to this matter.</p>


      `;

      // Send email with consolidators in CC
      await this.sqsService.sendEmail(reporter.email, subject, body, [...consolidatorEmails, '<EMAIL>']);
      // axios.post('https://api.eisqr.com/post-email', {
      //   to: '<EMAIL>',
      //   subject,
      //   body,
      //   cc: []
      // });
      console.log(`Rejection email sent to reporter: ${reporter.email}, CC: ${consolidatorEmails.join(', ')}`);

    } catch (error) {
      console.error('Error in sendRejectionEmail:', error);
      return;
    }
  }

  @post('/assign-ql-entity-users/{id}/reporter-submission')
  @response(200, {
    description: 'Reporter submission update success',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            status: {type: 'boolean'},
            message: {type: 'string'},
            data: {
              type: 'object',
              description: 'Updated assignment data'
            }
          }
        }
      }
    }
  })
  async reporterSubmission(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            description: 'Object with userId as key and submission data as value',
            additionalProperties: {
              type: 'object',
              description: 'Submission data for specific user'
            }
          }
        }
      }
    })
    submissionData: {[userId: string]: any}
  ): Promise<{status: boolean; message: string; data?: any}> {
    try {
      // Check if the assignment exists
      const existingAssignment = await this.assignQlEntityUserRepository.findById(id);

      if (!existingAssignment) {
        return {
          status: false,
          message: `Assignment with ID ${id} not found`
        };
      }

      // Get current response object or initialize as empty object
      const currentResponse = existingAssignment.response || {};

      // Since object keys are unique, we can directly merge without looping
      const updatedUserIds = Object.keys(submissionData);

      // Create updated response by merging existing data with new submission data
      const updatedResponse = {
        ...currentResponse,
        ...Object.fromEntries(
          Object.entries(submissionData).map(([userId, userData]) => [
            userId,
            {
              ...(currentResponse[userId] || {}),
              ...(userData.status === 'Completed' ? {type: 1, reject: 0} : {}),
              ...userData
            }
          ])
        )
      };

      // Update the assignment with the new response object
      await this.assignQlEntityUserRepository.updateById(id, {
        response: updatedResponse
      });

      // Fetch the updated assignment
      const updatedAssignment = await this.assignQlEntityUserRepository.findById(id);

      return {
        status: true,
        message: `Reporter submission updated successfully for user(s): ${updatedUserIds.join(', ')}`,
        data: updatedAssignment
      };

    } catch (error) {
      console.error('Error updating reporter submission:', error);
      return {
        status: false,
        message: `Failed to update reporter submission: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }


  @get('send-qualitative-reporter-reminder')
  async getQualitativeReporterReminder(): Promise<any> {

    // Step 1: Determine reminder type based on date
    // const today = DateTime.now().setZone('Asia/Kolkata');
    const today = DateTime.fromObject({day: 3, month: 4, year: 2025}, {zone: 'Asia/Kolkata'});

    const day = today.day;
    const month = today.month;
    const allowedMonths = [1, 4, 7, 10]; // Jan, Apr, Jul, Oct

    let reminderType = '';
    let reminderSubject = '';
    let reminderLine = '';

    if (allowedMonths.includes(month)) {
      if (day === 3) {
        reminderType = 'Reminder [1/2]';
        reminderSubject = 'Reminder to submit the Assigned Qualitative Response Forms';
      } else if (day === 7) {
        reminderType = 'Reminder [2/2]';
        reminderSubject = 'Reminder to submit the Assigned Qualitative Response Forms';
      } else if (day === 10) {
        reminderType = 'Final Reminder';
        reminderSubject = 'Final Reminder to submit the Assigned Qualitative Response Forms';
      } else {
        return {message: 'Today is not a reminder day. No emails prepared.'};
      }
    } else {
      return {message: 'This month is not scheduled for reminders. No emails prepared.'};
    }



    const reporterAssignment = await this.getConsolidatorAssignmentById({userId: 0, userProfileId: 289})

    const filteredAssignments = reporterAssignment?.reporter?.filter((x: any) => x.status !== "Completed") || []

    const reportData: any = []
    const reporter_ids = filteredAssignments.flatMap((d: any) => d?.reporter_ids || []);
    // const userPortal = "https://tvsmotor.eisqr.com/"
    const userList = await this.userProfileController.filteredUP({where: {id: {inq: [...reporter_ids]}}})
    const locations = await this.userProfileRepository.locationOnes(289).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})

    const adminObj = await this.userProfileRepository.findById(289);


    for (const assignment of filteredAssignments) {

      if (!assignment.entityAssId) {
        console.warn(`Missing entityAssId in assignment:`, assignment);
        continue;
      }

      const entityAssignment = await this.assignQlEntityRepository.findById(assignment.entityAssId)

      const consolidatorData = await this.userProfileController.filteredUP({where: {id: {inq: entityAssignment?.consolidator_ids || []}}})




      const [reporters, entity] = await Promise.all([
        this.userProfileController.getUsersByIds(assignment.reporter_ids, userList),
        this.userProfileController.getSortedEntity(assignment.level, assignment.locationId, locations)
      ]);
      const reporterNames = reporters.map((user: any) => ({
        id: user.id,
        name: user.information['empname'], email: user.email
      }));

      const consolidateNames = consolidatorData.map((user: any) => (

        user?.information?.['empname'] || ''
      )).filter((x: any) => x).join(',');


      interface Entity {
        name: string;
        // other props if needed
      }

      const formattedDueDate = assignment.due_date
        ? new Date(assignment.due_date).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
        : '';

      reportData.push({
        reporterNames,
        "Topic": assignment.qTopic?.name || '',
        "Section": assignment.qSection?.name || '',
        "ReportingEntity": (entity as Entity)?.name || '',
        "DueDate": formattedDueDate || '',
        "consolidatorName": consolidateNames || ''
      });

    }

    const userMailMap: any = {};

    reportData.forEach(({Topic, Section, ReportingEntity, DueDate, consolidatorName, reporterNames = []}: any) => {
      const keyInfo = {Topic, Section, "Reporting Entity": ReportingEntity, "Due Date": DueDate, "Consolidator": consolidatorName};

      reporterNames.forEach((user: any) => {
        if (!userMailMap[user.email]) {
          userMailMap[user.email] = {reporter: [], name: user.name};
        }
        userMailMap[user.email].reporter.push(keyInfo);
      });



    });

    //     return [{
    //       id: 289,
    //       email: Object.entries(userMailMap).map(([email, value]) => {
    //         const user = value as {name: string, reporter: any}; // Explicit type assertion

    //         return {
    //           email,
    //           name: user.name,
    //           subject: 'Reminder to submit the Assigned Qualitative Response Forms ',
    //           body: `<div>
    //                   <p>Dear ${user.name}</p>
    //                   <p>This is <strong>Reminder[1/2]</strong> to complete your assigned <strong>Qualitative Response Form(s)</strong> as per the submission timeline.</p>
    // <p>The following submissions are pending from your end and are essential for the completeness of our sustainability data reporting.Timely submission will ensure they are processed and reflected in the performance dashboards and reports.</p>
    // ${this.helper.generateHtmlTable(user.reporter)}
    // <p>Please log in to the ${userPortal}  to complete and submit the required data along with all supporting documents before the deadline.</p>
    // <p>If you have already submitted the response form, please disregard this message. </p>

    // <p>For assistance, contact  <a href="mailto:<EMAIL>" > <EMAIL></a>. If you face any issues, such as incorrect assignments or technical difficulties, please report them immediately.</p>
    // <p>Thank you for your prompt attention to this matter.
    //                   <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
    //                 </div>`,
    //         };
    //       })
    //     }];
    // Step 4: Generate final email structure
    const emails = Object.entries(userMailMap).map(([email, value]) => {
      const user = value as {name: string, reporter: any};

      return {
        email,
        name: user.name,
        subject: `${reminderSubject}`,
        body: `<div>
        <p>Dear ${user.name},</p>
        <p>This is <strong>${reminderType}</strong> to complete your assigned <strong>Qualitative Response Form(s)</strong> as per the submission timeline.</p>

        <p>The following submissions are pending from your end and are essential for the completeness of our sustainability data reporting. Timely submission will ensure they are processed and reflected in the performance dashboards and reports.</p>

        ${this.helper.generateHtmlTable(user.reporter)}

         ${adminObj?.userPortalUrl ? `<p style="margin: 5px 0px;">
     Please log in to the
      <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform</a> to complete and submit the required responses along with all supporting documents before the deadline.
    </p>` : ''}


        <p>If you have already submitted the response form, please disregard this message.</p>
        <p>For assistance, contact <a href="mailto:<EMAIL>"><EMAIL></a>. If you face any issues, such as incorrect assignments or technical difficulties, please report them immediately.</p>
        <p>Thank you for your prompt attention to this matter.</p>
        <p style="font-style:italic;">This is an automated notification. Please do not reply to this message.</p>
      </div>`
      };
    });
    return [{
      id: 289,
      email: emails
    }];

  }

  @get('/send-qualitative-consolidator-reminder')
  async getQualitativeConsolidatorReminder(): Promise<any> {

    // Step 1: Determine reminder type based on date
    // const today = DateTime.now().setZone('Asia/Kolkata');
    const today = DateTime.fromObject({day: 15, month: 1, year: 2025}, {zone: 'Asia/Kolkata'});

    const day = today.day;
    const month = today.month;
    const allowedMonths = [1, 4, 7, 10]; // Jan, Apr, Jul, Oct

    let reminderType = '';
    let reminderSubject = '';
    let reminderLine = '';

    if (allowedMonths.includes(month)) {
      if (day === 7) {
        reminderType = 'Reminder [1/2]';
        reminderSubject = 'Reminder to submit the Assigned Qualitative Response Forms';
      } else if (day === 10) {
        reminderType = 'Reminder [2/2]';
        reminderSubject = 'Reminder to submit the Assigned Qualitative Response Forms';
      } else if (day === 15) {
        reminderType = 'Final Reminder';
        reminderSubject = 'Final Reminder to submit the Assigned Qualitative Response Forms';
      } else {
        return {message: 'Today is not a reminder day. No emails prepared.'};
      }
    } else {
      return {message: 'This month is not scheduled for reminders. No emails prepared.'};
    }

    const result = await this.getConsolidatorAssignmentById({userId: 0, userProfileId: 289});

    // console.log('Result:', result); //


    const filteredAssignments = result?.consolidation?.filter((x: any) => {
      return (!x.response || x.status !== "Completed");
    }) || [];


    const conData: any = []
    const con_ids = filteredAssignments.flatMap((d: any) => d?.consolidator_ids || []);

    const userList = await this.userProfileController.filteredUP({where: {id: {inq: [...con_ids]}}})

    const adminObj = await this.userProfileRepository.findById(289);
    const allEntities: any[] = [];

    for (const assignment of filteredAssignments) {
      let entities: any[] = [];

      if (assignment.tier0_ids?.includes(0)) {
        entities.push({id: 0, name: 'Corporate'});
      }

      if (assignment.tier1_ids?.length) {
        const tier1 = await this.locationOneRepository.find({
          where: {id: {inq: assignment.tier1_ids}},
          fields: {id: true, name: true},
        });
        entities.push(...tier1);
      }

      if (assignment.tier2_ids?.length) {
        const tier2 = await this.locationTwoRepository.find({
          where: {id: {inq: assignment.tier2_ids}},
          fields: {id: true, name: true},
        });
        entities.push(...tier2);
      }

      if (assignment.tier3_ids?.length) {
        const tier3 = await this.locationThreeRepository.find({
          where: {id: {inq: assignment.tier3_ids}},
          fields: {id: true, name: true},
        });
        entities.push(...tier3);
      }

      allEntities.push(...entities);


      const [consolidators] = await Promise.all([
        this.userProfileController.getUsersByIds(assignment.consolidator_ids, userList),

      ]);
      const consildatorNames = consolidators.map((user: any) => ({
        id: user.id,
        name: user.information['empname'], email: user.email
      }));

      const reportingEntity = entities.map((x: any) => x.name).join(', ');

      conData.push({
        consildatorNames,
        "Topic": assignment.qTopic?.name || '',
        "Section": assignment.qSection?.name || '',
        "ReportingEntity": reportingEntity || '',
        "Status": "Pending Consolidation"
      });

    }

    const userMailMap: any = {};

    conData.forEach(({Topic, Section, ReportingEntity, Status, consildatorNames = []}: any) => {
      const keyInfo = {Topic, Section, "Reporting Entity": ReportingEntity, Status};

      consildatorNames.forEach((user: any) => {
        if (!userMailMap[user.email]) {
          userMailMap[user.email] = {consolidator: [], name: user.name};
        }
        userMailMap[user.email].consolidator.push(keyInfo);
      });



    });


    const emails = Object.entries(userMailMap).map(([email, value]) => {
      const user = value as {name: string, consolidator: any};

      return {
        email,
        name: user.name,
        subject: ` ${reminderType}: Consolidation of Submitted Qualitative Response Form(s) Pending`,
        body: `
        <p>Dear ${user.name},</p>
        <p>This is ${reminderType} to complete the consolidation of the assigned Qualitative Response Form(s) submitted by reporters under your scope..</p>

<p>The following topics are pending consolidation and are essential for ensuring consistency and completeness across entities. Your timely action will enable accurate reflection of qualitative inputs in the performance dashboards and sustainability reports.</p>

        ${this.helper.generateHtmlTable(user.consolidator)}


         ${adminObj?.userPortalUrl ? `<p style="margin: 5px 0px;">
     Please log in to the
      <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform</a> to complete the review and consolidation of the respective forms before the submission deadline.
    </p>` : ''}

      <p>If you have already completed the consolidation, kindly disregard this message.</p>
        <p>For any assistance or to report technical issues, please reach out to<a href="mailto:<EMAIL>"><EMAIL></a>. </p>
        <p>Thank you for your cooperation and timely contribution to the ESG reporting process.</p>
        <p style="font-style:italic;">This is an automated notification. Please do not reply to this message.</p>


`
      };
    });
    return [{
      id: 289,
      email: emails
    }];










  }
}


