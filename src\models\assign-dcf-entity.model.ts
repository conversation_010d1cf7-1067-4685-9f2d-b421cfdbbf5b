import {Entity, belongsTo, model, property} from '@loopback/repository';
import {FormCollection} from './form-collection.model';

@model()
export class AssignDcfEntity extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'any',
  })
  comments?: any;

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier0_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier1_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier2_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier3_ids?: number[];


  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => FormCollection, {name: 'dcf'})
  dcfId: number;

  constructor(data?: Partial<AssignDcfEntity>) {
    super(data);
  }
}

export interface AssignDcfEntityRelations {
  // describe navigational properties here
}

export type AssignDcfEntityWithRelations = AssignDcfEntity & AssignDcfEntityRelations;
