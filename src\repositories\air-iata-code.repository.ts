import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AirIataCode, AirIataCodeRelations} from '../models';

export class AirIataCodeRepository extends DefaultCrudRepository<
  AirIataCode,
  typeof AirIataCode.prototype.id,
  AirIataCodeRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AirIataCode, dataSource);
  }
}
