import {Entity, model, property} from '@loopback/repository';

@model()
export class QuantitativeDpReport extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'any',
  })
  standard?: any;

  @property({
    type: 'number',
  })
  submitId?: number;

  @property({
    type: 'string',
  })
  dp?: string;

  @property({
    type: 'any',
  })
  value?: any;
  @property({
    type: 'any',
  })
  dataType?: any;
  @property({
    type: 'any',
  })
  formId?: any;
  @property({
    type: 'number',
  })
  frequency?: number;
  @property({
    type: 'number',
  })
  formType?: number;
  @property({
    type: 'any',
  })
  tier0_id?: any;
  @property({
    type: 'any',
  })
  tier1_id?: any;
  @property({
    type: 'any',
  })
  tier2_id?: any;
  @property({
    type: 'any',
  })
  tier3_id?: any;
  @property({
    type: 'number',
  })
  level?: number;
  @property({
    type: 'number',
  })
  entityUserAssId?: number;
  @property({
    type: 'number',
  })
  entityAssId?: number;
  @property({
    type: 'number',
  })
  dcfId?: number;
  @property({
    type: 'array',
    itemType: 'any',
  })
  reporting_period?: any[];

  @property({
    type: 'string',
  })
  created_on?: string;
  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'string',
  })
  modified_on?: string;
  @property({
    type: 'number',
  })
  modified_by?: number;
  @property({
    type: 'any',
  })
  reviewed_by?: any;
  @property({
    type: 'number',
  })
  approved_by?: number;
  @property({
    type: 'number',
  })
  reported_by?: number;
  @property({
    type: 'number',
  })
  submissionType?: number;
  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<QuantitativeDpReport>) {
    super(data);
  }
}

export interface QuantitativeDpReportRelations {
  // describe navigational properties here
}

export type QuantitativeDpReportWithRelations = QuantitativeDpReport & QuantitativeDpReportRelations;
