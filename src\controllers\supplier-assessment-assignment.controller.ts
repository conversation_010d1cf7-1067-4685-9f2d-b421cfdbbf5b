import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {SupplierAssessmentAssignment} from '../models';
import {AssessmentSectionRepository, AssessmentSubSection1Repository, AssessmentSubSection2Repository, AuditorAssignmentSubmissionRepository, SupplierAssessmentAssignmentRepository, SupplierAssignmentSubmissionRepository, UserRoleAuthorizationRepository, VendorCodeRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class SupplierAssessmentAssignmentController {
  constructor(
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
    @repository(AssessmentSectionRepository)
    public assessmentSectionRepository: AssessmentSectionRepository,
    @repository(AssessmentSubSection1Repository)
    public assessmentSubSection1Repository: AssessmentSubSection1Repository,
    @repository(AssessmentSubSection2Repository)
    public assessmentSubSection2Repository: AssessmentSubSection2Repository,
    @repository(AuditorAssignmentSubmissionRepository)
    public auditorAssignmentSubmissionRepository: AuditorAssignmentSubmissionRepository,
    @repository(SupplierAssignmentSubmissionRepository)
    public supplierAssignmentSubmissionRepository: SupplierAssignmentSubmissionRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @repository(UserRoleAuthorizationRepository) protected userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
  ) { }

  @post('/supplier-assessment-assignments')
  @response(200, {
    description: 'SupplierAssessmentAssignment model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierAssessmentAssignment)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssessmentAssignment, {
            title: 'NewSupplierAssessmentAssignment',
            exclude: ['id'],
          }),
        },
      },
    })
    supplierAssessmentAssignment: Omit<SupplierAssessmentAssignment, 'id'>,
  ): Promise<SupplierAssessmentAssignment> {
    return this.supplierAssessmentAssignmentRepository.create(supplierAssessmentAssignment);
  }

  @get('/supplier-assessment-assignments/count')
  @response(200, {
    description: 'SupplierAssessmentAssignment model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplierAssessmentAssignment) where?: Where<SupplierAssessmentAssignment>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.count(where);
  }

  @get('/supplier-assessment-assignments')
  @response(200, {
    description: 'Array of SupplierAssessmentAssignment model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplierAssessmentAssignment, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplierAssessmentAssignment) filter?: Filter<SupplierAssessmentAssignment>,
  ): Promise<SupplierAssessmentAssignment[]> {
    return this.supplierAssessmentAssignmentRepository.find(filter);
  }

  @patch('/supplier-assessment-assignments')
  @response(200, {
    description: 'SupplierAssessmentAssignment PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssessmentAssignment, {partial: true}),
        },
      },
    })
    supplierAssessmentAssignment: SupplierAssessmentAssignment,
    @param.where(SupplierAssessmentAssignment) where?: Where<SupplierAssessmentAssignment>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.updateAll(supplierAssessmentAssignment, where);
  }

  @get('/supplier-assessment-assignments/{id}')
  @response(200, {
    description: 'SupplierAssessmentAssignment model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplierAssessmentAssignment, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SupplierAssessmentAssignment, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplierAssessmentAssignment>
  ): Promise<SupplierAssessmentAssignment> {
    return this.supplierAssessmentAssignmentRepository.findById(id, filter);
  }

  @patch('/supplier-assessment-assignments/{id}')
  @response(204, {
    description: 'SupplierAssessmentAssignment PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssessmentAssignment, {partial: true}),
        },
      },
    })
    supplierAssessmentAssignment: SupplierAssessmentAssignment,
  ): Promise<void> {

    const calibrateAssignment = await this.supplierAssessmentAssignmentRepository.updateById(id, supplierAssessmentAssignment);
    const allReviewers = supplierAssessmentAssignment?.reviewer_ids || []
    const allAssessors = [
      supplierAssessmentAssignment?.group1?.assessors,
      supplierAssessmentAssignment?.group2?.assessors,
      supplierAssessmentAssignment?.group3?.assessors,
      supplierAssessmentAssignment?.group4?.assessors,
    ].filter(assessors => Array.isArray(assessors)).flatMap(x => x).filter((id): id is number => id != null);

    console.log(allAssessors);

    const categoryList = [{name: 'Forging & Machining', value: 1}, {name: 'Casting & Machining', value: 2}, {name: 'Pressing & Fabrication', value: 3}, {name: 'Proprietary Mechanical', value: 4}, {name: 'Proprietary Electrical', value: 5}, {name: 'Plastics, Rubber, Painting and Stickers', value: 6}, {name: 'EV/3W/2W', value: 7}, {name: 'BW', value: 8}, {name: 'Accessories', value: 9}, {name: 'IDM (Indirect Material)', value: 10}, {name: 'Import', value: 11}]
    const attachment1 = supplierAssessmentAssignment?.attachment1;
    const attachment2 = supplierAssessmentAssignment?.attachment2;
    const attachment3 = supplierAssessmentAssignment?.attachment3;
    const auditStartDate = supplierAssessmentAssignment?.auditStartDate;
    const oldObject = await this.supplierAssessmentAssignmentRepository.findById(id)
    const vendorData = await this.vendorCodeRepository.findById(oldObject.vendorId);
    const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
    const auditorData = await this.userProfileController.filteredUP({where: {id: {inq: allAssessors || []}}})
    const reviewerData = await this.userProfileController.filteredUP({where: {id: {inq: allReviewers || []}}})
    const supplierOtherSpoc = this.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x)
    const supplierCategoryName =
      categoryList.find(item => item.value === vendorData.supplierCategory)?.name || 'N/A';
    const roleAgainsCategory = {
      1: 25,
      2: 26,
      3: 27,
      4: 28,
      5: 29,
      6: 30,
      7: 31,
      8: 32,
      9: 33,
      10: 34,
      11: 35
    }
    const role_id = roleAgainsCategory[vendorData?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
    const roles = await this.userRoleAuthorizationRepository.execute(

      `SELECT * FROM UserRoleAuthorization
            WHERE roles IS NOT NULL
              AND JSON_LENGTH(roles) > 0
              AND JSON_CONTAINS(roles, ?, '$')`,
      [JSON.stringify([role_id])]
    )
    const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
    const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
    const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)

    if (!attachment1 && !attachment2 && !attachment3 && auditStartDate != null) {
      const subject = ` Upcoming My Sustainability Index (MSI) Calibration Schedule – ${vendorData.supplierName}`;
      const body = ` <p  style="margin: 5px 0px;" > Dear Auditor(s) and ${vendorData.supplierName} </p>
    <p>Greetings from TVS Motor Company</p>

    <p style="margin: 5px 0px;" >
      This is to formally inform you that the MSI Calibration Audit for the supplier mentioned below has been scheduled. Kindly find the details below:
    </p>



     <p  style="margin: 5px 0px;" ><strong> Supplier Details :</strong> </p>
      <ul>
<li>Supplier Name :<strong>${vendorData.supplierName}</strong> (${vendorData.code})</li>
<li>Supplier Category :<strong>${supplierCategoryName}</strong> </li>
 <li>Supplier Location :<strong>${vendorData.supplierLocation}</strong> </li>
   <li>Supplier Spoc Name :<strong>${vendorData.supplierSPOC}</strong> </li>
      </ul>
         <p><strong> Audit Schedule :</strong> </p>

          <ul>
<li>Scheduled Date :<strong>${DateTime.fromISO(supplierAssessmentAssignment?.auditStartDate ? supplierAssessmentAssignment.auditStartDate : '', {zone: 'utc'}).plus({day: 1}).toFormat('dd-MM-yyyy')}</strong> </li>
${auditorData?.map((item: any, index: number) => {
        return (
          `<li>Auditor Name #${index + 1} :<strong>${item?.information?.empname || 'NA'}</strong> </li>
<li>Auditor Email Id #${index + 1}:<strong>${item?.email || 'NA'}</strong> </li>`

        )
      }).join('')}
      ${reviewerData?.map((item: any, index: number) => {
        return (
          `<li>Reviewer Name #${index + 1} :<strong>${item?.information?.empname || 'NA'}</strong> </li>
<li>Reviewer Email Id #${index + 1}:<strong>${item?.email || 'NA'}</strong> </li>`

        )
      }).join('')}
      </ul>

      <p  style="margin: 5px 0px;" >We request the Supplier team to be available and prepared with the necessary documentation and access required to facilitate a smooth Calibration process. Ensure availability of documents like Air & Water Consent order, Hazardous Waste Authorization, Licenses, HR & accounts records etc.,</p>

      <p  style="margin: 5px 0px;" >We also request the assigned auditor to be fully prepared with all required tools and materials to conduct the audit as scheduled.</p>

        <p  style="margin: 5px 0px;" >For any queries or assistance, feel free to reach out to us at <strong><EMAIL></strong> and copy <strong><EMAIL>.</strong></p>



        <p>Thank you for your cooperation.</p>

        <p>Warm regards,</p>
<p>TVS Motor Company Limited </p>

<p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>

  `;

      try {
        await this.sqsService.sendEmail([vendorSpoc?.[0]?.email, ...supplierOtherSpoc, ...auditorData.map((x: any) => x.email)], subject, body, [...headSpocMailId, '<EMAIL>', '<EMAIL>'])

      } catch (error) {
        console.error('Error sending email:', error);

      }
    }
    return calibrateAssignment;

  }

  @put('/supplier-assessment-assignments/{id}')
  @response(204, {
    description: 'SupplierAssessmentAssignment PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() supplierAssessmentAssignment: SupplierAssessmentAssignment,
  ): Promise<void> {
    await this.supplierAssessmentAssignmentRepository.replaceById(id, supplierAssessmentAssignment);
  }

  @del('/supplier-assessment-assignments/{id}')
  @response(204, {
    description: 'SupplierAssessmentAssignment DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.supplierAssessmentAssignmentRepository.deleteById(id);
  }

  @post('/assessment-sections-supplier-custom')
  @response(200, {
    description: 'AssessmentSection model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierAssessmentAssignment)}},
  })
  async getSupplierAssessmentStatus(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {

              assignmentId: {
                type: 'string',
              }
            },
            required: ['assignmentId'],
          },
        },
      },
    })
    requestBody: any,
  ): Promise<{}> {
    const {assignmentId, type} = requestBody

    let submittedData = await this.supplierAssignmentSubmissionRepository.findOne({where: {supplierAssessmentAssignmentId: assignmentId}})
    if (!submittedData) {
      let sectionData = await this.assessmentSectionRepository.find({
        include: [
          {
            relation: "assessmentSubSection1s",
            scope: {
              include: [{
                relation: "assessmentSubSection2s", "scope": {include: ['form']}
              }],

            },
          },
        ],
      })

      return {result: 1, response: JSON.stringify(sectionData)}
    } else {
      return {result: 2, ...submittedData}
    }

  }
  @post('/assessment-sections-auditor-custom')
  @response(200, {
    description: 'AssessmentSection model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierAssessmentAssignment)}},
  })
  async getAuditorAssessmentStatus(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {

              assignmentId: {
                type: 'string',
              }
            },
            required: ['assignmentId'],
          },
        },
      },
    })
    requestBody: any,
  ): Promise<{}> {
    const {assignmentId, type} = requestBody

    let supplierResponse = await this.supplierAssignmentSubmissionRepository.findOne({where: {supplierAssessmentAssignmentId: assignmentId}})
    let sectionData = await this.assessmentSectionRepository.find({
      include: [
        {
          relation: "assessmentSubSection1s",
          scope: {
            include: [{
              relation: "assessmentSubSection2s", "scope": {include: ['form']}
            }],

          },
        },
      ],
    })

    let submittedData = await this.auditorAssignmentSubmissionRepository.findOne({where: {supplierAssessmentAssignmentId: assignmentId}})
    if (!supplierResponse && !submittedData) {
      return {result: 0, response: JSON.stringify(sectionData), supplierResponse: []}
    } else if (supplierResponse && !submittedData) {
      return {result: 1, response: JSON.stringify(sectionData), supplierResponse: supplierResponse?.response || []}

    } else {
      return {result: 2, ...submittedData, supplierResponse: supplierResponse?.response || []}
    }

  }
  getUniqueValidEmails(data: any) {
    const seenEmails = new Set(); // Global tracker for unique emails

    return data.map(({code, supplierEmail3, supplierEmail2}: any) => {
      const uniqueEmails = new Set(); // Local tracker to prevent duplicate emails within the same code

      [supplierEmail3, supplierEmail2].forEach((email: any) => {
        if (this.isValidEmail(email) && !seenEmails.has(email)) {
          uniqueEmails.add(email);
          seenEmails.add(email); // Track globally
        }
      });

      return {code, emails: [...uniqueEmails]};
    }).filter((entry: any) => entry.emails.length > 0); // Remove empty email lists
  }

  isValidEmail(email: any) {
    if (!email?.trim()) return false; // Returns false for null, undefined, or empty string
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };
}
