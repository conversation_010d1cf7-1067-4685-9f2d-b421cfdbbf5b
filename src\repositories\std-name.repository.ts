import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {StdName, StdNameRelations, StdScope} from '../models';
import {StdScopeRepository} from './std-scope.repository';

export class StdNameRepository extends DefaultCrudRepository<
  StdName,
  typeof StdName.prototype.id,
  StdNameRelations
> {

  public readonly stdScopes: HasManyRepositoryFactory<StdScope, typeof StdName.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('StdScopeRepository') protected stdScopeRepositoryGetter: Getter<StdScopeRepository>,
  ) {
    super(StdName, dataSource);
    this.stdScopes = this.createHasManyRepositoryFactoryFor('stdScopes', stdScopeRepositoryGetter,);
    this.registerInclusionResolver('stdScopes', this.stdScopes.inclusionResolver);
  }
}
