import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, repository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewDataPoint, NewMetric, NewMetricRelations} from '../models';
import {NewDataPointRepository} from './new-data-point.repository';

export class NewMetricRepository extends DefaultCrudRepository<
  NewMetric,
  typeof NewMetric.prototype.id,
  NewMetricRelations
> {

  public readonly newDataPoints: HasManyRepositoryFactory<NewDataPoint, typeof NewMetric.prototype.id>;


  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewDataPointRepository') protected newDataPointRepositoryGetter: Getter<NewDataPointRepository>
  ) {
    super(NewMetric, dataSource);

    this.newDataPoints = this.createHasManyRepositoryFactoryFor('newDataPoints', newDataPointRepositoryGetter,);
    this.registerInclusionResolver('newDataPoints', this.newDataPoints.inclusionResolver);
  }
}
