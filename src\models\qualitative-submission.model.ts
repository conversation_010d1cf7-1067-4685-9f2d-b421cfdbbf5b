import {Entity, model, property} from '@loopback/repository';

@model()
export class QualitativeSubmission extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  categoryId?: number;

  @property({
    type: 'number',
  })
  indicatorId?: number;
  @property({
    type: 'number',
  })
  implevel?: number;
  @property({
    type: 'number',
  })
  response_type?: number;

  @property({
    type: 'number',
  })
  topicId?: number;

  @property({
    type: 'number',
  })
  dfId?: number;

  @property({
    type: 'array',
    itemType: 'string',
  })
  reporting_period?: string[];


  @property({
    type: 'number',
  })
  reviewed_by?: number;

  @property({
    type: 'string',
  })
  reviewed_on?: string;


  @property({
    type: 'number',
  })
  user_type?: number;

  @property({
    type: 'number',
  })
  form_type?: number;

  @property({
    type: 'number',
  })
  locationId?: number;
  @property({
    type: 'number',
  })
  level?: number;
  @property({
    type: 'number',
  })
  tier0_id?: number;
  @property({
    type: 'number',
  })
  tier1_id?: number;
  @property({
    type: 'number',
  })
  tier2_id?: number;
  @property({
    type: 'number',
  })
  tier3_id?: number;


  @property({
    type: 'array',
    itemType: 'any',
  })
  response?: any[];

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'any',
  })
  logs?: any;

  @property({
    type: 'any',
  })
  return_remarks?: any;

  @property({
    type: 'any',
  })
  standard?: any;

  @property({
    type: 'number',
  })
  edit?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  data1?: any[];

  @property({
    type: 'string',
  })
  submitted_on?: string;
  @property({
    type: 'string',
  })
  approver_modified_on?: string;
  @property({
    type: 'number',
  })
  approver_modified_by?: number;

  @property({
    type: 'string',
  })
  reviewer_modified_on?: string;
  @property({
    type: 'number',
  })
  reviewer_modified_by?: number;

  @property({
    type: 'string',
  })
  reporter_modified_on?: string;
  @property({
    type: 'number',
  })
  reporter_modified_by?: number;

  @property({
    type: 'string',
  })
  last_modified_on?: string;

  @property({
    type: 'number',
  })
  last_modified_by?: number;
  @property({
    type: 'number',
  })
  reject?: number;

  @property({
    type: 'string',
  })
  approved_on?: string;

  @property({
    type: 'string',
  })
  rejected_on?: string;

  @property({
    type: 'number',
  })
  frequency?: number;

  @property({
    type: 'number',
  })
  return_status?: number;

  @property({
    type: 'number',
  })
  reviewer_return?: number;

  @property({
    type: 'number',
  })
  approver_return?: number;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'number',
  })
  approved_by?: number;

  @property({
    type: 'number',
  })
  review_return_by?: number;
  @property({
    type: 'string',
  })
  review_return_on?: string;
  @property({
    type: 'number',
  })
  approve_return_by?: number;
  @property({
    type: 'string',
  })
  approve_return_on?: string;

  @property({
    type: 'boolean',
  })
  self?: boolean;

  @property({
    type: 'any',
  })
  entityUserAssId?: any;

  @property({
    type: 'any',
  })
  entityAssId?: any;

  @property({
    type: 'any',
  })
  documents?: any;
  @property({
    type: 'any',
  })
  justification?: any;
  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<QualitativeSubmission>) {
    super(data);
  }
}

export interface QualitativeSubmissionRelations {
  // describe navigational properties here
}

export type QualitativeSubmissionWithRelations = QualitativeSubmission & QualitativeSubmissionRelations;
