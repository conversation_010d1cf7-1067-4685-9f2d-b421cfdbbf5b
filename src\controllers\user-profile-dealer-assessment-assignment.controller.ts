import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {
  DealerAssessmentAssignment,
  UserProfile,
} from '../models';
import {UserProfileRepository, UserRoleAuthorizationRepository, VendorCodeRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class UserProfileDealerAssessmentAssignmentController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,
    @repository(UserRoleAuthorizationRepository) protected userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @get('/user-profiles/{id}/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many DealerAssessmentAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DealerAssessmentAssignment)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<DealerAssessmentAssignment>,
  ): Promise<DealerAssessmentAssignment[]> {
    return this.userProfileRepository.dealerAssessmentAssignments(id).find(filter);
  }

  @post('/user-profiles/{id}/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerAssessmentAssignment)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {
            title: 'NewDealerAssessmentAssignmentInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) dealerAssessmentAssignment: Omit<DealerAssessmentAssignment, 'id'>,
  ): Promise<DealerAssessmentAssignment> {

    const vendorData = await this.vendorCodeRepository.findById(Number(dealerAssessmentAssignment.vendorId))
    if (vendorData) {

      const auditorData = await this.userProfileController.filteredUP({where: {id: {inq: dealerAssessmentAssignment?.assessors || []}}})
      const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}})
      const headMailids = this.userProfileController.extractDealerHeadValidEmails(vendorData, ['aps', 'ao', 'sales', 'service'], ['areaManagerMailId', 'areaCommercialManagerMailId'])

      if (auditorData.length && vendorSpoc.length && vendorSpoc[0].email) {
        const dealerSpoc = vendorSpoc[0].email
        const roles = await this.userRoleAuthorizationRepository.execute(

          `SELECT * FROM UserRoleAuthorization
      WHERE roles IS NOT NULL
        AND JSON_LENGTH(roles) > 0
        AND JSON_CONTAINS(roles, ?, '$')`,
          [JSON.stringify([13])]
        )

        const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
        const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
        const sectionAdminMailIds = headUserData.map((x: any) => x.email).filter((x: any) => x)
        const subject = "Upcoming My Sustainability Index (MSI) Calibration Schedule – " + vendorData.dealerName
        const body = `<p>Dear Auditor(s) and ${vendorData.dealerName} </p>
  <p  style="margin: 5px 0px;" >Greetings from TVS Motors </p>
    <p  style="margin: 5px 0px;" >This is to formally inform you that the <strong>MSI Calibration </strong> for the dealership mentioned below has been scheduled. Kindly find the Calibration details below: </p>
      <p   ><strong> Dealer Details :</strong> </p>
      <ul>
<li>Dealer Name :<strong>${vendorData.dealerName}</strong> </li>
<li>Dealer Code :<strong>${vendorData.code}</strong> </li>
<li>Dealer Location :<strong>${vendorData.dealerLocation}</strong> </li>
      </ul>
         <p   ><strong>Calibration Schedule :</strong> </p>
      <ul>
 <li>Scheduled Date :<strong>${DateTime.fromISO(dealerAssessmentAssignment?.auditStartDate || '', {zone: 'utc'}).setZone('Asia/Calcutta').toFormat('dd-MM-yyyy')}</strong> </li>
${auditorData?.map((item: any, index: number) => {
          return (
            `<li>Auditor Name :<strong>${item?.information?.empname || 'NA'}</strong> </li>
<li>Auditor Email Id :<strong>${item?.email || 'NA'}</strong> </li>`

          )
        })}
      </ul>
        <p  style="margin: 5px 0px;" >We request the dealer team (Sales Manager/Works Manager/HR & Finance Team) to be available and prepared with the necessary documentation and access required to facilitate a smooth Calibration process.
Ensure availability of documents like Air & Water Consent order, Hazardous Waste Authorization, Shops & Establishment Licenses, Insurances, HR & accounts records. </p>
 <p  style="margin: 5px 0px;" >We also request the assigned auditor to be fully prepared with all required tools and materials to conduct the Calibration as scheduled.</p>
  <p  style="margin: 5px 0px;" >For any queries or assistance, please reach out to <strong><EMAIL></strong> and copy <strong><EMAIL>.</strong></p>
   <p  style="margin: 5px 0px;" >Thank you for your cooperation.</p>
   <p style='margin: 5px 0px;'>Warm regards,</p>
<p style='margin: 5px 0px;'><strong>TVS Motor Company Limited</strong></p>
    <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
  `
        this.sqsService.sendEmail([...auditorData.map((x: any) => x.email).filter((x: any) => x), dealerSpoc], subject, body, [...headMailids, ...sectionAdminMailIds])

      }
    }

    return this.userProfileRepository.dealerAssessmentAssignments(id).create(dealerAssessmentAssignment);
  }

  @patch('/user-profiles/{id}/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.DealerAssessmentAssignment PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {partial: true}),
        },
      },
    })
    dealerAssessmentAssignment: Partial<DealerAssessmentAssignment>,
    @param.query.object('where', getWhereSchemaFor(DealerAssessmentAssignment)) where?: Where<DealerAssessmentAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.dealerAssessmentAssignments(id).patch(dealerAssessmentAssignment, where);
  }

  @del('/user-profiles/{id}/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.DealerAssessmentAssignment DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(DealerAssessmentAssignment)) where?: Where<DealerAssessmentAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.dealerAssessmentAssignments(id).delete(where);
  }

  @post('/user-profiles/{id}/dealer-assessment-assignments-custom', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many DealerAssessmentAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DealerAssessmentAssignment)},
          },
        },
      },
    },
  })
  async findCustom(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            dealerId: {
              type: 'number'
            },
            type: {
              type: 'string'
            }
          },
          required: ['dealerId'],
        },
      },
    },
  })
  requestBody: {dealerId: number, type: string},
    @param.path.number('id') id: number,
  ): Promise<any> {
    const {type, dealerId} = requestBody
    try {
      let assignmentList: any = []

      if (type === 'llarevo') {
        console.log('overall')
        assignmentList = await this.userProfileRepository.dealerAssessmentAssignments(id).find({include: [{relation: "dealer"}, {relation: "form"}, {relation: "dealerChecklistSubmissions", scope: {"include": ["dsubmitter", "asubmitter", "amodifier", "dmodifier"]}}]});

      } else {
        console.log('less')

        assignmentList = await this.userProfileRepository.dealerAssessmentAssignments(id).find({where: {dealerId}, include: [{relation: "dealer"}, {relation: "form"}, {relation: "dealerChecklistSubmissions", scope: {"include": ["dsubmitter", "asubmitter", "amodifier", "dmodifier"]}}]});

      }
      console.log(assignmentList)
      const result = await new Promise(async (resolve) => {
        let data = [];
        let newObj = []
        for (const ass of assignmentList) {

          let months = this.getMonthsBetween(ass.assessmentStartDate, 1);
          console.log(months)
          let found = months.map((i) => ({
            reporting_period: i, rp: this.getRPTextFormat(i),
            submitted: (ass?.dealerChecklistSubmissions?.find(
              (x: any) => this.getRPTextFormat(x?.reporting_period || []) === this.getRPTextFormat(i)
            ) || null),
          }));
          delete ass.dealerChecklistSubmissions
          newObj = {...ass}
          newObj['months'] = found

          data.push(newObj);
        }

        resolve(data); // Resolve after all iterations are complete
      });
      return {status: true, data: result}
    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }
  getRPTextFormat(item: string[]): string {
    if (item.length !== 0) {
      if (item.length >= 2) {
        const startDate = DateTime.fromFormat(item[0], "MM-yyyy").toFormat(
          "LLL-yyyy"
        );
        const endDate = DateTime.fromFormat(
          item[item.length - 1],
          "MM-yyyy"
        ).toFormat("LLL-yyyy");
        return `${startDate} to ${endDate}`;
      } else {
        return DateTime.fromFormat(item[0], "MM-yyyy").toFormat("LLL-yyyy");
      }
    } else {
      return ''
    }
  }
  getMonthsBetween(startDate: any, frequency: number) {
    // Default to the current date if startDate or endDate is null
    const currentDate = DateTime.utc();
    startDate = startDate ? DateTime.fromISO(startDate).setZone("Asia/calcutta").startOf('month') : currentDate;
    let endDate = DateTime.utc().setZone("Asia/calcutta").endOf('month')

    const months = [];
    let current = startDate.startOf('month');

    // Loop to add months based on frequency
    while (current < endDate) {
      let nextDate = current.plus({months: frequency - 1}).startOf('month');
      if (current > endDate) {
        nextDate = endDate;
      }

      if (frequency === 1) {
        months.push(this.getMonthArray(current.toFormat('MMM-yyyy'), frequency - 1));
      } else {
        months.push(this.getMonthArray(`${current.toFormat('MMM-yyyy')} to ${nextDate.toFormat('MMM-yyyy')}`, frequency - 1));
      }

      current = nextDate.plus({months: 1}).startOf('month');
    }

    return months;
  }
  getMonthArray(dateStr: string, frequency: number) {
    const [startStr] = dateStr.split(" to ");
    const [monthStr, yearStr] = startStr.split("-");

    // Parse the date in Luxon (using 'Jan-2022' format)
    const startDate = DateTime.fromFormat(`${monthStr}-${yearStr}`, "MMM-yyyy");

    // Generate the previous months based on frequency
    const previousMonths = [];
    for (let i = 0; i <= frequency; i++) {
      const previousMonth = startDate.plus({months: i});
      previousMonths.push(previousMonth.toFormat("MM-yyyy"));
    }

    return previousMonths;
  }
}
