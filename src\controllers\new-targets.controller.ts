import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewTargets} from '../models';
import {NewTargetsRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewTargetsController {
  constructor(
    @repository(NewTargetsRepository)
    public newTargetsRepository : NewTargetsRepository,
  ) {}

  @post('/new-targets')
  @response(200, {
    description: 'NewTargets model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewTargets)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTargets, {
            title: 'NewNewTargets',
            exclude: ['id'],
          }),
        },
      },
    })
    newTargets: Omit<NewTargets, 'id'>,
  ): Promise<NewTargets> {
    return this.newTargetsRepository.create(newTargets);
  }

  @get('/new-targets/count')
  @response(200, {
    description: 'NewTargets model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewTargets) where?: Where<NewTargets>,
  ): Promise<Count> {
    return this.newTargetsRepository.count(where);
  }

  @get('/new-targets')
  @response(200, {
    description: 'Array of NewTargets model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewTargets, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewTargets) filter?: Filter<NewTargets>,
  ): Promise<NewTargets[]> {
    return this.newTargetsRepository.find(filter);
  }

  // @patch('/new-targets')
  // @response(200, {
  //   description: 'NewTargets PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewTargets, {partial: true}),
  //       },
  //     },
  //   })
  //   newTargets: NewTargets,
  //   @param.where(NewTargets) where?: Where<NewTargets>,
  // ): Promise<Count> {
  //   return this.newTargetsRepository.updateAll(newTargets, where);
  // }

  @get('/new-targets/{id}')
  @response(200, {
    description: 'NewTargets model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewTargets, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewTargets, {exclude: 'where'}) filter?: FilterExcludingWhere<NewTargets>
  ): Promise<NewTargets> {
    return this.newTargetsRepository.findById(id, filter);
  }

  @patch('/new-targets/{id}')
  @response(204, {
    description: 'NewTargets PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTargets, {partial: true}),
        },
      },
    })
    newTargets: NewTargets,
  ): Promise<void> {
    await this.newTargetsRepository.updateById(id, newTargets);
  }

  @put('/new-targets/{id}')
  @response(204, {
    description: 'NewTargets PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newTargets: NewTargets,
  ): Promise<void> {
    await this.newTargetsRepository.replaceById(id, newTargets);
  }

  @del('/new-targets/{id}')
  @response(204, {
    description: 'NewTargets DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newTargetsRepository.deleteById(id);
  }
}
