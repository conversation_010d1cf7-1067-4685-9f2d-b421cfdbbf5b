import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {QualitativeApproval} from '../models';
import {QualitativeApprovalRepository} from '../repositories';

export class QualitativeApprovalController {
  constructor(
    @repository(QualitativeApprovalRepository)
    public qualitativeApprovalRepository: QualitativeApprovalRepository,
  ) { }

  @post('/qualitative-approvals')
  @response(200, {
    description: 'QualitativeApproval model instance',
    content: {'application/json': {schema: getModelSchemaRef(QualitativeApproval)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeApproval, {
            title: 'NewQualitativeApproval',
            exclude: ['id'],
          }),
        },
      },
    })
    qualitativeApproval: Omit<QualitativeApproval, 'id'>,
  ): Promise<QualitativeApproval> {
    return this.qualitativeApprovalRepository.create(qualitativeApproval);
  }

  @get('/qualitative-approvals/count')
  @response(200, {
    description: 'QualitativeApproval model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QualitativeApproval) where?: Where<QualitativeApproval>,
  ): Promise<Count> {
    return this.qualitativeApprovalRepository.count(where);
  }

  @get('/qualitative-approvals')
  @response(200, {
    description: 'Array of QualitativeApproval model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QualitativeApproval, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QualitativeApproval) filter?: Filter<QualitativeApproval>,
  ): Promise<QualitativeApproval[]> {
    return this.qualitativeApprovalRepository.find(filter);
  }

  @patch('/qualitative-approvals')
  @response(200, {
    description: 'QualitativeApproval PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeApproval, {partial: true}),
        },
      },
    })
    qualitativeApproval: QualitativeApproval,
    @param.where(QualitativeApproval) where?: Where<QualitativeApproval>,
  ): Promise<Count> {
    return this.qualitativeApprovalRepository.updateAll(qualitativeApproval, where);
  }

  @get('/qualitative-approvals/{id}')
  @response(200, {
    description: 'QualitativeApproval model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QualitativeApproval, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QualitativeApproval, {exclude: 'where'}) filter?: FilterExcludingWhere<QualitativeApproval>
  ): Promise<QualitativeApproval> {
    return this.qualitativeApprovalRepository.findById(id, filter);
  }

  @patch('/qualitative-approvals/{id}')
  @response(204, {
    description: 'QualitativeApproval PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeApproval, {partial: true}),
        },
      },
    })
    qualitativeApproval: QualitativeApproval,
  ): Promise<void> {
    await this.qualitativeApprovalRepository.updateById(id, qualitativeApproval);
  }

  @put('/qualitative-approvals/{id}')
  @response(204, {
    description: 'QualitativeApproval PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() qualitativeApproval: QualitativeApproval,
  ): Promise<void> {
    await this.qualitativeApprovalRepository.replaceById(id, qualitativeApproval);
  }

  @del('/qualitative-approvals/{id}')
  @response(204, {
    description: 'QualitativeApproval DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.qualitativeApprovalRepository.deleteById(id);
  }
  @post('/qualitative-approval-submission-custom')
  @response(200, {
    description: 'Qualitative Approval',

  })
  async updateQLApproval(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              reporting_year: {
                type: 'number',
              },
              userProfileId: {
                type: 'number',
              },

              dfId: {
                type: 'number',
              },
              categoryId: {
                type: 'number',
              },
              topicId: {
                type: 'number',
              },
              indicatorId: {
                type: 'number',
              },
            },
            required: ['reporting_year', 'dfId', 'userProfileId', 'categoryId', 'topicId', 'indicatorId'],
          }
        }
      },
    })
    requestBody: QualitativeApproval
  ): Promise<any> {
    const {reporting_year, dfId, userProfileId, categoryId, topicId, indicatorId, type, response, edit, last_modified_on, last_modified_by} = requestBody
    let find = await this.qualitativeApprovalRepository.findOne({where: {userProfileId, dfId, categoryId, topicId, indicatorId, reporting_year}})
    console.log(find, 'find')
    if (find) {
      let obj: any = {}
      if (response) {
        obj['response'] = response
      }
      if (edit !== undefined) {
        obj['edit'] = edit

      }
      let result = await this.qualitativeApprovalRepository.updateById(find.id, {...obj, type, last_modified_on, last_modified_by})
      return {result: true, old: true, message: 'Updated'}
    } else {
      let result = await this.qualitativeApprovalRepository.create(requestBody)
      return {result: true, old: false, message: 'Updated'}
    }
  }
  @post('/qualitative-approval-submission-custom-indirect')
  @response(200, {
    description: 'Qualitative Approval',

  })
  async updateQLApprovalIndirect(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              reporting_year: {
                type: 'number',
              },
              userProfileId: {
                type: 'number',
              },

              dfId: {
                type: 'number',
              },
              categoryId: {
                type: 'number',
              },
              topicId: {
                type: 'number',
              },
              indicatorId: {
                type: 'number',
              },
            },
            required: ['reporting_year', 'dfId', 'userProfileId', 'categoryId', 'topicId', 'indicatorId'],
          }
        }
      },
    })
    requestBody: QualitativeApproval
  ): Promise<any> {
    const {reporting_year, dfId, userProfileId, categoryId, topicId, indicatorId, type, response, edit, last_modified_on, last_modified_by} = requestBody
    let find = await this.qualitativeApprovalRepository.findOne({where: {userProfileId, dfId, categoryId, topicId, indicatorId, reporting_year}})
    console.log(find, 'find')
    if (find) {
      let result = await this.qualitativeApprovalRepository.updateById(find.id, {response, edit, type, last_modified_on, last_modified_by})
      return {result: true, old: true, message: 'Updated'}
    } else {
      let result = await this.qualitativeApprovalRepository.create(requestBody)
      return {result: true, old: false, message: 'Updated'}
    }
  }
}
