import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEfSubcategory4, NewEfSubcategory4Relations} from '../models';

export class NewEfSubcategory4Repository extends DefaultCrudRepository<
  NewEfSubcategory4,
  typeof NewEfSubcategory4.prototype.id,
  NewEfSubcategory4Relations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(NewEfSubcategory4, dataSource);
  }
}
