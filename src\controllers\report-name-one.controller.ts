import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ReportNameOne} from '../models';
import {ReportNameOneRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ReportNameOneController {
  constructor(
    @repository(ReportNameOneRepository)
    public reportNameOneRepository : ReportNameOneRepository,
  ) {}

  @post('/report-name-ones')
  @response(200, {
    description: 'ReportNameOne model instance',
    content: {'application/json': {schema: getModelSchemaRef(ReportNameOne)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportNameOne, {
            title: 'NewReportNameOne',
            exclude: ['id'],
          }),
        },
      },
    })
    reportNameOne: Omit<ReportNameOne, 'id'>,
  ): Promise<ReportNameOne> {
    return this.reportNameOneRepository.create(reportNameOne);
  }

  @get('/report-name-ones/count')
  @response(200, {
    description: 'ReportNameOne model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ReportNameOne) where?: Where<ReportNameOne>,
  ): Promise<Count> {
    return this.reportNameOneRepository.count(where);
  }

  @get('/report-name-ones')
  @response(200, {
    description: 'Array of ReportNameOne model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportNameOne, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ReportNameOne) filter?: Filter<ReportNameOne>,
  ): Promise<ReportNameOne[]> {
    return this.reportNameOneRepository.find(filter);
  }

  // @patch('/report-name-ones')
  // @response(200, {
  //   description: 'ReportNameOne PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ReportNameOne, {partial: true}),
  //       },
  //     },
  //   })
  //   reportNameOne: ReportNameOne,
  //   @param.where(ReportNameOne) where?: Where<ReportNameOne>,
  // ): Promise<Count> {
  //   return this.reportNameOneRepository.updateAll(reportNameOne, where);
  // }

  @get('/report-name-ones/{id}')
  @response(200, {
    description: 'ReportNameOne model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ReportNameOne, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ReportNameOne, {exclude: 'where'}) filter?: FilterExcludingWhere<ReportNameOne>
  ): Promise<ReportNameOne> {
    return this.reportNameOneRepository.findById(id, filter);
  }

  @patch('/report-name-ones/{id}')
  @response(204, {
    description: 'ReportNameOne PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportNameOne, {partial: true}),
        },
      },
    })
    reportNameOne: ReportNameOne,
  ): Promise<void> {
    await this.reportNameOneRepository.updateById(id, reportNameOne);
  }

  @put('/report-name-ones/{id}')
  @response(204, {
    description: 'ReportNameOne PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() reportNameOne: ReportNameOne,
  ): Promise<void> {
    await this.reportNameOneRepository.replaceById(id, reportNameOne);
  }

  @del('/report-name-ones/{id}')
  @response(204, {
    description: 'ReportNameOne DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.reportNameOneRepository.deleteById(id);
  }
}
