import {BindingScope, injectable, Provider} from '@loopback/core';
import {DateTime} from 'luxon';

export interface Helper {
  generateHtmlTable(data: Record<string, any>[]): string;
  getRPTextFormat(data: any): any;
  filterSubmissionsByFiscalYear(submissions: any[], year: number, fymonth: number): any;
  getSortedEntity(level: number, locationId: number, locations: any[]): any;
  getReportingFiscalYearByReportingperiod(reporting_period: any, fymonth: any): any,
  isNextMonthDay(reporting_period: string, day: number): boolean,
  filterDCFAssignmentsByMonthRange(assignments: any[],
    startMonthStr: string,
    endMonthStr: string): any[],
  filterByReportingPeriodByMonthRange(assignment: any[], startMonthStr: string,
    endMonthStr: string): any[],
  generateReportingPeriods(
    startMonthStr: string,
    endMonthStr: string,
    assignments: any[],
    submittedData: any[]
  ): any[]
}

@injectable({scope: BindingScope.TRANSIENT})
export class HelperProvider implements Provider<Helper> {
  constructor(

  ) { }

  value(): Helper {
    const self = this;
    return {
      generateHtmlTable(data: Record<string, any>[]): string {
        if (!data || data.length === 0) return '<p>No data available</p>';

        const headers = Object.keys(data[0]);
        const thead = `
          <thead>
            <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
          </thead>
        `;
        const tbody = `
          <tbody>
            ${data
            .map(row => {
              return `<tr>${headers
                .map(h => `<td>${row[h] ?? ''}</td>`)
                .join('')}</tr>`;
            })
            .join('')}
          </tbody>
        `;

        return `<table border="1" cellpadding="6" cellspacing="0" style="border-collapse: collapse;">${thead}${tbody}</table>`;
      },
      getRPTextFormat(item: any): any {
        if (!item) {return ''}
        if (item.length !== 0) {
          if (item.length >= 2) {
            const startDate = DateTime.fromFormat(item[0], "MM-yyyy").toFormat(
              "LLL-yyyy"
            );
            const endDate = DateTime.fromFormat(
              item[item.length - 1],
              "MM-yyyy"
            ).toFormat("LLL-yyyy");
            return `${startDate} to ${endDate}`;
          } else {
            return DateTime.fromFormat(item[0], "MM-yyyy").toFormat("LLL-yyyy");
          }
        }
      },
      filterSubmissionsByFiscalYear(submissions: any[], year: number, fymonth: number) {
        const {startDate, endDate} = getFiscalYearRange(year, fymonth);

        return submissions.filter(submission => {
          const allDatesWithinRange = submission.reporting_period.every((period: any) => {
            const periodDate = DateTime.fromFormat(period, 'MM-yyyy').startOf('month');
            return periodDate >= startDate && periodDate <= endDate;
          });

          return allDatesWithinRange;
        });
      }, getSortedEntity(level: any, locationId: any, locations: any) {


        const locationMap = new Map(
          locations.map((loc: any) => [
            loc.id,
            loc.name,
          ]),
        );

        const locationTwoMap = new Map(
          locations.flatMap(
            (loc: any) =>
              loc.locationTwos?.map((locTwo: any) => [
                locTwo.id,
                locTwo.name,
              ]) || [],
          ),
        );

        const locationThreeMap = new Map(
          locations.flatMap(
            (loc: any) =>
              loc.locationTwos?.flatMap(
                (locTwo: any) =>
                  locTwo.locationThrees?.map((locThree: any) => [
                    locThree.id,
                    locThree.name,
                  ]) || [],
              ) || [],
          ),
        );

        return (
          (level === 0 && 'Corporate') ||
          (level === 1 && locationMap.get(locationId)) ||
          (level === 2 && locationTwoMap.get(locationId)) ||
          (level === 3 && locationThreeMap.get(locationId)) ||
          'No Data Found'
        );
      }, getReportingFiscalYearByReportingperiod(reporting_period: any, fymonth: any) {
        const parseDate = (str: any) => {
          // Try both formats to parse different date strings
          return DateTime.fromFormat(str, "MMM-yyyy").isValid
            ? DateTime.fromFormat(str, "MMM-yyyy")
            : DateTime.fromFormat(str, "MM-yyyy");
        };

        const determineFiscalYear = (date: any) => {
          // Determine fiscal year based on fymonth
          if (fymonth === 1) {
            return date.year; // Calendar year if fiscal year starts in January
          } else {
            const fiscalYearStart = DateTime.fromObject({year: date.year, month: fymonth, day: 1});
            return date >= fiscalYearStart ? date.year + 1 : date.year;
          }
        };

        // If reporting_period is an array, parse the last date in the array
        if (Array.isArray(reporting_period)) {
          const lastPeriod = reporting_period[reporting_period.length - 1];
          const date = parseDate(lastPeriod);
          return date.isValid ? determineFiscalYear(date) : null;
        }

        // If reporting_period is a single date or range string
        if (typeof reporting_period === "string") {
          if (reporting_period.includes(" to ")) {
            // Parse the end date in the range "Jan-2022 to Mar-2022"
            const [, endDateStr] = reporting_period.split(" to ");
            const endDate = parseDate(endDateStr.trim());
            return endDate.isValid ? determineFiscalYear(endDate) : null;
          } else {
            // Single date format, e.g., "Jan-2022" or "02-2022"
            const date = parseDate(reporting_period);
            return date.isValid ? determineFiscalYear(date) : null;
          }
        }

        return null; // Return null if format is invalid
      }, isNextMonthDay(dateStr: string, day: number): boolean {
        const today = DateTime.now().startOf('day');

        let endMonth: DateTime;

        if (dateStr.includes('to')) {
          const parts = dateStr.split('to').map(part => part.trim());
          if (parts.length !== 2) return false;

          endMonth = DateTime.fromFormat(parts[1], 'MMM-yyyy');
        } else {
          endMonth = DateTime.fromFormat(dateStr.trim(), 'MMM-yyyy');
        }

        if (!endMonth.isValid) return false;

        // Do not allow future end months
        const currentMonth = today.startOf('month');
        if (endMonth > currentMonth) return false;

        // Add 1 month + n days to the end month
        const targetDate = endMonth.plus({months: 1}).startOf('month').plus({days: day});

        // Return true if today is same or after targetDate
        return today >= targetDate;
      }, filterDCFAssignmentsByMonthRange(
        assignments: any[],
        startMonthStr: string,
        endMonthStr: string
      ): any[] {
        const parseMonthYear = (str: string): Date => {
          const [month, year] = str.split('-');
          return new Date(`${month} 1, ${year}`);
        };

        const addOneDay = (date: Date): Date => {
          const copy = new Date(date);
          copy.setDate(copy.getDate() + 1);
          return copy;
        };

        const startDate = parseMonthYear(startMonthStr);
        const endDate = parseMonthYear(endMonthStr);
        endDate.setMonth(endDate.getMonth() + 1); // to include the end month fully

        return assignments.filter((assignment) => {
          const assignmentStart = addOneDay(new Date(assignment.start_date));
          const assignmentEnd = assignment.end_date
            ? addOneDay(new Date(assignment.end_date))
            : addOneDay(new Date()); // treat null as current date

          return assignmentEnd >= startDate && assignmentStart < endDate;
        });
      }, filterByReportingPeriodByMonthRange(
        data: any[],
        startMonth: string, endMonth: string
      ): any[] {
        const start = DateTime.fromFormat(startMonth, 'MMM-yyyy').startOf('month');
        const end = DateTime.fromFormat(endMonth, 'MMM-yyyy').endOf('month');

        return data.filter(item =>
          item.reporting_period.some((period: any) => {
            const periodDate = DateTime.fromFormat(period, 'MM-yyyy').startOf('month');
            return periodDate >= start && periodDate <= end;
          })
        );
      }, generateReportingPeriods(
        startMonthStr: string,
        endMonthStr: string,
        assignments: any[],
        submittedData: any[]
      ): any[] {
        const startMonth = DateTime.fromFormat(startMonthStr, "MMM-yyyy").startOf("month");
        const endMonth = DateTime.fromFormat(endMonthStr, "MMM-yyyy").endOf("month");

        const freqOffset: Record<number, {months?: number; years?: number}> = {
          1: {months: 1},
          2: {months: 2},
          3: {months: 3},
          5: {months: 6},
          4: {years: 1},
        };

        // 🔄 Preprocess submitted data into a Map for faster lookup
        const submittedMap = new Map<string, any[]>();

        for (const entry of submittedData) {
          const key = `${entry.dcfId}_${entry.level}_${entry.locationId}_${entry.rp?.join("_")}`;
          if (!submittedMap.has(key)) submittedMap.set(key, []);
          submittedMap.get(key)!.push(entry);
        }

        const result: any[] = [];

        for (const assignment of assignments) {
          const freq = assignment.frequency;
          const offset = freqOffset[freq];
          if (!offset) continue;

          const assignmentStart = DateTime.fromISO(assignment.start_date).startOf("month");
          const assignmentEnd = assignment.end_date
            ? DateTime.fromISO(assignment.end_date).endOf("month")
            : endMonth;

          let current = assignmentStart < startMonth ? startMonth : assignmentStart;
          const final = assignmentEnd > endMonth ? endMonth : assignmentEnd;

          while (current <= final) {
            const startPeriod = current;
            const endPeriod = current.plus(offset).minus({days: 1}).endOf("month");
            const adjustedEnd = endPeriod > final ? final : endPeriod;

            const reporting_period =
              freq === 1
                ? startPeriod.toFormat("MMM-yyyy")
                : `${startPeriod.toFormat("MMM-yyyy")} to ${adjustedEnd.toFormat("MMM-yyyy")}`;

            // Build lookup key for Map (same format as above)
            const reportingPeriodRaw =
              freq === 1
                ? [startPeriod.toFormat("MM-yyyy")]
                : [
                  startPeriod.toFormat("MM-yyyy"),
                  adjustedEnd.toFormat("MM-yyyy")
                ];

            const key = `${assignment.dcfId}_${assignment.level}_${assignment.locationId}_${reportingPeriodRaw.join("_")}`;
            const matchingData = submittedMap.get(key) || [];

            result.push({
              ...assignment,
              reporting_period,
              data: matchingData,
            });

            current = current.plus(offset);
          }
        }

        return result;
      }



    };
  }
}
function getFiscalYearRange(year: number, fymonth: number) {
  let startDate, endDate;


  if (fymonth === 1) {
    startDate = DateTime.fromObject({year, month: 1, day: 1}).startOf('day');
    endDate = DateTime.fromObject({year, month: 12, day: 31}).endOf('day');
  } else {
    startDate = DateTime.fromObject({year: year - 1, month: fymonth, day: 1}).startOf('day');
    endDate = DateTime.fromObject({year, month: fymonth - 1, day: 1}).endOf('month');
  }

  return {startDate, endDate};
};
