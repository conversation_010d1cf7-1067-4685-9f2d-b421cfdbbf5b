import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SupplierActionHistory, SupplierActionHistoryRelations, VendorCode} from '../models';
import {VendorCodeRepository} from './vendor-code.repository';

export class SupplierActionHistoryRepository extends DefaultCrudRepository<
  SupplierActionHistory,
  typeof SupplierActionHistory.prototype.id,
  SupplierActionHistoryRelations
> {

  public readonly vendor: BelongsToAccessor<VendorCode, typeof SupplierActionHistory.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>,
  ) {
    super(SupplierActionHistory, dataSource);
    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
  }
}
