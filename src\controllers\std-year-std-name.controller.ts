import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  StdYear,
  StdName,
} from '../models';
import {StdYearRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdYearStdNameController {
  constructor(
    @repository(StdYearRepository) protected stdYearRepository: StdYearRepository,
  ) { }

  @get('/std-years/{id}/std-names', {
    responses: {
      '200': {
        description: 'Array of StdYear has many StdName',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(StdName)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<StdName>,
  ): Promise<StdName[]> {
    return this.stdYearRepository.stdNames(id).find(filter);
  }

  @post('/std-years/{id}/std-names', {
    responses: {
      '200': {
        description: 'StdYear model instance',
        content: {'application/json': {schema: getModelSchemaRef(StdName)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof StdYear.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdName, {
            title: 'NewStdNameInStdYear',
            exclude: ['id'],
            optional: ['stdYearId']
          }),
        },
      },
    }) stdName: Omit<StdName, 'id'>,
  ): Promise<StdName> {
    return this.stdYearRepository.stdNames(id).create(stdName);
  }

  // @patch('/std-years/{id}/std-names', {
  //   responses: {
  //     '200': {
  //       description: 'StdYear.StdName PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StdName, {partial: true}),
  //       },
  //     },
  //   })
  //   stdName: Partial<StdName>,
  //   @param.query.object('where', getWhereSchemaFor(StdName)) where?: Where<StdName>,
  // ): Promise<Count> {
  //   return this.stdYearRepository.stdNames(id).patch(stdName, where);
  // }

  // @del('/std-years/{id}/std-names', {
  //   responses: {
  //     '200': {
  //       description: 'StdYear.StdName DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(StdName)) where?: Where<StdName>,
  // ): Promise<Count> {
  //   return this.stdYearRepository.stdNames(id).delete(where);
  // }
}
