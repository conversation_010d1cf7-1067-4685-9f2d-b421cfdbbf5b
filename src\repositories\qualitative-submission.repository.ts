import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {QualitativeSubmission, QualitativeSubmissionRelations} from '../models';

export class QualitativeSubmissionRepository extends DefaultCrudRepository<
  QualitativeSubmission,
  typeof QualitativeSubmission.prototype.id,
  QualitativeSubmissionRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(QualitativeSubmission, dataSource);
  }
}
