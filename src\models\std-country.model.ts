import {Entity, model, property, hasMany} from '@loopback/repository';
import {StdYear} from './std-year.model';

@model()
export class StdCountry extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @hasMany(() => StdYear)
  stdYears: StdYear[];

  constructor(data?: Partial<StdCountry>) {
    super(data);
  }
}

export interface StdCountryRelations {
  // describe navigational properties here
}

export type StdCountryWithRelations = StdCountry & StdCountryRelations;
