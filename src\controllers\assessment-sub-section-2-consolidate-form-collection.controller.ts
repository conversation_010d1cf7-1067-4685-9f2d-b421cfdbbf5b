import {
  repository,
} from '@loopback/repository';
import {
  get,
  getModelSchemaRef,
  param,
} from '@loopback/rest';
import {
  AssessmentSubSection2,
  ConsolidateFormCollection,
} from '../models';
import {AssessmentSubSection2Repository} from '../repositories';

export class AssessmentSubSection2ConsolidateFormCollectionController {
  constructor(
    @repository(AssessmentSubSection2Repository)
    public assessmentSubSection2Repository: AssessmentSubSection2Repository,
  ) { }

  @get('/assessment-sub-section2s/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to AssessmentSubSection2',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ConsolidateFormCollection)},
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.string('id') id: typeof AssessmentSubSection2.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.assessmentSubSection2Repository.form(id);
  }
}
