import {belongsTo, Entity, model, property} from '@loopback/repository';
import {AssessmentSection} from './assessment-section.model';
import {AssessmentSubSection1} from './assessment-sub-section1.model';
import {AssessmentSubSection2} from './assessment-sub-section2.model';
import {DealerResponseForm} from './dealer-response-form.model';

@model()
export class SupplierSectionSubmission extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;


  @property({
    type: 'number',
  })
  status?: number;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  vendorCode?: string | null;
  @property({
    type: 'number',
  })
  type?: number;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_by?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;
  @property({
    type: 'string',

  })
  submitted_on?: string;
  @property({
    type: 'number',

  })
  submitted_by?: number;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  data1?: string;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'string',
  })
  supplierAssessmentAssignmentId?: string;

  @belongsTo(() => AssessmentSection)
  assessmentSectionId: string;

  @belongsTo(() => AssessmentSubSection1)
  assessmentSubSection1Id: string;

  @belongsTo(() => AssessmentSubSection2)
  assessmentSubSection2Id: string;

  @belongsTo(() => DealerResponseForm)
  dealerResponseFormId: number;

  constructor(data?: Partial<SupplierSectionSubmission>) {
    super(data);
  }
}

export interface SupplierSectionSubmissionRelations {
  // describe navigational properties here
}

export type SupplierSectionSubmissionWithRelations = SupplierSectionSubmission & SupplierSectionSubmissionRelations;
