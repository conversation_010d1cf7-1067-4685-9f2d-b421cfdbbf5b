import {DateTime} from 'luxon';

interface BusinessUnit {
  id: number;
}
interface Assignment {
  start_date?: string;
  end_date?: string;
  // Add other relevant fields as needed
}
interface Region {
  id: number;
  locationThrees: BusinessUnit[];
}

interface Country {
  id: number;
  locationTwos: Region[];
}

interface ValidTierIds {
  countries: number[];
  regions: number[];
  businessUnits: number[];
}

function getValidTierIds(
  locationData: Country[],
  tier1_id: number,
  tier2_id: number | null,
  tier3_id: number | null
): ValidTierIds {
  const countries = new Set<number>();
  const regions = new Set<number>();
  const businessUnits = new Set<number>();

  locationData.forEach((country) => {
    if (tier1_id === 0 || tier1_id === country.id) {
      countries.add(country.id);

      country?.locationTwos?.forEach((region) => {
        if (tier2_id === 0 || tier2_id === region.id) {
          regions.add(region.id);

          region?.locationThrees?.forEach((businessUnit) => {
            if (
              tier3_id === 0 ||
              (tier2_id === 0 && tier3_id === null) ||
              tier3_id === businessUnit.id
            ) {
              businessUnits.add(businessUnit.id);
            }
          });
        }
      });
    }
  });

  return {
    countries: Array.from(countries),
    regions: Array.from(regions),
    businessUnits: Array.from(businessUnits),
  };
}
function getFiscalYearRange(year: number, fymonth: number) {
  let startDate, endDate;


  if (fymonth === 1) {
    startDate = DateTime.fromObject({year, month: 1, day: 1}).startOf('day');
    endDate = DateTime.fromObject({year, month: 12, day: 31}).endOf('day');
  } else {
    startDate = DateTime.fromObject({year: year - 1, month: fymonth, day: 1}).startOf('day');
    endDate = DateTime.fromObject({year, month: fymonth - 1, day: 1}).endOf('month');
  }

  return {startDate, endDate};
};
function removeDuplicateMonths(arrays: any) {
  const expandedRanges = new Set();

  arrays.forEach((row: any) => {
    row.forEach((period: any) => {
      if (period.includes('to')) {
        const [start, end] = period.split(' to ');
        const rangeMonths = expandRange(start.trim(), end.trim());
        rangeMonths.forEach((month: any) => expandedRanges.add(month));
      }
    });
  });

  return arrays.map((row: any) =>
    row.filter((period: any) => {
      if (period.includes('to')) return true; // Keep ranges
      return !expandedRanges.has(period); // Remove single months if they exist in a range
    })
  );
};
function sortPeriods(periods: string[]): string[] {
  return periods.sort((a, b) => {
    const getStartDate = (period: string): DateTime => {
      const start = period.split(' to ')[0].trim();
      return DateTime.fromFormat(start, 'MMM-yyyy');
    };

    const dateA = getStartDate(a);
    const dateB = getStartDate(b);

    return dateA.toMillis() - dateB.toMillis();
  });
}
const expandRange = (start: string, end: string) => {
  const startDate = DateTime.fromFormat(start, 'MMM-yyyy');
  const endDate = DateTime.fromFormat(end, 'MMM-yyyy');
  const months = [];
  let currentDate = startDate;

  while (currentDate <= endDate) {
    months.push(currentDate.toFormat('MMM-yyyy'));
    currentDate = currentDate.plus({months: 1});
  }

  return months;
};
function parseMonthYear(monthYear: string) {
  return DateTime.fromFormat(monthYear, 'MMM-yyyy').startOf('month');
}
function getMonthRange(period: string): [DateTime, DateTime] {
  // Regular expressions to match single month and range formats
  const rangeRegex = /^([A-Za-z]+-\d{4}) to ([A-Za-z]+-\d{4})$/;
  const singleMonthRegex = /^([A-Za-z]+-\d{4})$/;

  // Function to parse 'MMM-yyyy' format into a DateTime object
  const parseMonthYear = (monthYear: string): DateTime => {
    const dt = DateTime.fromFormat(monthYear, 'MMM-yyyy', {zone: 'utc'});
    if (!dt.isValid) {
      throw new Error(`Invalid date format: ${monthYear}`);
    }
    return dt;
  };

  // Check if the period matches a single month format
  if (singleMonthRegex.test(period)) {
    const date = parseMonthYear(period);
    return [date.startOf('month'), date.endOf('month')];
  }
  // Check if the period matches a range format
  else if (rangeRegex.test(period)) {
    const match = period.match(rangeRegex);
    if (!match) {
      throw new Error(`Invalid date range format: ${period}`);
    }
    const [, startMonthYear, endMonthYear] = match;
    const start = parseMonthYear(startMonthYear).startOf('month');
    const end = parseMonthYear(endMonthYear).endOf('month');
    return [start, end];
  } else {
    throw new Error(`Invalid period format: ${period}`);
  }
}
function parseReportingPeriod(period: string) {
  return DateTime.fromFormat(period, 'MM-yyyy').startOf('month');
}
export function filterByReportingPeriods(dateString: string, objects: any[]) {
  const [startRange, endRange] = getMonthRange(dateString);
  return objects.filter(obj => {
    return obj.reporting_period.every((period: any) => {
      const periodDate = parseReportingPeriod(period);
      return periodDate >= startRange && periodDate <= endRange;
    });
  });
}
// Main filtering function using Luxon
export function filterObjectsByDateRange(dateString: string, objects: any[]) {
  const [startRange, endRange] = getMonthRange(dateString);

  return objects.filter(obj => {
    const objStart = DateTime.fromISO(obj.start_date);
    const objEnd = obj.end_date ? DateTime.fromISO(obj.end_date) : DateTime.utc(); // Use current date if end_date is null

    return objStart <= endRange && objEnd >= startRange;
  });
}
export function filterDataByTierAndLocationByLevel(
  data: any,
  locationData: any,
  tier1_id: any,
  tier2_id: any,
  tier3_id: any
): any[] {
  if (tier1_id === 0 && tier2_id === null && tier3_id === null) {
    return data; // If tier is 0, null, null return the given data
  }

  const {countries, regions, businessUnits} = getValidTierIds(locationData, tier1_id, tier2_id, tier3_id);


  return data.filter((item: any) => {
    if (tier1_id !== 0 && tier2_id === 0 && tier3_id === null) {
      // Case when we want all regions and sites under a country
      return (item.level === 1 && countries.includes(item.locationId)) ||
        (item.level === 2 && regions.includes(item.locationId)) ||
        (item.level === 3 && businessUnits.includes(item.locationId));
    } else if (tier1_id !== 0 && tier2_id !== 0 && tier3_id === 0) {
      // Case when we want a specific region and all its sites
      return (item.level === 2 && regions.includes(item.locationId)) ||
        (item.level === 3 && businessUnits.includes(item.locationId));
    } else if (tier1_id !== 0 && tier2_id !== 0 && tier3_id !== 0) {
      // Case when we want a specific site
      return item.level === 3 && businessUnits.includes(item.locationId);
    } else {
      // Case when we want only the specific country
      return item.level === 1 && countries.includes(item.locationId);
    }
  });
}
export function filterAssignmentsByFiscalYear(
  assignments: any[],
  year: number,
  fymonth: number
): Assignment[] {
  // Assume getFiscalYearRange is a function that returns the fiscal year's start and end dates
  const {startDate, endDate} = getFiscalYearRange(year, fymonth);
  const currentDate = DateTime.local().startOf('day');

  return assignments.filter(assignment => {
    const assignmentStartDate = assignment.start_date
      ? DateTime.fromISO(assignment.start_date, {zone: 'utc'}).startOf('day')
      : currentDate;
    const assignmentEndDate = assignment.end_date
      ? DateTime.fromISO(assignment.end_date, {zone: 'utc'}).startOf('day')
      : currentDate;

    return (
      (assignmentStartDate >= startDate && assignmentStartDate <= endDate) ||
      (assignmentEndDate >= startDate && assignmentEndDate <= endDate) ||
      (assignmentStartDate <= startDate && assignmentEndDate >= endDate)
    );
  });
}
export function getPeriodsForAssignment(startDate: any, endDate: any, frequency: any, fymonth: number, year: number) {

  let start = DateTime.fromISO(startDate).toLocal();
  let fiscalYearStart = null
  if (fymonth === 1) {
    fiscalYearStart = DateTime.fromObject({year, month: 1, day: 1});
  } else {
    fiscalYearStart = DateTime.fromObject({year: year - 1, month: fymonth, day: 1});
  }
  // If start date is earlier than the fiscal year start, adjust it
  if (!year) {

  } else if (start < fiscalYearStart) {
    start = fiscalYearStart;
  }
  // Determine the current year
  const currentYear = DateTime.now().year;

  // If endDate is null, compute based on fiscal year and year condition
  if (!year) {
    if (!endDate) {
      endDate = DateTime.utc()
    }

  } else if (!endDate) {


    // Set endDate based on fymonth and the adjusted endYear
    if (fymonth === 1) {
      endDate = DateTime.fromObject({year, month: 12, day: 31}); // Dec of the specified year
    } else {
      endDate = DateTime.fromObject({year, month: fymonth - 1, day: 31}); // Mar(fymonth-1)-year
    }
  }

  let end = DateTime.fromISO(endDate); // Now `end` is defined either from input or calculated

  let periods = [];

  while (start <= end) {
    let periodEnd = start.plus({months: frequency - 1});
    if (periodEnd > end) {
      periodEnd = end;
    }

    // Create a period string
    const period =
      frequency === 1
        ? start.toFormat('MMM-yyyy')
        : `${start.toFormat('MMM-yyyy')} to ${periodEnd.toFormat('MMM-yyyy')}`;
    periods.push(period);

    // Move to the next period
    start = start.plus({months: frequency});
  }

  return periods;
};
export function generateApprovedPeriods(arrays: any) {
  const cleanedData = removeDuplicateMonths(arrays);
  const periodMap = new Map();

  // Populate the map with the longest period found
  cleanedData.forEach((row: any) => {
    row.forEach((period: any) => {
      const periodKey = period.replace(/\sto\s/g, ' to '); // Normalize spacing for comparison
      const [start, end] = periodKey.includes('to') ? periodKey.split(' to ') : [periodKey, periodKey];

      if (periodMap.has(start)) {
        const currentEnd = periodMap.get(start);
        if (DateTime.fromFormat(end, 'MMM-yyyy') > DateTime.fromFormat(currentEnd, 'MMM-yyyy')) {
          periodMap.set(start, end); // Update with longer range
        }
      } else {
        periodMap.set(start, end); // Add new period
      }
    });
  });

  // Convert back to the "start to end" format
  const result: any = [];
  periodMap.forEach((end, start) => {
    if (start === end) {
      result.push(start);
    } else {
      result.push(`${start} to ${end}`);
    }
  });

  return sortPeriods(result);
};

export function getPeriodsForAssignment_filtered(startDate: string, endDate: any, frequency: any, fymonth: number, year: number, targetDateStr: string) {
  let start = DateTime.fromISO(startDate).toLocal();
  let fiscalYearStart = fymonth === 1
    ? DateTime.fromObject({year, month: 1, day: 1})
    : DateTime.fromObject({year: year - 1, month: fymonth, day: 1});
  if (!year) {

  } else if (start < fiscalYearStart) start = fiscalYearStart;
  if (!year) {
    if (!endDate) {
      endDate = DateTime.utc()
    }
  } else if (!endDate) {
    endDate = fymonth === 1
      ? DateTime.fromObject({year, month: 12, day: 31})
      : DateTime.fromObject({year, month: fymonth - 1, day: 31});
  }

  let end = DateTime.fromISO(endDate);
  let periods = [];

  // Parse the target date range directly
  const [targetStartStr, targetEndStr] = targetDateStr.split(' to ');
  const targetStart = DateTime.fromFormat(targetStartStr, 'MMM-yyyy');
  const targetEnd = targetEndStr ? DateTime.fromFormat(targetEndStr, 'MMM-yyyy') : targetStart;

  while (start <= end) {
    let periodEnd = start.plus({months: frequency - 1});
    if (periodEnd > end) periodEnd = end;

    const period =
      frequency === 1
        ? start.toFormat('MMM-yyyy')
        : `${start.toFormat('MMM-yyyy')} to ${periodEnd.toFormat('MMM-yyyy')}`;

    // Check if the period overlaps or matches the target date range
    const currentStart = start;
    const currentEnd = periodEnd;

    if (
      (currentStart <= targetEnd && currentEnd >= targetStart) // Checks for any overlap or match
    ) {
      periods.push(period);
    }

    start = start.plus({months: frequency});
  }

  return periods;
};

export function getRPTextFormat(item: any) {
  if (item.length !== 0) {
    if (item.length >= 2) {

      const startDate = DateTime.fromFormat(item[0], 'MM-yyyy').toFormat('LLL-yyyy');
      const endDate = DateTime.fromFormat(item[item.length - 1], 'MM-yyyy').toFormat('LLL-yyyy');
      return `${startDate} to ${endDate}`;
    } else {
      return DateTime.fromFormat(item[0], item[0].length === 6 ? "M-yyyy" : 'MM-yyyy').toFormat('LLL-yyyy');
    }
  }
};
export function getOverdueDays(monthString: any) {

  const [startMonth, endMonth] = monthString.split(' to ');

  const month = endMonth ? endMonth : startMonth;
  const [monthValue, year] = month.split('-');
  const endOfMonth = DateTime.fromObject({year: parseInt(year), month: DateTime.fromFormat(monthValue, 'LLL').month}).endOf('month');
  const currentDate = DateTime.local();
  return endOfMonth.diff(currentDate, 'days').days;
}
export function getDueMonth(dateStr: any) {
  const [from, to] = dateStr.split(' to ')
  if (to) {
    return to
  }
  return from
}
