import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AdUser, AdUserRelations} from '../models';

export class AdUserRepository extends DefaultCrudRepository<
  AdUser,
  typeof AdUser.prototype.id,
  AdUserRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AdUser, dataSource);
  }
}
