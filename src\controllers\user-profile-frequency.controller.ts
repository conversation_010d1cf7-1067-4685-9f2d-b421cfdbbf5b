import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  Frequency,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';




export class UserProfileFrequencyController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/frequencies', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many Frequency',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Frequency)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Frequency>,
  ): Promise<Frequency[]> {
    return this.userProfileRepository.frequencies(id).find(filter);
  }

  @post('/user-profiles/{id}/frequencies', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(Frequency)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Frequency, {
            title: 'NewFrequencyInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) frequency: Omit<Frequency, 'id'>,
  ): Promise<Frequency> {
    return this.userProfileRepository.frequencies(id).create(frequency);
  }

  // @patch('/user-profiles/{id}/frequencies', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.Frequency PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(Frequency, {partial: true}),
  //       },
  //     },
  //   })
  //   frequency: Partial<Frequency>,
  //   @param.query.object('where', getWhereSchemaFor(Frequency)) where?: Where<Frequency>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.frequencies(id).patch(frequency, where);
  // }

  // @del('/user-profiles/{id}/frequencies', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.Frequency DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(Frequency)) where?: Where<Frequency>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.frequencies(id).delete(where);
  // }

}
