import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SubmitRfNew} from '../models';
import {SubmitRfNewRepository} from '../repositories';

export class SubmitRfNewController {
  constructor(
    @repository(SubmitRfNewRepository)
    public submitRfNewRepository: SubmitRfNewRepository,
  ) { }

  @post('/submit-rf-news')
  @response(200, {
    description: 'SubmitRfNew model instance',
    content: {'application/json': {schema: getModelSchemaRef(SubmitRfNew)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitRfNew, {
            title: 'NewSubmitRfNew',
            exclude: ['id'],
          }),
        },
      },
    })
    submitRfNew: Omit<SubmitRfNew, 'id'>,
  ): Promise<SubmitRfNew> {
    return this.submitRfNewRepository.create(submitRfNew);
  }

  @get('/submit-rf-news/count')
  @response(200, {
    description: 'SubmitRfNew model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SubmitRfNew) where?: Where<SubmitRfNew>,
  ): Promise<Count> {
    return this.submitRfNewRepository.count(where);
  }

  @get('/submit-rf-news')
  @response(200, {
    description: 'Array of SubmitRfNew model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SubmitRfNew, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SubmitRfNew) filter?: Filter<SubmitRfNew>,
  ): Promise<SubmitRfNew[]> {
    return this.submitRfNewRepository.find(filter);
  }

  // @patch('/submit-rf-news')
  // @response(200, {
  //   description: 'SubmitRfNew PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SubmitRfNew, {partial: true}),
  //       },
  //     },
  //   })
  //   submitRfNew: SubmitRfNew,
  //   @param.where(SubmitRfNew) where?: Where<SubmitRfNew>,
  // ): Promise<Count> {
  //   return this.submitRfNewRepository.updateAll(submitRfNew, where);
  // }

  @get('/submit-rf-news/{id}')
  @response(200, {
    description: 'SubmitRfNew model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SubmitRfNew, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SubmitRfNew, {exclude: 'where'}) filter?: FilterExcludingWhere<SubmitRfNew>
  ): Promise<SubmitRfNew> {
    return this.submitRfNewRepository.findById(id, filter);
  }

  @patch('/submit-rf-news/{id}')
  @response(204, {
    description: 'SubmitRfNew PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubmitRfNew, {partial: true}),
        },
      },
    })
    submitRfNew: SubmitRfNew,
  ): Promise<void> {
    await this.submitRfNewRepository.updateById(id, submitRfNew);
  }

  @put('/submit-rf-news/{id}')
  @response(204, {
    description: 'SubmitRfNew PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() submitRfNew: SubmitRfNew,
  ): Promise<void> {
    await this.submitRfNewRepository.replaceById(id, submitRfNew);
  }

  @del('/submit-rf-news/{id}')
  @response(204, {
    description: 'SubmitRfNew DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.submitRfNewRepository.deleteById(id);
  }
}
