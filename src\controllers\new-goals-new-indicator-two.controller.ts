import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewGoals,
  NewIndicatorTwo,
} from '../models';
import {NewGoalsRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewGoalsNewIndicatorTwoController {
  constructor(
    @repository(NewGoalsRepository) protected newGoalsRepository: NewGoalsRepository,
  ) { }

  @get('/new-goals/{id}/new-indicator-twos', {
    responses: {
      '200': {
        description: 'Array of NewGoals has many NewIndicatorTwo',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewIndicatorTwo)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewIndicatorTwo>,
  ): Promise<NewIndicatorTwo[]> {
    return this.newGoalsRepository.newIndicatorTwos(id).find(filter);
  }

  @post('/new-goals/{id}/new-indicator-twos', {
    responses: {
      '200': {
        description: 'NewGoals model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewIndicatorTwo)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewGoals.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewIndicatorTwo, {
            title: 'NewNewIndicatorTwoInNewGoals',
            exclude: ['id'],
            optional: ['newGoalsId']
          }),
        },
      },
    }) newIndicatorTwo: Omit<NewIndicatorTwo, 'id'>,
  ): Promise<NewIndicatorTwo> {
    return this.newGoalsRepository.newIndicatorTwos(id).create(newIndicatorTwo);
  }

  // @patch('/new-goals/{id}/new-indicator-twos', {
  //   responses: {
  //     '200': {
  //       description: 'NewGoals.NewIndicatorTwo PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewIndicatorTwo, {partial: true}),
  //       },
  //     },
  //   })
  //   newIndicatorTwo: Partial<NewIndicatorTwo>,
  //   @param.query.object('where', getWhereSchemaFor(NewIndicatorTwo)) where?: Where<NewIndicatorTwo>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.newIndicatorTwos(id).patch(newIndicatorTwo, where);
  // }

  // @del('/new-goals/{id}/new-indicator-twos', {
  //   responses: {
  //     '200': {
  //       description: 'NewGoals.NewIndicatorTwo DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewIndicatorTwo)) where?: Where<NewIndicatorTwo>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.newIndicatorTwos(id).delete(where);
  // }
  
}
