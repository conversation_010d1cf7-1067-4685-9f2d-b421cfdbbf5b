import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewEfSubcategory2} from '../models';
import {NewEfSubcategory2Repository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfSubcategory2Controller {
  constructor(
    @repository(NewEfSubcategory2Repository)
    public newEfSubcategory2Repository : NewEfSubcategory2Repository,
  ) {}

  @post('/new-ef-subcategory2s')
  @response(200, {
    description: 'NewEfSubcategory2 model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEfSubcategory2)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory2, {
            title: 'NewNewEfSubcategory2',
            exclude: ['id'],
          }),
        },
      },
    })
    newEfSubcategory2: Omit<NewEfSubcategory2, 'id'>,
  ): Promise<NewEfSubcategory2> {
    return this.newEfSubcategory2Repository.create(newEfSubcategory2);
  }

  @get('/new-ef-subcategory2s/count')
  @response(200, {
    description: 'NewEfSubcategory2 model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEfSubcategory2) where?: Where<NewEfSubcategory2>,
  ): Promise<Count> {
    return this.newEfSubcategory2Repository.count(where);
  }

  @get('/new-ef-subcategory2s')
  @response(200, {
    description: 'Array of NewEfSubcategory2 model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEfSubcategory2, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEfSubcategory2) filter?: Filter<NewEfSubcategory2>,
  ): Promise<NewEfSubcategory2[]> {
    return this.newEfSubcategory2Repository.find(filter);
  }

  @patch('/new-ef-subcategory2s')
  @response(200, {
    description: 'NewEfSubcategory2 PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory2, {partial: true}),
        },
      },
    })
    newEfSubcategory2: NewEfSubcategory2,
    @param.where(NewEfSubcategory2) where?: Where<NewEfSubcategory2>,
  ): Promise<Count> {
    return this.newEfSubcategory2Repository.updateAll(newEfSubcategory2, where);
  }

  @get('/new-ef-subcategory2s/{id}')
  @response(200, {
    description: 'NewEfSubcategory2 model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEfSubcategory2, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEfSubcategory2, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEfSubcategory2>
  ): Promise<NewEfSubcategory2> {
    return this.newEfSubcategory2Repository.findById(id, filter);
  }

  @patch('/new-ef-subcategory2s/{id}')
  @response(204, {
    description: 'NewEfSubcategory2 PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory2, {partial: true}),
        },
      },
    })
    newEfSubcategory2: NewEfSubcategory2,
  ): Promise<void> {
    await this.newEfSubcategory2Repository.updateById(id, newEfSubcategory2);
  }

  @put('/new-ef-subcategory2s/{id}')
  @response(204, {
    description: 'NewEfSubcategory2 PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEfSubcategory2: NewEfSubcategory2,
  ): Promise<void> {
    await this.newEfSubcategory2Repository.replaceById(id, newEfSubcategory2);
  }

  @del('/new-ef-subcategory2s/{id}')
  @response(204, {
    description: 'NewEfSubcategory2 DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfSubcategory2Repository.deleteById(id);
  }
}
