import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewIndicatorTwo, NewIndicatorTwoRelations} from '../models';

export class NewIndicatorTwoRepository extends DefaultCrudRepository<
  NewIndicatorTwo,
  typeof NewIndicatorTwo.prototype.id,
  NewIndicatorTwoRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(NewIndicatorTwo, dataSource);
  }
}
