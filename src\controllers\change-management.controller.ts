import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ChangeManagement} from '../models';
import {ChangeManagementRepository} from '../repositories';

export class ChangeManagementController {
  constructor(
    @repository(ChangeManagementRepository)
    public changeManagementRepository : ChangeManagementRepository,
  ) {}

  @post('/change-managements')
  @response(200, {
    description: 'ChangeManagement model instance',
    content: {'application/json': {schema: getModelSchemaRef(ChangeManagement)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChangeManagement, {
            title: 'NewChangeManagement',
            exclude: ['id'],
          }),
        },
      },
    })
    changeManagement: Omit<ChangeManagement, 'id'>,
  ): Promise<ChangeManagement> {
    return this.changeManagementRepository.create(changeManagement);
  }

  @get('/change-managements/count')
  @response(200, {
    description: 'ChangeManagement model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ChangeManagement) where?: Where<ChangeManagement>,
  ): Promise<Count> {
    return this.changeManagementRepository.count(where);
  }

  @get('/change-managements')
  @response(200, {
    description: 'Array of ChangeManagement model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ChangeManagement, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ChangeManagement) filter?: Filter<ChangeManagement>,
  ): Promise<ChangeManagement[]> {
    return this.changeManagementRepository.find(filter);
  }

  @patch('/change-managements')
  @response(200, {
    description: 'ChangeManagement PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChangeManagement, {partial: true}),
        },
      },
    })
    changeManagement: ChangeManagement,
    @param.where(ChangeManagement) where?: Where<ChangeManagement>,
  ): Promise<Count> {
    return this.changeManagementRepository.updateAll(changeManagement, where);
  }

  @get('/change-managements/{id}')
  @response(200, {
    description: 'ChangeManagement model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ChangeManagement, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ChangeManagement, {exclude: 'where'}) filter?: FilterExcludingWhere<ChangeManagement>
  ): Promise<ChangeManagement> {
    return this.changeManagementRepository.findById(id, filter);
  }

  @patch('/change-managements/{id}')
  @response(204, {
    description: 'ChangeManagement PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChangeManagement, {partial: true}),
        },
      },
    })
    changeManagement: ChangeManagement,
  ): Promise<void> {
    await this.changeManagementRepository.updateById(id, changeManagement);
  }

  @put('/change-managements/{id}')
  @response(204, {
    description: 'ChangeManagement PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() changeManagement: ChangeManagement,
  ): Promise<void> {
    await this.changeManagementRepository.replaceById(id, changeManagement);
  }

  @del('/change-managements/{id}')
  @response(204, {
    description: 'ChangeManagement DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.changeManagementRepository.deleteById(id);
  }
}
