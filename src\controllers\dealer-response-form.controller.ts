import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {DealerResponseForm} from '../models';
import {DealerResponseFormRepository} from '../repositories';

export class DealerResponseFormController {
  constructor(
    @repository(DealerResponseFormRepository)
    public dealerResponseFormRepository : DealerResponseFormRepository,
  ) {}

  @post('/dealer-response-forms')
  @response(200, {
    description: 'DealerResponseForm model instance',
    content: {'application/json': {schema: getModelSchemaRef(DealerResponseForm)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerResponseForm, {
            title: 'NewDealerResponseForm',
            exclude: ['id'],
          }),
        },
      },
    })
    dealerResponseForm: Omit<DealerResponseForm, 'id'>,
  ): Promise<DealerResponseForm> {
    return this.dealerResponseFormRepository.create(dealerResponseForm);
  }

  @get('/dealer-response-forms/count')
  @response(200, {
    description: 'DealerResponseForm model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(DealerResponseForm) where?: Where<DealerResponseForm>,
  ): Promise<Count> {
    return this.dealerResponseFormRepository.count(where);
  }

  @get('/dealer-response-forms')
  @response(200, {
    description: 'Array of DealerResponseForm model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DealerResponseForm, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DealerResponseForm) filter?: Filter<DealerResponseForm>,
  ): Promise<DealerResponseForm[]> {
    return this.dealerResponseFormRepository.find(filter);
  }

  @patch('/dealer-response-forms')
  @response(200, {
    description: 'DealerResponseForm PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerResponseForm, {partial: true}),
        },
      },
    })
    dealerResponseForm: DealerResponseForm,
    @param.where(DealerResponseForm) where?: Where<DealerResponseForm>,
  ): Promise<Count> {
    return this.dealerResponseFormRepository.updateAll(dealerResponseForm, where);
  }

  @get('/dealer-response-forms/{id}')
  @response(200, {
    description: 'DealerResponseForm model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(DealerResponseForm, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DealerResponseForm, {exclude: 'where'}) filter?: FilterExcludingWhere<DealerResponseForm>
  ): Promise<DealerResponseForm> {
    return this.dealerResponseFormRepository.findById(id, filter);
  }

  @patch('/dealer-response-forms/{id}')
  @response(204, {
    description: 'DealerResponseForm PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerResponseForm, {partial: true}),
        },
      },
    })
    dealerResponseForm: DealerResponseForm,
  ): Promise<void> {
    await this.dealerResponseFormRepository.updateById(id, dealerResponseForm);
  }

  @put('/dealer-response-forms/{id}')
  @response(204, {
    description: 'DealerResponseForm PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() dealerResponseForm: DealerResponseForm,
  ): Promise<void> {
    await this.dealerResponseFormRepository.replaceById(id, dealerResponseForm);
  }

  @del('/dealer-response-forms/{id}')
  @response(204, {
    description: 'DealerResponseForm DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.dealerResponseFormRepository.deleteById(id);
  }
}
