import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {StakeHolder} from '../models';
import {StakeHolderRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';


export class StakeHolderController {
  constructor(
    @repository(StakeHolderRepository)
    public stakeHolderRepository : StakeHolderRepository,
  ) {}

  @post('/stake-holders')
  @response(200, {
    description: 'StakeHolder model instance',
    content: {'application/json': {schema: getModelSchemaRef(StakeHolder)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StakeHolder, {
            title: 'NewStakeHolder',
            exclude: ['id'],
          }),
        },
      },
    })
    stakeHolder: Omit<StakeHolder, 'id'>,
  ): Promise<StakeHolder> {
    return this.stakeHolderRepository.create(stakeHolder);
  }

  @get('/stake-holders/count')
  @response(200, {
    description: 'StakeHolder model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(StakeHolder) where?: Where<StakeHolder>,
  ): Promise<Count> {
    return this.stakeHolderRepository.count(where);
  }

  @get('/stake-holders')
  @response(200, {
    description: 'Array of StakeHolder model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(StakeHolder, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(StakeHolder) filter?: Filter<StakeHolder>,
  ): Promise<StakeHolder[]> {
    return this.stakeHolderRepository.find(filter);
  }

  // @patch('/stake-holders')
  // @response(200, {
  //   description: 'StakeHolder PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StakeHolder, {partial: true}),
  //       },
  //     },
  //   })
  //   stakeHolder: StakeHolder,
  //   @param.where(StakeHolder) where?: Where<StakeHolder>,
  // ): Promise<Count> {
  //   return this.stakeHolderRepository.updateAll(stakeHolder, where);
  // }

  @get('/stake-holders/{id}')
  @response(200, {
    description: 'StakeHolder model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(StakeHolder, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(StakeHolder, {exclude: 'where'}) filter?: FilterExcludingWhere<StakeHolder>
  ): Promise<StakeHolder> {
    return this.stakeHolderRepository.findById(id, filter);
  }

  @patch('/stake-holders/{id}')
  @response(204, {
    description: 'StakeHolder PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StakeHolder, {partial: true}),
        },
      },
    })
    stakeHolder: StakeHolder,
  ): Promise<void> {
    await this.stakeHolderRepository.updateById(id, stakeHolder);
  }

  @put('/stake-holders/{id}')
  @response(204, {
    description: 'StakeHolder PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() stakeHolder: StakeHolder,
  ): Promise<void> {
    await this.stakeHolderRepository.replaceById(id, stakeHolder);
  }

  @del('/stake-holders/{id}')
  @response(204, {
    description: 'StakeHolder DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.stakeHolderRepository.deleteById(id);
  }
}
