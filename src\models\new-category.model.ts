import {Entity, model, property, hasMany} from '@loopback/repository';
import {NewTopic} from './new-topic.model';

@model({settings: {strict: false}})
export class NewCategory extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;

  @property({
    type: 'string',
  })
  data1?: string;

  @property({
    type: 'string',
  })
  data2?: string;

  @property({
    type: 'string',
  })
  extra?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  order?: number;

  @hasMany(() => NewTopic)
  newTopics: NewTopic[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<NewCategory>) {
    super(data);
  }
}

export interface NewCategoryRelations {
  // describe navigational properties here
}

export type NewCategoryWithRelations = NewCategory & NewCategoryRelations;
