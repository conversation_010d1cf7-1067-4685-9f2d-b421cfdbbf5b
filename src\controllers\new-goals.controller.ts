import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewGoals} from '../models';
import {NewGoalsRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewGoalsController {
  constructor(
    @repository(NewGoalsRepository)
    public newGoalsRepository : NewGoalsRepository,
  ) {}

  @post('/new-goals')
  @response(200, {
    description: 'NewGoals model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewGoals)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewGoals, {
            title: 'NewNewGoals',
            exclude: ['id'],
          }),
        },
      },
    })
    newGoals: Omit<NewGoals, 'id'>,
  ): Promise<NewGoals> {
    return this.newGoalsRepository.create(newGoals);
  }

  @get('/new-goals/count')
  @response(200, {
    description: 'NewGoals model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewGoals) where?: Where<NewGoals>,
  ): Promise<Count> {
    return this.newGoalsRepository.count(where);
  }

  @get('/new-goals')
  @response(200, {
    description: 'Array of NewGoals model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewGoals, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewGoals) filter?: Filter<NewGoals>,
  ): Promise<NewGoals[]> {
    return this.newGoalsRepository.find(filter);
  }

  // @patch('/new-goals')
  // @response(200, {
  //   description: 'NewGoals PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewGoals, {partial: true}),
  //       },
  //     },
  //   })
  //   newGoals: NewGoals,
  //   @param.where(NewGoals) where?: Where<NewGoals>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.updateAll(newGoals, where);
  // }

  @get('/new-goals/{id}')
  @response(200, {
    description: 'NewGoals model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewGoals, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewGoals, {exclude: 'where'}) filter?: FilterExcludingWhere<NewGoals>
  ): Promise<NewGoals> {
    return this.newGoalsRepository.findById(id, filter);
  }

  @patch('/new-goals/{id}')
  @response(204, {
    description: 'NewGoals PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewGoals, {partial: true}),
        },
      },
    })
    newGoals: NewGoals,
  ): Promise<void> {
    await this.newGoalsRepository.updateById(id, newGoals);
  }

  @put('/new-goals/{id}')
  @response(204, {
    description: 'NewGoals PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newGoals: NewGoals,
  ): Promise<void> {
    await this.newGoalsRepository.replaceById(id, newGoals);
  }

  @del('/new-goals/{id}')
  @response(204, {
    description: 'NewGoals DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newGoalsRepository.deleteById(id);
  }
}
