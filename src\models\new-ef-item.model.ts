import {Entity, model, property, belongsTo} from '@loopback/repository';
import {NewEfSubcategory1} from './new-ef-subcategory1.model';
import {NewEfSubcategory2} from './new-ef-subcategory2.model';
import {NewEfSubcategory3} from './new-ef-subcategory3.model';
import {NewEfSubcategory4} from './new-ef-subcategory4.model';

@model()
export class NewEfItem extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  unit?: string;
  @property({
    type: 'string',
  })
  created_by?: string;

  @property({
    type: 'string',
  })
  checked_by?: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  methodology?: string;

  @property({
    type: 'array',
    itemType: 'number',
  })
  applicability?: number[];

  @property({
    type: 'string',
  })
  quality?: string;

  @property({
    type: 'string',
  })
  notes?: string;

  @property({
    type: 'any',
  })
  co2e?: any;

  @property({
    type: 'any',
  })
  co2?: any;

  @property({
    type: 'any',
  })
  ch4?: any;

  @property({
    type: 'any',
  })
  n2o?: any;

  @property({
    type: 'any',
  })
  other1?: any;

  @property({
    type: 'any',
  })
  other2?: any;

  @property({
    type: 'any',
  })
  other3?: any;

  @property({
    type: 'any',
  })
  other4?: any;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'any',
  })
  extra1?: any;

  @property({
    type: 'array',
    itemType: 'any',
  })
  extra2?: any[];

  @property({
    type: 'number',
  })
  newEfId?: number;

  @property({
    type: 'string',
  })
  ef_id?: string;

  @belongsTo(() => NewEfSubcategory1, {name: 'subcat1'})
  subcategory1: number;

  @belongsTo(() => NewEfSubcategory2, {name: 'subcat2'})
  subcategory2: number;

  @belongsTo(() => NewEfSubcategory3, {name: 'subcat3'})
  subcategory3: number;

  @belongsTo(() => NewEfSubcategory4, {name: 'subcat4'})
  subcategory4: number;

  constructor(data?: Partial<NewEfItem>) {
    super(data);
  }
}

export interface NewEfItemRelations {
  // describe navigational properties here
}

export type NewEfItemWithRelations = NewEfItem & NewEfItemRelations;
