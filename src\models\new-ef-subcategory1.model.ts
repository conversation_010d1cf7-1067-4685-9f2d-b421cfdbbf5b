import {Entity, model, property, hasMany} from '@loopback/repository';
import {NewEfSubcategory2} from './new-ef-subcategory2.model';

@model()
export class NewEfSubcategory1 extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  extra?: string;

  @property({
    type: 'number',
  })
  newEfCategoryId?: number;

  @hasMany(() => NewEfSubcategory2)
  newEfSubcategory2s: NewEfSubcategory2[];

  constructor(data?: Partial<NewEfSubcategory1>) {
    super(data);
  }
}

export interface NewEfSubcategory1Relations {
  // describe navigational properties here
}

export type NewEfSubcategory1WithRelations = NewEfSubcategory1 & NewEfSubcategory1Relations;
