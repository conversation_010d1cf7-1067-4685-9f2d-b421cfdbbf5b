import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewTopic,
  NewMetric,
} from '../models';
import {NewTopicRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewTopicNewMetricController {
  constructor(
    @repository(NewTopicRepository) protected newTopicRepository: NewTopicRepository,
  ) { }

  @get('/new-topics/{id}/new-metrics', {
    responses: {
      '200': {
        description: 'Array of NewTopic has many NewMetric',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewMetric)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewMetric>,
  ): Promise<NewMetric[]> {
    return this.newTopicRepository.newMetrics(id).find(filter);
  }

  @post('/new-topics/{id}/new-metrics', {
    responses: {
      '200': {
        description: 'NewTopic model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewMetric)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewTopic.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewMetric, {
            title: 'NewNewMetricInNewTopic',
            exclude: ['id'],
            optional: ['newTopicId']
          }),
        },
      },
    }) newMetric: Omit<NewMetric, 'id'>,
  ): Promise<NewMetric> {
    return this.newTopicRepository.newMetrics(id).create(newMetric);
  }

 
  // @patch('/new-topics/{id}/new-metrics', {
  //   responses: {
  //     '200': {
  //       description: 'NewTopic.NewMetric PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewMetric, {partial: true}),
  //       },
  //     },
  //   })
  //   newMetric: Partial<NewMetric>,
  //   @param.query.object('where', getWhereSchemaFor(NewMetric)) where?: Where<NewMetric>,
  // ): Promise<Count> {
  //   return this.newTopicRepository.newMetrics(id).patch(newMetric, where);
  // }

  // @del('/new-topics/{id}/new-metrics', {
  //   responses: {
  //     '200': {
  //       description: 'NewTopic.NewMetric DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewMetric)) where?: Where<NewMetric>,
  // ): Promise<Count> {
  //   return this.newTopicRepository.newMetrics(id).delete(where);
  // }

}
