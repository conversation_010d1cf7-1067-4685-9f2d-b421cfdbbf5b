import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  FormCollection,
  AssignDcfUser,
} from '../models';
import {FormCollectionRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class FormCollectionAssignDcfUserController {
  constructor(
    @repository(FormCollectionRepository) protected formCollectionRepository: FormCollectionRepository,
  ) { }

  @get('/form-collections/{id}/assign-dcf-users', {
    responses: {
      '200': {
        description: 'Array of FormCollection has many AssignDcfUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDcfUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDcfUser>,
  ): Promise<AssignDcfUser[]> {
    return this.formCollectionRepository.assignDcfUsers(id).find(filter);
  }

  @post('/form-collections/{id}/assign-dcf-users', {
    responses: {
      '200': {
        description: 'FormCollection model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDcfUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof FormCollection.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfUser, {
            title: 'NewAssignDcfUserInFormCollection',
            exclude: ['id'],
            optional: ['formCollectionId']
          }),
        },
      },
    }) assignDcfUser: Omit<AssignDcfUser, 'id'>,
  ): Promise<AssignDcfUser> {
    return this.formCollectionRepository.assignDcfUsers(id).create(assignDcfUser);
  }

  // @patch('/form-collections/{id}/assign-dcf-users', {
  //   responses: {
  //     '200': {
  //       description: 'FormCollection.AssignDcfUser PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfUser, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfUser: Partial<AssignDcfUser>,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfUser)) where?: Where<AssignDcfUser>,
  // ): Promise<Count> {
  //   return this.formCollectionRepository.assignDcfUsers(id).patch(assignDcfUser, where);
  // }

  // @del('/form-collections/{id}/assign-dcf-users', {
  //   responses: {
  //     '200': {
  //       description: 'FormCollection.AssignDcfUser DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignDcfUser)) where?: Where<AssignDcfUser>,
  // ): Promise<Count> {
  //   return this.formCollectionRepository.assignDcfUsers(id).delete(where);
  // }
  
}
