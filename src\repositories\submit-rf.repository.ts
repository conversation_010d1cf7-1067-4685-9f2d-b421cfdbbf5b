import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SubmitRf, SubmitRfRelations} from '../models';

export class SubmitRfRepository extends DefaultCrudRepository<
  SubmitRf,
  typeof SubmitRf.prototype.id,
  SubmitRfRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SubmitRf, dataSource);
  }
}
