import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewIndicator} from '../models';
import {NewIndicatorRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewIndicatorController {
  constructor(
    @repository(NewIndicatorRepository)
    public newIndicatorRepository : NewIndicatorRepository,
  ) {}

  @post('/new-indicators')
  @response(200, {
    description: 'NewIndicator model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewIndicator)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewIndicator, {
            title: 'NewNewIndicator',
            exclude: ['id'],
          }),
        },
      },
    })
    newIndicator: Omit<NewIndicator, 'id'>,
  ): Promise<NewIndicator> {
    return this.newIndicatorRepository.create(newIndicator);
  }

  @get('/new-indicators/count')
  @response(200, {
    description: 'NewIndicator model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewIndicator) where?: Where<NewIndicator>,
  ): Promise<Count> {
    return this.newIndicatorRepository.count(where);
  }

  @get('/new-indicators')
  @response(200, {
    description: 'Array of NewIndicator model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewIndicator, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewIndicator) filter?: Filter<NewIndicator>,
  ): Promise<NewIndicator[]> {
    return this.newIndicatorRepository.find(filter);
  }

  @get('/new-indicators/{id}')
  @response(200, {
    description: 'NewIndicator model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewIndicator, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewIndicator, {exclude: 'where'}) filter?: FilterExcludingWhere<NewIndicator>
  ): Promise<NewIndicator> {
    return this.newIndicatorRepository.findById(id, filter);
  }

  @patch('/new-indicators/{id}')
  @response(204, {
    description: 'NewIndicator PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewIndicator, {partial: true}),
        },
      },
    })
    newIndicator: NewIndicator,
  ): Promise<void> {
    await this.newIndicatorRepository.updateById(id, newIndicator);
  }

  @put('/new-indicators/{id}')
  @response(204, {
    description: 'NewIndicator PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newIndicator: NewIndicator,
  ): Promise<void> {
    await this.newIndicatorRepository.replaceById(id, newIndicator);
  }

  @del('/new-indicators/{id}')
  @response(204, {
    description: 'NewIndicator DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newIndicatorRepository.deleteById(id);
  }
}
