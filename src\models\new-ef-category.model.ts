import {Entity, hasMany, model, property} from '@loopback/repository';
import {NewEfSubcategory1} from './new-ef-subcategory1.model';

@model()
export class NewEfCategory extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'string',
  })
  extra?: string;

  @hasMany(() => NewEfSubcategory1)
  newEfSubcategory1s: NewEfSubcategory1[];

  @property({
    type: 'number',
  })
  newEfStdId?: number;

  @property({
    type: 'number',
  })
  ghgSubCategoryId?: number;

  constructor(data?: Partial<NewEfCategory>) {
    super(data);
  }
}

export interface NewEfCategoryRelations {
  // describe navigational properties here
}

export type NewEfCategoryWithRelations = NewEfCategory & NewEfCategoryRelations;
