import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Survey,

} from '../models';
import {SurveyRepository} from '../repositories';

export class SurveySurveyController {
  constructor(
    @repository(SurveyRepository) protected surveyRepository: SurveyRepository,
  ) { }

  @get('/surveys/{id}/surveys', {
    responses: {
      '200': {
        description: 'Array of Survey has many Survey',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Survey)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Survey>,
  ): Promise<Survey[]> {
    return this.surveyRepository.surveys(id).find(filter);
  }

  @post('/surveys/{id}/surveys', {
    responses: {
      '200': {
        description: 'Survey model instance',
        content: {'application/json': {schema: getModelSchemaRef(Survey)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Survey.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Survey, {
            title: 'NewSurveyInSurvey',
            exclude: ['id'],
            optional: ['surveyId']
          }),
        },
      },
    }) survey: Omit<Survey, 'id'>,
  ): Promise<Survey> {
    return this.surveyRepository.surveys(id).create(survey);
  }

  @patch('/surveys/{id}/surveys', {
    responses: {
      '200': {
        description: 'Survey.Survey PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Survey, {partial: true}),
        },
      },
    })
    survey: Partial<Survey>,
    @param.query.object('where', getWhereSchemaFor(Survey)) where?: Where<Survey>,
  ): Promise<Count> {
    return this.surveyRepository.surveys(id).patch(survey, where);
  }

  @del('/surveys/{id}/surveys', {
    responses: {
      '200': {
        description: 'Survey.Survey DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Survey)) where?: Where<Survey>,
  ): Promise<Count> {
    return this.surveyRepository.surveys(id).delete(where);
  }
}
