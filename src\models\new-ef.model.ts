import {Entity, model, property, hasMany} from '@loopback/repository';
import {NewEfItem} from './new-ef-item.model';

@model()
export class NewEf extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  category?: number;

  @property({
    type: 'string',
  })
  source?: string;

 

  @property({
    type: 'string',
  })
  year?: string;



  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'string',
  })
  version?: string;

  @property({
    type: 'number',
  })
  status?: number;

  @property({
    type: 'string',
  })
  last_update?: string;

  @property({
    type: 'string',
  })
  link?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'any',
  })
  extra1?: any;

  @property({
    type: 'array',
    itemType: 'any',
  })
  extra2?: any[];

  @hasMany(() => NewEfItem)
  newEfItems: NewEfItem[];

  @property({
    type: 'number',
  })
  newEfDateId?: number;

  constructor(data?: Partial<NewEf>) {
    super(data);
  }
}

export interface NewEfRelations {
  // describe navigational properties here
}

export type NewEfWithRelations = NewEf & NewEfRelations;
