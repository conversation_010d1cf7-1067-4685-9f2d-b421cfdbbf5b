import {Entity, model, property} from '@loopback/repository';

@model()
export class DdCategoryOne extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;
  @property({
    type: 'array',
    itemType: 'number'
  })
  client_ids?: number[];

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'number',
  })
  dropDownCategoryId?: number;

  constructor(data?: Partial<DdCategoryOne>) {
    super(data);
  }
}

export interface DdCategoryOneRelations {
  // describe navigational properties here
}

export type DdCategoryOneWithRelations = DdCategoryOne & DdCategoryOneRelations;
