import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  DeleteUserLog,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileDeleteUserLogController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/delete-user-logs', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many DeleteUserLog',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DeleteUserLog)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<DeleteUserLog>,
  ): Promise<DeleteUserLog[]> {
    return this.userProfileRepository.deleteUserLogs(id).find(filter);
  }

  @post('/user-profiles/{id}/delete-user-logs', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(DeleteUserLog)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DeleteUserLog, {
            title: 'NewDeleteUserLogInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) deleteUserLog: Omit<DeleteUserLog, 'id'>,
  ): Promise<DeleteUserLog> {
    return this.userProfileRepository.deleteUserLogs(id).create(deleteUserLog);
  }

  @patch('/user-profiles/{id}/delete-user-logs', {
    responses: {
      '200': {
        description: 'UserProfile.DeleteUserLog PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DeleteUserLog, {partial: true}),
        },
      },
    })
    deleteUserLog: Partial<DeleteUserLog>,
    @param.query.object('where', getWhereSchemaFor(DeleteUserLog)) where?: Where<DeleteUserLog>,
  ): Promise<Count> {
    return this.userProfileRepository.deleteUserLogs(id).patch(deleteUserLog, where);
  }

  @del('/user-profiles/{id}/delete-user-logs', {
    responses: {
      '200': {
        description: 'UserProfile.DeleteUserLog DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(DeleteUserLog)) where?: Where<DeleteUserLog>,
  ): Promise<Count> {
    return this.userProfileRepository.deleteUserLogs(id).delete(where);
  }
}
