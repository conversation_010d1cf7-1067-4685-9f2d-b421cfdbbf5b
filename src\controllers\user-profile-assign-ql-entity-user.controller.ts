import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {
  AssignQlEntityUser,
  UserProfile,
} from '../models';
import {AssignQlEntityRepository, LocationOneRepository, LocationThreeRepository, LocationTwoRepository, QSectionRepository, QTopicRepository, UserProfileRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class UserProfileAssignQlEntityUserController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(QTopicRepository) protected qTopicRepository: QTopicRepository,
    @repository(QSectionRepository) protected qSectionRepository: QSectionRepository,
    @repository(LocationOneRepository) protected locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository) protected locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository) protected locationThreeRepository: LocationThreeRepository,
    @repository(AssignQlEntityRepository) protected assignQlEntityRepository: AssignQlEntityRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @inject('services.SqsService')
    public sqsService: SqsService
  ) { }

  @get('/user-profiles/{id}/assign-ql-entity-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignQlEntityUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignQlEntityUser)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignQlEntityUser>,
  ): Promise<AssignQlEntityUser[]> {
    return this.userProfileRepository.assignQlEntityUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-ql-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignQlEntityUser)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntityUser, {
            title: 'NewAssignQlEntityUserInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignQlEntityUser: Omit<AssignQlEntityUser, 'id'>,
  ): Promise<AssignQlEntityUser> {
    console.log(assignQlEntityUser)
    const auditorData = await this.userProfileController.filteredUP({where: {id: {inq: assignQlEntityUser?.reporter_ids || []}}})
    const entityAssignment = await this.assignQlEntityRepository.findById(assignQlEntityUser.entityAssId)
    const consolidatorData = await this.userProfileController.filteredUP({where: {id: {inq: entityAssignment?.consolidator_ids || []}}})

    const topic = (await this.qTopicRepository.findById(assignQlEntityUser.qTopicId)).name
    const section = (await this.qSectionRepository.findById(assignQlEntityUser.qSectionId)).name
    const adminObj = await this.userProfileRepository.findById(id);
    let entity: any = '';
    if (assignQlEntityUser.level === 0) {
      entity = 'Corporate'
    } else if (assignQlEntityUser.level === 1) {
      entity = (await this.locationOneRepository.findById(assignQlEntityUser.locationId))?.name || 'NA'
    } else if (assignQlEntityUser.level === 2) {
      entity = (await this.locationTwoRepository.findById(assignQlEntityUser.locationId))?.name || 'NA'
    } else if (assignQlEntityUser.level === 3) {
      entity = (await this.locationThreeRepository.findById(assignQlEntityUser.locationId))?.name || 'NA'
    }
    for (const user of auditorData) {
      const body = `
      <p><strong>Dear ${user?.information?.empname || 'User'},</strong></p>

      <p style="margin: 5px 0px;">
        You have been assigned as the <strong>Data Reporter</strong> for the qualitative section
        <strong>"${section}"</strong> under the topic
        <strong>"${topic}"</strong> for the reporting entity
        <strong>"${entity}"</strong> on the <strong>Navigos ESG</strong> platform.
      </p>

      <p style="margin: 5px 0px;"><strong>Please find the assignment details below:</strong></p>
      <ul>
        <li><strong>Assigned Consolidator(s):</strong> ${consolidatorData.map((x: any) => x.email).filter((x: any) => x)}</li>
        <li><strong>Submission Due Date:</strong> ${DateTime.fromISO(assignQlEntityUser.due_date ? assignQlEntityUser.due_date : '', {zone: 'utc'}).plus({day: 1}).toFormat('dd-LLL-yyyy')}</li>
      </ul>

    ${adminObj?.userPortalUrl ? `<p style="margin: 5px 0px;">
        To access the form and begin your response, please log in to the platform using the following link:
        <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform</a>
      </p>` : ''}

      <p style="margin: 5px 0px;">
        If you have any questions or require assistance, you may raise a ticket through the platform or
        contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.
      </p>

      <p style="margin: 5px 0px;">Thank you for your cooperation.</p>

      <p style="margin: 5px 0px; color: gray; font-size: 0.9em;">
        This is an automated notification. Please do not reply to this message.
      </p>
      `;
      const subject = 'Assignment of Sustainability Qualitative Response Form – Navigos'


      // try {
      //   const info = await this.sqsService.sendEmail(user.email, subject, body, []).then((info) => {
      //     console.log('mail sent')

      //   }).catch((err) => {
      //     console.log('error in sending')

      //   })
      // } catch (error) {
      //   console.error(`Error sending email to`, error);
      //   throw new Error('Failed to send email');
      // }
    }

    return this.userProfileRepository.assignQlEntityUsers(id).create(assignQlEntityUser);
  }

  @patch('/user-profiles/{id}/assign-ql-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignQlEntityUser PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignQlEntityUser, {partial: true}),
        },
      },
    })
    assignQlEntityUser: Partial<AssignQlEntityUser>,
    @param.query.object('where', getWhereSchemaFor(AssignQlEntityUser)) where?: Where<AssignQlEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignQlEntityUsers(id).patch(assignQlEntityUser, where);
  }

  @del('/user-profiles/{id}/assign-ql-entity-users', {
    responses: {
      '200': {
        description: 'UserProfile.AssignQlEntityUser DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignQlEntityUser)) where?: Where<AssignQlEntityUser>,
  ): Promise<Count> {
    return this.userProfileRepository.assignQlEntityUsers(id).delete(where);
  }
}
