import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SubSurvey,
  Survey,
} from '../models';
import {SubSurveyRepository} from '../repositories';

export class SubSurveySurveyController {
  constructor(
    @repository(SubSurveyRepository)
    public subSurveyRepository: SubSurveyRepository,
  ) { }

  @get('/sub-surveys/{id}/survey', {
    responses: {
      '200': {
        description: 'Survey belonging to SubSurvey',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Survey)},
          },
        },
      },
    },
  })
  async getSurvey(
    @param.path.number('id') id: typeof SubSurvey.prototype.id,
  ): Promise<Survey> {
    return this.subSurveyRepository.survey(id);
  }
}
