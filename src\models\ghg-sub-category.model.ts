import {Entity, hasMany, model, property} from '@loopback/repository';
import {NewEfCategory} from './new-ef-category.model';

@model()
export class GhgSubCategory extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;
  @property({
    type: 'string',
  })
  created_on?: string;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'number',
  })
  ghgCategoryId?: number;

  @hasMany(() => NewEfCategory)
  newEfCategories: NewEfCategory[];

  constructor(data?: Partial<GhgSubCategory>) {
    super(data);
  }
}

export interface GhgSubCategoryRelations {
  // describe navigational properties here
}

export type GhgSubCategoryWithRelations = GhgSubCategory & GhgSubCategoryRelations;
