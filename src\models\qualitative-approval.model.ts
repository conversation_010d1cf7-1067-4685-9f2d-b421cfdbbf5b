import {Entity, model, property} from '@loopback/repository';

@model()
export class QualitativeApproval extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  reporting_year?: number;

  @property({
    type: 'string',
  })
  approver_modified_on?: string;
  @property({
    type: 'number',
  })
  approver_modified_by?: number;

  @property({
    type: 'number',
  })
  categoryId?: number;
  @property({
    type: 'number',
  })
  topicId?: number;

  @property({
    type: 'number',
  })
  dfId?: number;
  @property({
    type: 'number',
  })
  indicatorId?: number;
  @property({
    type: 'number',
  })
  user_type?: number;

  @property({
    type: 'number',
  })
  form_type?: number;
  @property({
    type: 'number',
  })
  type?: number;
  @property({
    type: 'array',
    itemType: 'any',
  })
  response?: any[];
  @property({
    type: 'string',
  })
  last_modified_on?: string;

  @property({
    type: 'number',
  })
  last_modified_by?: number;
  @property({
    type: 'number',
  })
  edit?: number;
  @property({
    type: 'number',
  })
  reject?: number;
  @property({
    type: 'any',
  })
  return_remarks?: any;
  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<QualitativeApproval>) {
    super(data);
  }
}

export interface QualitativeApprovalRelations {
  // describe navigational properties here
}

export type QualitativeApprovalWithRelations = QualitativeApproval & QualitativeApprovalRelations;
