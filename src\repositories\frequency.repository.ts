import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {Frequency, FrequencyRelations} from '../models';

export class FrequencyRepository extends DefaultCrudRepository<
  Frequency,
  typeof Frequency.prototype.id,
  FrequencyRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(Frequency, dataSource);
  }
}
