import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {StdName} from '../models';
import {StdNameRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdNameController {
  constructor(
    @repository(StdNameRepository)
    public stdNameRepository : StdNameRepository,
  ) {}

  @post('/std-names')
  @response(200, {
    description: 'StdName model instance',
    content: {'application/json': {schema: getModelSchemaRef(StdName)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdName, {
            title: 'NewStdName',
            exclude: ['id'],
          }),
        },
      },
    })
    stdName: Omit<StdName, 'id'>,
  ): Promise<StdName> {
    return this.stdNameRepository.create(stdName);
  }

  @get('/std-names/count')
  @response(200, {
    description: 'StdName model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(StdName) where?: Where<StdName>,
  ): Promise<Count> {
    return this.stdNameRepository.count(where);
  }

  @get('/std-names')
  @response(200, {
    description: 'Array of StdName model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(StdName, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(StdName) filter?: Filter<StdName>,
  ): Promise<StdName[]> {
    return this.stdNameRepository.find(filter);
  }

  // @patch('/std-names')
  // @response(200, {
  //   description: 'StdName PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StdName, {partial: true}),
  //       },
  //     },
  //   })
  //   stdName: StdName,
  //   @param.where(StdName) where?: Where<StdName>,
  // ): Promise<Count> {
  //   return this.stdNameRepository.updateAll(stdName, where);
  // }

  @get('/std-names/{id}')
  @response(200, {
    description: 'StdName model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(StdName, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(StdName, {exclude: 'where'}) filter?: FilterExcludingWhere<StdName>
  ): Promise<StdName> {
    return this.stdNameRepository.findById(id, filter);
  }

  @patch('/std-names/{id}')
  @response(204, {
    description: 'StdName PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdName, {partial: true}),
        },
      },
    })
    stdName: StdName,
  ): Promise<void> {
    await this.stdNameRepository.updateById(id, stdName);
  }

  @put('/std-names/{id}')
  @response(204, {
    description: 'StdName PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() stdName: StdName,
  ): Promise<void> {
    await this.stdNameRepository.replaceById(id, stdName);
  }

  @del('/std-names/{id}')
  @response(204, {
    description: 'StdName DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.stdNameRepository.deleteById(id);
  }
}
