import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Ticketing} from '../models';
import {TicketingRepository} from '../repositories';

export class TicketingController {
  constructor(
    @repository(TicketingRepository)
    public ticketingRepository : TicketingRepository,
  ) {}

  @post('/ticketings')
  @response(200, {
    description: 'Ticketing model instance',
    content: {'application/json': {schema: getModelSchemaRef(Ticketing)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ticketing, {
            title: 'NewTicketing',
            exclude: ['id'],
          }),
        },
      },
    })
    ticketing: Omit<Ticketing, 'id'>,
  ): Promise<Ticketing> {
    return this.ticketingRepository.create(ticketing);
  }

  @get('/ticketings/count')
  @response(200, {
    description: 'Ticketing model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Ticketing) where?: Where<Ticketing>,
  ): Promise<Count> {
    return this.ticketingRepository.count(where);
  }

  @get('/ticketings')
  @response(200, {
    description: 'Array of Ticketing model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Ticketing, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Ticketing) filter?: Filter<Ticketing>,
  ): Promise<Ticketing[]> {
    return this.ticketingRepository.find(filter);
  }

  @patch('/ticketings')
  @response(200, {
    description: 'Ticketing PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ticketing, {partial: true}),
        },
      },
    })
    ticketing: Ticketing,
    @param.where(Ticketing) where?: Where<Ticketing>,
  ): Promise<Count> {
    return this.ticketingRepository.updateAll(ticketing, where);
  }

  @get('/ticketings/{id}')
  @response(200, {
    description: 'Ticketing model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Ticketing, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(Ticketing, {exclude: 'where'}) filter?: FilterExcludingWhere<Ticketing>
  ): Promise<Ticketing> {
    return this.ticketingRepository.findById(id, filter);
  }

  @patch('/ticketings/{id}')
  @response(204, {
    description: 'Ticketing PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Ticketing, {partial: true}),
        },
      },
    })
    ticketing: Ticketing,
  ): Promise<void> {
    await this.ticketingRepository.updateById(id, ticketing);
  }

  @put('/ticketings/{id}')
  @response(204, {
    description: 'Ticketing PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() ticketing: Ticketing,
  ): Promise<void> {
    await this.ticketingRepository.replaceById(id, ticketing);
  }

  @del('/ticketings/{id}')
  @response(204, {
    description: 'Ticketing DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.ticketingRepository.deleteById(id);
  }
}
