import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GhgCategory} from '../models';
import {GhgCategoryRepository} from '../repositories';

export class GhgCategoryController {
  constructor(
    @repository(GhgCategoryRepository)
    public ghgCategoryRepository : GhgCategoryRepository,
  ) {}

  @post('/ghg-categories')
  @response(200, {
    description: 'GhgCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(GhgCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgCategory, {
            title: 'NewGhgCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    ghgCategory: Omit<GhgCategory, 'id'>,
  ): Promise<GhgCategory> {
    return this.ghgCategoryRepository.create(ghgCategory);
  }

  @get('/ghg-categories/count')
  @response(200, {
    description: 'GhgCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GhgCategory) where?: Where<GhgCategory>,
  ): Promise<Count> {
    return this.ghgCategoryRepository.count(where);
  }

  @get('/ghg-categories')
  @response(200, {
    description: 'Array of GhgCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GhgCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GhgCategory) filter?: Filter<GhgCategory>,
  ): Promise<GhgCategory[]> {
    return this.ghgCategoryRepository.find(filter);
  }

  @patch('/ghg-categories')
  @response(200, {
    description: 'GhgCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgCategory, {partial: true}),
        },
      },
    })
    ghgCategory: GhgCategory,
    @param.where(GhgCategory) where?: Where<GhgCategory>,
  ): Promise<Count> {
    return this.ghgCategoryRepository.updateAll(ghgCategory, where);
  }

  @get('/ghg-categories/{id}')
  @response(200, {
    description: 'GhgCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GhgCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(GhgCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<GhgCategory>
  ): Promise<GhgCategory> {
    return this.ghgCategoryRepository.findById(id, filter);
  }

  @patch('/ghg-categories/{id}')
  @response(204, {
    description: 'GhgCategory PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgCategory, {partial: true}),
        },
      },
    })
    ghgCategory: GhgCategory,
  ): Promise<void> {
    await this.ghgCategoryRepository.updateById(id, ghgCategory);
  }

  @put('/ghg-categories/{id}')
  @response(204, {
    description: 'GhgCategory PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() ghgCategory: GhgCategory,
  ): Promise<void> {
    await this.ghgCategoryRepository.replaceById(id, ghgCategory);
  }

  @del('/ghg-categories/{id}')
  @response(204, {
    description: 'GhgCategory DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.ghgCategoryRepository.deleteById(id);
  }
}
