import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierSectionSubmission,
  DealerResponseForm,
} from '../models';
import {SupplierSectionSubmissionRepository} from '../repositories';

export class SupplierSectionSubmissionDealerResponseFormController {
  constructor(
    @repository(SupplierSectionSubmissionRepository)
    public supplierSectionSubmissionRepository: SupplierSectionSubmissionRepository,
  ) { }

  @get('/supplier-section-submissions/{id}/dealer-response-form', {
    responses: {
      '200': {
        description: 'DealerResponseForm belonging to SupplierSectionSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DealerResponseForm)},
          },
        },
      },
    },
  })
  async getDealerResponseForm(
    @param.path.string('id') id: typeof SupplierSectionSubmission.prototype.id,
  ): Promise<DealerResponseForm> {
    return this.supplierSectionSubmissionRepository.dealerResponseForm(id);
  }
}
