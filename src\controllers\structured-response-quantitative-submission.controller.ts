import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  StructuredResponse,
  QuantitativeSubmission,
} from '../models';
import {StructuredResponseRepository} from '../repositories';

export class StructuredResponseQuantitativeSubmissionController {
  constructor(
    @repository(StructuredResponseRepository)
    public structuredResponseRepository: StructuredResponseRepository,
  ) { }

  @get('/structured-responses/{id}/quantitative-submission', {
    responses: {
      '200': {
        description: 'QuantitativeSubmission belonging to StructuredResponse',
        content: {
          'application/json': {
            schema: getModelSchemaRef(QuantitativeSubmission),
          },
        },
      },
    },
  })
  async getQuantitativeSubmission(
    @param.path.number('id') id: typeof StructuredResponse.prototype.id,
  ): Promise<QuantitativeSubmission> {
    return this.structuredResponseRepository.submitDcf(id);
  }
}
