import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  IndicatorSection,
  UserProfile
} from '../models';
import {NewMetricRepository, UserProfileRepository} from '../repositories';

export class UserProfileIndicatorSectionController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(NewMetricRepository) protected newMetricRepository: NewMetricRepository,

  ) { }

  @get('/user-profiles/{id}/indicator-sections', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many IndicatorSection',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(IndicatorSection)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<IndicatorSection>,
  ): Promise<IndicatorSection[]> {
    return this.userProfileRepository.indicatorSections(id).find(filter);
  }

  @post('/user-profiles/{id}/indicator-sections', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(IndicatorSection)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorSection, {
            title: 'NewIndicatorSectionInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) indicatorSection: Omit<IndicatorSection, 'id'>,
  ): Promise<IndicatorSection> {
    return this.userProfileRepository.indicatorSections(id).create(indicatorSection);
  }

  @patch('/user-profiles/{id}/indicator-sections', {
    responses: {
      '200': {
        description: 'UserProfile.IndicatorSection PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(IndicatorSection, {partial: true}),
        },
      },
    })
    indicatorSection: Partial<IndicatorSection>,
    @param.query.object('where', getWhereSchemaFor(IndicatorSection)) where?: Where<IndicatorSection>,
  ): Promise<Count> {
    return this.userProfileRepository.indicatorSections(id).patch(indicatorSection, where);
  }

  @del('/user-profiles/{id}/indicator-sections', {
    responses: {
      '200': {
        description: 'UserProfile.IndicatorSection DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(IndicatorSection)) where?: Where<IndicatorSection>,
  ): Promise<Count> {
    return this.userProfileRepository.indicatorSections(id).delete(where);
  }
  @post('/user-profiles/{id}/get-indicator-by-section-custom', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many DealerAssessmentAssignment',
      },
    },
  })
  async getIndicatorListBySection(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            sectionId: {
              type: 'number'
            }
          },
          required: ['sectionId'],
        },
      },
    },
  })
  requestBody: {sectionId: number},
    @param.path.number('id') id: number,
  ): Promise<any> {
    const {sectionId} = requestBody
    try {
      const dcfAss = await this.userProfileRepository.assignDcfClients(id).find()
      const metrics = await this.newMetricRepository.find()

      if (dcfAss.length === 1 && dcfAss[0].metric_ids && dcfAss[0].metric_ids.length) {
        const found = await this.userProfileRepository.indicatorSections(id).find({where: {id: sectionId}})
        console.log(found)
        const assignmentObj = {...dcfAss[0]}
        const allSection = await this.userProfileRepository.indicatorSections(id).find()
        if (found.length === 1 && (!found[0].metric_ids || found[0].metric_ids.length === 0)) {
          const assignedMetricIds = allSection
            .filter((section) => section.id !== sectionId)  // Exclude current section if editing
            .flatMap((section) => section?.metric_ids || []);

          const availableMetricIds = dcfAss[0].metric_ids.filter(
            (metricId) => !assignedMetricIds.includes(metricId),
          );
          return {status: true, assignment: {...found[0], metric_ids: []}, available: availableMetricIds.map(x => metrics.find(i => i.id === x)).filter(x => x), message: 'Entries Found with 0 Indicator(s)'}

        } else if (found.length === 1 && found[0].metric_ids && found[0].metric_ids.length !== 0) {
          const assignedMetricIds = allSection
            .filter((section) => section.id !== sectionId)  // Exclude current section if editing
            .flatMap((section) => section?.metric_ids || []);
          const availableMetricIds = dcfAss[0].metric_ids.filter(
            (metricId) => !assignedMetricIds.includes(metricId),
          );
          return {status: true, assignment: {...found[0], metric_ids: found[0].metric_ids.filter(x => dcfAss[0].metric_ids?.includes(x) && metrics.map(y => y.id).includes(x))}, available: availableMetricIds.map(x => metrics.find(i => i.id === x)).filter(x => x), message: `Entries Found with ${found[0].metric_ids.filter(x => dcfAss[0].metric_ids?.includes(x)).length} Indicator(s)`}

        } else {
          return {status: false, message: 'Multiple Entries Found in Section'}

        }
      } else {
        return {status: false, message: 'Multiple Entries Found in Indicator Assignment'}
      }



    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }

}
