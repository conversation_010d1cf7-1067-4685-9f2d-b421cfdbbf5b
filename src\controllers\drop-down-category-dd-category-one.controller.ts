import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  DdCategoryOne,
  DropDownCategory,
} from '../models';
import {DropDownCategoryRepository} from '../repositories';

export class DropDownCategoryDdCategoryOneController {
  constructor(
    @repository(DropDownCategoryRepository) protected initiativeCategoryRepository: DropDownCategoryRepository,
  ) { }

  @get('/drop-down-categories/{id}/dd-category-ones', {
    responses: {
      '200': {
        description: 'Array of DropDownCategory has many DdCategoryOne',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DdCategoryOne)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<DdCategoryOne>,
  ): Promise<DdCategoryOne[]> {
    return this.initiativeCategoryRepository.ddCategoryOnes(id).find(filter);
  }

  @post('/drop-down-categories/{id}/dd-category-ones', {
    responses: {
      '200': {
        description: 'DropDownCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(DdCategoryOne)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof DropDownCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DdCategoryOne, {
            title: 'NewDdCategoryOneInDropDownCategory',
            exclude: ['id'],
            optional: ['dropDownCategoryId']
          }),
        },
      },
    }) initCategoryUnit: Omit<DdCategoryOne, 'id'>,
  ): Promise<DdCategoryOne> {
    return this.initiativeCategoryRepository.ddCategoryOnes(id).create(initCategoryUnit);
  }

  @patch('/drop-down-categories/{id}/dd-category-ones', {
    responses: {
      '200': {
        description: 'DropDownCategory.DdCategoryOne PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DdCategoryOne, {partial: true}),
        },
      },
    })
    initCategoryUnit: Partial<DdCategoryOne>,
    @param.query.object('where', getWhereSchemaFor(DdCategoryOne)) where?: Where<DdCategoryOne>,
  ): Promise<Count> {
    return this.initiativeCategoryRepository.ddCategoryOnes(id).patch(initCategoryUnit, where);
  }

  @del('/drop-down-categories/{id}/dd-category-ones', {
    responses: {
      '200': {
        description: 'DropDownCategory.DdCategoryOne DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(DdCategoryOne)) where?: Where<DdCategoryOne>,
  ): Promise<Count> {
    return this.initiativeCategoryRepository.ddCategoryOnes(id).delete(where);
  }
}
