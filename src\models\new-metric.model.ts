import {Entity, hasMany, model, property} from '@loopback/repository';
import {NewDataPoint} from './new-data-point.model';


@model({settings: {strict: false}})
export class NewMetric extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'any',
  })
  extra?: any;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  newTopicId?: number;

  @property({
    type: 'number',
  })
  order?: number;
  @property({
    type: 'number',
  })
  cloneMetricId?: number;

  @property({
    type: 'any',
  })
  tag?: any;

  @property({
    type: 'number',
  })

  cloneTopicId?: number;



  @hasMany(() => NewDataPoint)
  newDataPoints: NewDataPoint[];


  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<NewMetric>) {
    super(data);
  }
}

export interface NewMetricRelations {
  // describe navigational properties here
}

export type NewMetricWithRelations = NewMetric & NewMetricRelations;
