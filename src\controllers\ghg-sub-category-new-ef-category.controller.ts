import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  GhgSubCategory,
  NewEfCategory,
} from '../models';
import {GhgSubCategoryRepository} from '../repositories';

export class GhgSubCategoryNewEfCategoryController {
  constructor(
    @repository(GhgSubCategoryRepository) protected ghgSubCategoryRepository: GhgSubCategoryRepository,
  ) { }

  @get('/ghg-sub-categories/{id}/new-ef-categories', {
    responses: {
      '200': {
        description: 'Array of GhgSubCategory has many NewEfCategory',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfCategory)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEfCategory>,
  ): Promise<NewEfCategory[]> {
    return this.ghgSubCategoryRepository.newEfCategories(id).find(filter);
  }

  @post('/ghg-sub-categories/{id}/new-ef-categories', {
    responses: {
      '200': {
        description: 'GhgSubCategory model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEfCategory)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof GhgSubCategory.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfCategory, {
            title: 'NewNewEfCategoryInGhgSubCategory',
            exclude: ['id'],
            optional: ['ghgSubCategoryId']
          }),
        },
      },
    }) newEfCategory: Omit<NewEfCategory, 'id'>,
  ): Promise<NewEfCategory> {
    return this.ghgSubCategoryRepository.newEfCategories(id).create(newEfCategory);
  }

  @patch('/ghg-sub-categories/{id}/new-ef-categories', {
    responses: {
      '200': {
        description: 'GhgSubCategory.NewEfCategory PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfCategory, {partial: true}),
        },
      },
    })
    newEfCategory: Partial<NewEfCategory>,
    @param.query.object('where', getWhereSchemaFor(NewEfCategory)) where?: Where<NewEfCategory>,
  ): Promise<Count> {
    return this.ghgSubCategoryRepository.newEfCategories(id).patch(newEfCategory, where);
  }

  @del('/ghg-sub-categories/{id}/new-ef-categories', {
    responses: {
      '200': {
        description: 'GhgSubCategory.NewEfCategory DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(NewEfCategory)) where?: Where<NewEfCategory>,
  ): Promise<Count> {
    return this.ghgSubCategoryRepository.newEfCategories(id).delete(where);
  }
}
