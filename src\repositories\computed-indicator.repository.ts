import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ComputedIndicator, ComputedIndicatorRelations} from '../models';

export class ComputedIndicatorRepository extends DefaultCrudRepository<
  ComputedIndicator,
  typeof ComputedIndicator.prototype.id,
  ComputedIndicatorRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ComputedIndicator, dataSource);
  }
}
