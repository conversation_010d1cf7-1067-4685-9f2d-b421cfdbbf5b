import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {QualitativeApproval, QualitativeApprovalRelations} from '../models';

export class QualitativeApprovalRepository extends DefaultCrudRepository<
  QualitativeApproval,
  typeof QualitativeApproval.prototype.id,
  QualitativeApprovalRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(QualitativeApproval, dataSource);
  }
}
