import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDcfEntity, AssignDcfEntityRelations, FormCollection} from '../models';
import {FormCollectionRepository} from './form-collection.repository';

export class AssignDcfEntityRepository extends DefaultCrudRepository<
  AssignDcfEntity,
  typeof AssignDcfEntity.prototype.id,
  AssignDcfEntityRelations
> {

  public readonly dcf: BelongsToAccessor<FormCollection, typeof AssignDcfEntity.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('FormCollectionRepository') protected formCollectionRepositoryGetter: Getter<FormCollectionRepository>,
  ) {
    super(AssignDcfEntity, dataSource);
    this.dcf = this.createBelongsToAccessorFor('dcf', formCollectionRepositoryGetter,);
    this.registerInclusionResolver('dcf', this.dcf.inclusionResolver);
  }
}
