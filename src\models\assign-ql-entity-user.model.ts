import {belongsTo, Entity, model, property} from '@loopback/repository';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';
import {QCategory} from './q-category.model';
import {QSection} from './q-section.model';
import {QTopic} from './q-topic.model';

@model()
export class AssignQlEntityUser extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;


  @property({
    type: 'array',
    itemType: 'number',
  })
  reporter_ids?: number[];


  @property({
    type: 'string',
  })
  due_date?: string;

  @property({
    type: 'number',
  })
  level?: number;

  @property({
    type: 'number',
  })
  locationId?: number;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
    default: 'Not Started'
  })
  status?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'any',
  })
  comments?: any;

  @property({
    type: 'any',
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  response?: any;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  entityAssId?: number;

  @property({
    type: 'number',
  })
  tier0_id?: number;
  @property({
    type: 'number',
  })
  tier1_id?: number;
  @property({
    type: 'number',
  })
  tier2_id?: number;

  @property({
    type: 'number',
  })
  tier3_id?: number;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  rejected_on?: string | null;

  @property({
    type: 'any',

    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      type: 'array',
      items: {
        type: 'object',
      },
      nullable: true,
    }

  })
  return_remarks?: any[] | null;




  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  reject?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  rejected_by?: number | null;

  @belongsTo(() => QCategory)
  qCategoryId: number;

  @belongsTo(() => QTopic)
  qTopicId: number;

  @belongsTo(() => QSection)
  qSectionId: number;

  @belongsTo(() => ConsolidateFormCollection)
  srfId: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<AssignQlEntityUser>) {
    super(data);
  }
}

export interface AssignQlEntityUserRelations {
  // describe navigational properties here
}

export type AssignQlEntityUserWithRelations = AssignQlEntityUser & AssignQlEntityUserRelations;
