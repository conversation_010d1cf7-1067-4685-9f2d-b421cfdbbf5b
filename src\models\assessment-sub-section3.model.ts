import {belongsTo, Entity, model, property} from '@loopback/repository';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';

@model()
export class AssessmentSubSection3 extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
  })
  title?: string;
  @property({
    type: 'boolean',
    default: false,
  })
  isRoot?: boolean;
  @property({
    type: 'any',
  })
  order?: any;
  @property({
    type: 'string',
  })
  assessmentSubSection2Id?: string;

  @belongsTo(() => ConsolidateFormCollection, {name: 'srf'})
  formId: number;

  constructor(data?: Partial<AssessmentSubSection3>) {
    super(data);
  }
}

export interface AssessmentSubSection3Relations {
  // describe navigational properties here
}

export type AssessmentSubSection3WithRelations = AssessmentSubSection3 & AssessmentSubSection3Relations;
