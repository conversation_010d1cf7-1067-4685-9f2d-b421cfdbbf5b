import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  StructuredResponse,
  FormCollection,
} from '../models';
import {StructuredResponseRepository} from '../repositories';

export class StructuredResponseFormCollectionController {
  constructor(
    @repository(StructuredResponseRepository)
    public structuredResponseRepository: StructuredResponseRepository,
  ) { }

  @get('/structured-responses/{id}/form-collection', {
    responses: {
      '200': {
        description: 'FormCollection belonging to StructuredResponse',
        content: {
          'application/json': {
            schema: getModelSchemaRef(FormCollection),
          },
        },
      },
    },
  })
  async getFormCollection(
    @param.path.number('id') id: typeof StructuredResponse.prototype.id,
  ): Promise<FormCollection> {
    return this.structuredResponseRepository.dcf(id);
  }
}
