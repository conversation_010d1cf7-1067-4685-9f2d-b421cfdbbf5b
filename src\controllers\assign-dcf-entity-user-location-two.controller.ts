import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignDcfEntityUser,
  LocationTwo,
} from '../models';
import {AssignDcfEntityUserRepository} from '../repositories';

export class AssignDcfEntityUserLocationTwoController {
  constructor(
    @repository(AssignDcfEntityUserRepository)
    public assignDcfEntityUserRepository: AssignDcfEntityUserRepository,
  ) { }

  @get('/assign-dcf-entity-users/{id}/location-two', {
    responses: {
      '200': {
        description: 'LocationTwo belonging to AssignDcfEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationTwo),
          },
        },
      },
    },
  })
  async getLocationTwo(
    @param.path.number('id') id: typeof AssignDcfEntityUser.prototype.id,
  ): Promise<LocationTwo> {
    return this.assignDcfEntityUserRepository.ltwo(id);
  }
}
