import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignSrfEntity} from '../models';
import {AssignSrfEntityRepository} from '../repositories';

export class AssignSrfEntityController {
  constructor(
    @repository(AssignSrfEntityRepository)
    public assignSrfEntityRepository : AssignSrfEntityRepository,
  ) {}

  @post('/assign-srf-entities')
  @response(200, {
    description: 'AssignSrfEntity model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignSrfEntity)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntity, {
            title: 'NewAssignSrfEntity',
            exclude: ['id'],
          }),
        },
      },
    })
    assignSrfEntity: Omit<AssignSrfEntity, 'id'>,
  ): Promise<AssignSrfEntity> {
    return this.assignSrfEntityRepository.create(assignSrfEntity);
  }

  @get('/assign-srf-entities/count')
  @response(200, {
    description: 'AssignSrfEntity model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignSrfEntity) where?: Where<AssignSrfEntity>,
  ): Promise<Count> {
    return this.assignSrfEntityRepository.count(where);
  }

  @get('/assign-srf-entities')
  @response(200, {
    description: 'Array of AssignSrfEntity model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignSrfEntity, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignSrfEntity) filter?: Filter<AssignSrfEntity>,
  ): Promise<AssignSrfEntity[]> {
    return this.assignSrfEntityRepository.find(filter);
  }

  @patch('/assign-srf-entities')
  @response(200, {
    description: 'AssignSrfEntity PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntity, {partial: true}),
        },
      },
    })
    assignSrfEntity: AssignSrfEntity,
    @param.where(AssignSrfEntity) where?: Where<AssignSrfEntity>,
  ): Promise<Count> {
    return this.assignSrfEntityRepository.updateAll(assignSrfEntity, where);
  }

  @get('/assign-srf-entities/{id}')
  @response(200, {
    description: 'AssignSrfEntity model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignSrfEntity, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignSrfEntity, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignSrfEntity>
  ): Promise<AssignSrfEntity> {
    return this.assignSrfEntityRepository.findById(id, filter);
  }

  @patch('/assign-srf-entities/{id}')
  @response(204, {
    description: 'AssignSrfEntity PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignSrfEntity, {partial: true}),
        },
      },
    })
    assignSrfEntity: AssignSrfEntity,
  ): Promise<void> {
    await this.assignSrfEntityRepository.updateById(id, assignSrfEntity);
  }

  @put('/assign-srf-entities/{id}')
  @response(204, {
    description: 'AssignSrfEntity PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignSrfEntity: AssignSrfEntity,
  ): Promise<void> {
    await this.assignSrfEntityRepository.replaceById(id, assignSrfEntity);
  }

  @del('/assign-srf-entities/{id}')
  @response(204, {
    description: 'AssignSrfEntity DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignSrfEntityRepository.deleteById(id);
  }
}
