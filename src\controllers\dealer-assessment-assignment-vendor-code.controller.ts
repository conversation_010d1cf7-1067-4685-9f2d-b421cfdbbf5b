import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  DealerAssessmentAssignment,
  VendorCode,
} from '../models';
import {DealerAssessmentAssignmentRepository} from '../repositories';

export class DealerAssessmentAssignmentVendorCodeController {
  constructor(
    @repository(DealerAssessmentAssignmentRepository)
    public dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
  ) { }

  @get('/dealer-assessment-assignments/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to DealerAssessmentAssignment',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof DealerAssessmentAssignment.prototype.id,
  ): Promise<VendorCode> {
    return this.dealerAssessmentAssignmentRepository.vendor(id);
  }
}
