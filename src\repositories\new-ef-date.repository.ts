import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEfDate, NewEfDateRelations, NewEf} from '../models';
import {NewEfRepository} from './new-ef.repository';

export class NewEfDateRepository extends DefaultCrudRepository<
  NewEfDate,
  typeof NewEfDate.prototype.id,
  NewEfDateRelations
> {

  public readonly newEfs: HasManyRepositoryFactory<NewEf, typeof NewEfDate.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfRepository') protected newEfRepositoryGetter: Getter<NewEfRepository>,
  ) {
    super(NewEfDate, dataSource);
    this.newEfs = this.createHasManyRepositoryFactoryFor('newEfs', newEfRepositoryGetter,);
    this.registerInclusionResolver('newEfs', this.newEfs.inclusionResolver);
  }
}
