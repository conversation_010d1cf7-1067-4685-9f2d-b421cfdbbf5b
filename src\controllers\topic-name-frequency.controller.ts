import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  TopicName,
  Frequency,
} from '../models';
import {TopicNameRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class TopicNameFrequencyController {
  constructor(
    @repository(TopicNameRepository) protected topicNameRepository: TopicNameRepository,
  ) { }

  @get('/topic-names/{id}/frequencies', {
    responses: {
      '200': {
        description: 'Array of TopicName has many Frequency',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Frequency)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Frequency>,
  ): Promise<Frequency[]> {
    return this.topicNameRepository.frequencies(id).find(filter);
  }

  @post('/topic-names/{id}/frequencies', {
    responses: {
      '200': {
        description: 'TopicName model instance',
        content: {'application/json': {schema: getModelSchemaRef(Frequency)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof TopicName.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Frequency, {
            title: 'NewFrequencyInTopicName',
            exclude: ['id'],
            optional: ['topicNameId']
          }),
        },
      },
    }) frequency: Omit<Frequency, 'id'>,
  ): Promise<Frequency> {
    return this.topicNameRepository.frequencies(id).create(frequency);
  }

  // @patch('/topic-names/{id}/frequencies', {
  //   responses: {
  //     '200': {
  //       description: 'TopicName.Frequency PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(Frequency, {partial: true}),
  //       },
  //     },
  //   })
  //   frequency: Partial<Frequency>,
  //   @param.query.object('where', getWhereSchemaFor(Frequency)) where?: Where<Frequency>,
  // ): Promise<Count> {
  //   return this.topicNameRepository.frequencies(id).patch(frequency, where);
  // }

  // @del('/topic-names/{id}/frequencies', {
  //   responses: {
  //     '200': {
  //       description: 'TopicName.Frequency DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(Frequency)) where?: Where<Frequency>,
  // ): Promise<Count> {
  //   return this.topicNameRepository.frequencies(id).delete(where);
  // }
  
}
