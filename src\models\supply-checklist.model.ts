import {belongsTo, Entity, model, property} from '@loopback/repository';
import {UserProfile} from './user-profile.model';

@model()
export class SupplyChecklist extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @property({
    type: 'string',
  })
  updatedAt?: string;

  @property({
    type: 'boolean',
  })
  status?: boolean;

  @property({
    type: 'number',
  })
  supplyCategoryId?: number;

  @property({
    type: 'any',
  })
  values?: any;

  @property({
    type: 'number',
  })
  supplySectionId?: number;

  @belongsTo(() => UserProfile)
  createdById: number;

  constructor(data?: Partial<SupplyChecklist>) {
    super(data);
  }
}

export interface SupplyChecklistRelations {
  // describe navigational properties here
}

export type SupplyChecklistWithRelations = SupplyChecklist & SupplyChecklistRelations;
