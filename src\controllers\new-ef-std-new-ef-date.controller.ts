import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewEfStd,
  NewEfDate,
} from '../models';
import {NewEfStdRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfStdNewEfDateController {
  constructor(
    @repository(NewEfStdRepository) protected newEfStdRepository: NewEfStdRepository,
  ) { }

  @get('/new-ef-stds/{id}/new-ef-dates', {
    responses: {
      '200': {
        description: 'Array of NewEfStd has many NewEfDate',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfDate)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEfDate>,
  ): Promise<NewEfDate[]> {
    return this.newEfStdRepository.newEfDates(id).find(filter);
  }

  @post('/new-ef-stds/{id}/new-ef-dates', {
    responses: {
      '200': {
        description: 'NewEfStd model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEfDate)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEfStd.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfDate, {
            title: 'NewNewEfDateInNewEfStd',
            exclude: ['id'],
            optional: ['newEfStdId']
          }),
        },
      },
    }) newEfDate: Omit<NewEfDate, 'id'>,
  ): Promise<NewEfDate> {
    return this.newEfStdRepository.newEfDates(id).create(newEfDate);
  }

  // @patch('/new-ef-stds/{id}/new-ef-dates', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfStd.NewEfDate PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfDate, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfDate: Partial<NewEfDate>,
  //   @param.query.object('where', getWhereSchemaFor(NewEfDate)) where?: Where<NewEfDate>,
  // ): Promise<Count> {
  //   return this.newEfStdRepository.newEfDates(id).patch(newEfDate, where);
  // }

  // @del('/new-ef-stds/{id}/new-ef-dates', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfStd.NewEfDate DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewEfDate)) where?: Where<NewEfDate>,
  // ): Promise<Count> {
  //   return this.newEfStdRepository.newEfDates(id).delete(where);
  // }
  
}
