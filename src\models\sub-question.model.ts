import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class SubQuestion extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  options: object;

  @property({
    type: 'number',
  })
  questionId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<SubQuestion>) {
    super(data);
  }
}

export interface SubQuestionRelations {
  // describe navigational properties here
}

export type SubQuestionWithRelations = SubQuestion & SubQuestionRelations;
