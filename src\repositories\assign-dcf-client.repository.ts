import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDcfClient, AssignDcfClientRelations, AssignDcfUser} from '../models';
import {AssignDcfUserRepository} from './assign-dcf-user.repository';

export class AssignDcfClientRepository extends DefaultCrudRepository<
  AssignDcfClient,
  typeof AssignDcfClient.prototype.id,
  AssignDcfClientRelations
> {

  public readonly assignDcfUsers: HasManyRepositoryFactory<AssignDcfUser, typeof AssignDcfClient.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('AssignDcfUserRepository') protected assignDcfUserRepositoryGetter: Getter<AssignDcfUserRepository>,
  ) {
    super(AssignDcfClient, dataSource);
    this.assignDcfUsers = this.createHasManyRepositoryFactoryFor('assignDcfUsers', assignDcfUserRepositoryGetter,);
    this.registerInclusionResolver('assignDcfUsers', this.assignDcfUsers.inclusionResolver);
  }
}
