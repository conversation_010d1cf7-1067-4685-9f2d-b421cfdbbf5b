import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  AssignRfUsers,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';





export class UserProfileAssignRfUsersController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-rf-users', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignRfUsers',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignRfUsers)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignRfUsers>,
  ): Promise<AssignRfUsers[]> {
    return this.userProfileRepository.assignRfUsers(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-rf-users', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignRfUsers)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignRfUsers, {
            title: 'NewAssignRfUsersInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignRfUsers: Omit<AssignRfUsers, 'id'>,
  ): Promise<AssignRfUsers> {
    return this.userProfileRepository.assignRfUsers(id).create(assignRfUsers);
  }

  // @patch('/user-profiles/{id}/assign-rf-users', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignRfUsers PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignRfUsers, {partial: true}),
  //       },
  //     },
  //   })
  //   assignRfUsers: Partial<AssignRfUsers>,
  //   @param.query.object('where', getWhereSchemaFor(AssignRfUsers)) where?: Where<AssignRfUsers>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignRfUsers(id).patch(assignRfUsers, where);
  // }

  // @del('/user-profiles/{id}/assign-rf-users', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.AssignRfUsers DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(AssignRfUsers)) where?: Where<AssignRfUsers>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.assignRfUsers(id).delete(where);
  // }
  
}
