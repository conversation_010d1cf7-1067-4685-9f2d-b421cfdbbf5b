import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDcfEntityUser, AssignDcfEntityUserRelations, FormCollection, LocationOne, LocationTwo} from '../models';
import {FormCollectionRepository} from './form-collection.repository';
import {LocationOneRepository} from './location-one.repository';
import {LocationTwoRepository} from './location-two.repository';

export class AssignDcfEntityUserRepository extends DefaultCrudRepository<
  AssignDcfEntityUser,
  typeof AssignDcfEntityUser.prototype.id,
  AssignDcfEntityUserRelations
> {

  public readonly dcf: BelongsToAccessor<FormCollection, typeof AssignDcfEntityUser.prototype.id>;

  public readonly lone: BelongsToAccessor<LocationOne, typeof AssignDcfEntityUser.prototype.id>;

  public readonly ltwo: BelongsToAccessor<LocationTwo, typeof AssignDcfEntityUser.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('FormCollectionRepository') protected formCollectionRepositoryGetter: Getter<FormCollectionRepository>, @repository.getter('LocationOneRepository') protected locationOneRepositoryGetter: Getter<LocationOneRepository>, @repository.getter('LocationTwoRepository') protected locationTwoRepositoryGetter: Getter<LocationTwoRepository>,
  ) {
    super(AssignDcfEntityUser, dataSource);
    this.ltwo = this.createBelongsToAccessorFor('ltwo', locationTwoRepositoryGetter,);
    this.registerInclusionResolver('ltwo', this.ltwo.inclusionResolver);
    this.lone = this.createBelongsToAccessorFor('lone', locationOneRepositoryGetter,);
    this.registerInclusionResolver('lone', this.lone.inclusionResolver);
    this.dcf = this.createBelongsToAccessorFor('dcf', formCollectionRepositoryGetter,);
    this.registerInclusionResolver('dcf', this.dcf.inclusionResolver);
  }
}
