import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewTargetsTwo,
  NewIndicator,
} from '../models';
import {NewTargetsTwoRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewTargetsTwoNewIndicatorController {
  constructor(
    @repository(NewTargetsTwoRepository) protected newTargetsTwoRepository: NewTargetsTwoRepository,
  ) { }

  @get('/new-targets-twos/{id}/new-indicators', {
    responses: {
      '200': {
        description: 'Array of NewTargetsTwo has many NewIndicator',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewIndicator)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewIndicator>,
  ): Promise<NewIndicator[]> {
    return this.newTargetsTwoRepository.newIndicators(id).find(filter);
  }

  @post('/new-targets-twos/{id}/new-indicators', {
    responses: {
      '200': {
        description: 'NewTargetsTwo model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewIndicator)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewTargetsTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewIndicator, {
            title: 'NewNewIndicatorInNewTargetsTwo',
            exclude: ['id'],
            optional: ['newTargetsTwoId']
          }),
        },
      },
    }) newIndicator: Omit<NewIndicator, 'id'>,
  ): Promise<NewIndicator> {
    return this.newTargetsTwoRepository.newIndicators(id).create(newIndicator);
  }

  // @patch('/new-targets-twos/{id}/new-indicators', {
  //   responses: {
  //     '200': {
  //       description: 'NewTargetsTwo.NewIndicator PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewIndicator, {partial: true}),
  //       },
  //     },
  //   })
  //   newIndicator: Partial<NewIndicator>,
  //   @param.query.object('where', getWhereSchemaFor(NewIndicator)) where?: Where<NewIndicator>,
  // ): Promise<Count> {
  //   return this.newTargetsTwoRepository.newIndicators(id).patch(newIndicator, where);
  // }

  // @del('/new-targets-twos/{id}/new-indicators', {
  //   responses: {
  //     '200': {
  //       description: 'NewTargetsTwo.NewIndicator DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewIndicator)) where?: Where<NewIndicator>,
  // ): Promise<Count> {
  //   return this.newTargetsTwoRepository.newIndicators(id).delete(where);
  // }
  
}
