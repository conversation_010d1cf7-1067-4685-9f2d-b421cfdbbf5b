import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewTargetsTwo} from '../models';
import {NewTargetsTwoRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewTargetsTwoController {
  constructor(
    @repository(NewTargetsTwoRepository)
    public newTargetsTwoRepository : NewTargetsTwoRepository,
  ) {}

  @post('/new-targets-twos')
  @response(200, {
    description: 'NewTargetsTwo model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewTargetsTwo)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTargetsTwo, {
            title: 'NewNewTargetsTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    newTargetsTwo: Omit<NewTargetsTwo, 'id'>,
  ): Promise<NewTargetsTwo> {
    return this.newTargetsTwoRepository.create(newTargetsTwo);
  }

  @get('/new-targets-twos/count')
  @response(200, {
    description: 'NewTargetsTwo model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewTargetsTwo) where?: Where<NewTargetsTwo>,
  ): Promise<Count> {
    return this.newTargetsTwoRepository.count(where);
  }

  @get('/new-targets-twos')
  @response(200, {
    description: 'Array of NewTargetsTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewTargetsTwo, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewTargetsTwo) filter?: Filter<NewTargetsTwo>,
  ): Promise<NewTargetsTwo[]> {
    return this.newTargetsTwoRepository.find(filter);
  }


  @get('/new-targets-twos/{id}')
  @response(200, {
    description: 'NewTargetsTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewTargetsTwo, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewTargetsTwo, {exclude: 'where'}) filter?: FilterExcludingWhere<NewTargetsTwo>
  ): Promise<NewTargetsTwo> {
    return this.newTargetsTwoRepository.findById(id, filter);
  }

  @patch('/new-targets-twos/{id}')
  @response(204, {
    description: 'NewTargetsTwo PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTargetsTwo, {partial: true}),
        },
      },
    })
    newTargetsTwo: NewTargetsTwo,
  ): Promise<void> {
    await this.newTargetsTwoRepository.updateById(id, newTargetsTwo);
  }

  @put('/new-targets-twos/{id}')
  @response(204, {
    description: 'NewTargetsTwo PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newTargetsTwo: NewTargetsTwo,
  ): Promise<void> {
    await this.newTargetsTwoRepository.replaceById(id, newTargetsTwo);
  }

  @del('/new-targets-twos/{id}')
  @response(204, {
    description: 'NewTargetsTwo DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newTargetsTwoRepository.deleteById(id);
  }
}
