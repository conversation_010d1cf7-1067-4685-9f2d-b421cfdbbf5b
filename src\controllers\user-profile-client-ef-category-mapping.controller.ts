import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  response,
} from '@loopback/rest';
import {
  ClientEfCategoryMapping,
  UserProfile
} from '../models';
import {ClientEfCategoryMappingRepository, NewEfStdRepository, NewEfSubcategory1Repository, UserProfileRepository} from '../repositories';

export class UserProfileClientEfCategoryMappingController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(NewEfSubcategory1Repository) protected newEfSubcategory1Repository: NewEfSubcategory1Repository,
    @repository(NewEfStdRepository) protected newEfStdRepository: NewEfStdRepository,
    @repository(ClientEfCategoryMappingRepository) protected clientEfCategoryMappingRepository: ClientEfCategoryMappingRepository
  ) { }

  @get('/user-profiles/{id}/client-ef-category-mappings', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many ClientEfCategoryMapping',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ClientEfCategoryMapping)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ClientEfCategoryMapping>,
  ): Promise<ClientEfCategoryMapping[]> {
    return this.userProfileRepository.clientEfCategoryMappings(id).find(filter);
  }

  @post('/user-profiles/{id}/client-ef-category-mappings', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(ClientEfCategoryMapping)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryMapping, {
            title: 'NewClientEfCategoryMappingInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) clientEfCategoryMapping: Omit<ClientEfCategoryMapping, 'id'>,
  ): Promise<ClientEfCategoryMapping> {
    return this.userProfileRepository.clientEfCategoryMappings(id).create(clientEfCategoryMapping);
  }

  @patch('/user-profiles/{id}/client-ef-category-mappings', {
    responses: {
      '200': {
        description: 'UserProfile.ClientEfCategoryMapping PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ClientEfCategoryMapping, {partial: true}),
        },
      },
    })
    clientEfCategoryMapping: Partial<ClientEfCategoryMapping>,
    @param.query.object('where', getWhereSchemaFor(ClientEfCategoryMapping)) where?: Where<ClientEfCategoryMapping>,
  ): Promise<Count> {
    return this.userProfileRepository.clientEfCategoryMappings(id).patch(clientEfCategoryMapping, where);
  }

  @del('/user-profiles/{id}/client-ef-category-mappings', {
    responses: {
      '200': {
        description: 'UserProfile.ClientEfCategoryMapping DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(ClientEfCategoryMapping)) where?: Where<ClientEfCategoryMapping>,
  ): Promise<Count> {
    return this.userProfileRepository.clientEfCategoryMappings(id).delete(where);
  }

  @post('/get-client-ef-category-mapping')
  @response(200, {
    description: 'Retrieve assignments or return empty mappings',
  })
  async getClientEfCategoryMapping(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {type: 'number'},
              efCategoryId: {type: 'number'},
              efStandardId: {type: 'number'},
              efGhgCatId: {type: 'number'},
              efGhgSubCatId: {type: 'number'},
              locations: {
                type: 'object',
                properties: {
                  tier1_id: {type: 'number'},
                  tier2_id: {type: 'number', nullable: true},
                  tier3_id: {type: 'number', nullable: true},
                },
              },
            },
            required: ['userProfileId', 'locations', 'efCategoryId', 'efStandardId', 'efGhgCatId', 'efGhgSubCatId'],
          },
        },
      },
    })
    requestBody: {
      userProfileId: number;
      efCategoryId: number;
      efStandardId: number;
      efGhgCatId: number;
      efGhgSubCatId: number;
      locations: {
        tier1_id?: number;
        tier2_id?: number | null;
        tier3_id?: number | null;
      };
    },
  ): Promise<any> {
    const {userProfileId, efCategoryId, efStandardId, efGhgCatId, efGhgSubCatId, locations} = requestBody;

    // Fetch assignments matching the criteria
    const assignments = await this.clientEfCategoryMappingRepository.find({
      where: {
        and: [
          {efCategoryId},
          {efGhgSubCatId},
          {efGhgCatId},
          {efStandardId},
          {userProfileId},
          {
            or: [
              {tier1_id: 0, tier2_id: null, tier3_id: null}, // Corporate level
              {tier1_id: locations.tier1_id, tier2_id: 0, tier3_id: null}, // Country level
              {tier1_id: locations.tier1_id, tier2_id: locations.tier2_id, tier3_id: 0}, // Region level
              {tier1_id: locations.tier1_id, tier2_id: locations.tier2_id, tier3_id: locations.tier3_id}, // Site level
            ],
          },
        ],
      },

    });
    let selected_ids: string[] = [];

    let disabled_ids: string[] = [];
    const subCategory = await this.newEfSubcategory1Repository.find({"where": {"newEfCategoryId": efCategoryId}, "include": [{"relation": "newEfSubcategory2s", "scope": {"include": [{"relation": "newEfSubcategory3s", "scope": {"include": ["newEfSubcategory4s"]}}]}}]});
    if (assignments.length > 0) {
      // Process assignments matching the exact location tiers
      assignments.forEach((assignment) => {
        if (
          assignment.tier1_id === locations.tier1_id &&
          assignment.tier2_id === locations.tier2_id &&
          assignment.tier3_id === locations.tier3_id
        ) {
          selected_ids = assignment.selected_ids || [];

        }
      });

      // Collect all checked IDs from higher-level assignments
      const allCheckedIds = new Set<string>();
      assignments.forEach((assignment) => {
        if (
          (assignment.tier1_id === 0 && assignment.tier2_id === null && assignment.tier3_id === null) ||
          (assignment.tier1_id === locations.tier1_id && assignment.tier2_id === 0 && assignment.tier3_id === null) ||
          (assignment.tier1_id === locations.tier1_id && assignment.tier2_id === locations.tier2_id && assignment.tier3_id === 0)
        ) {
          assignment.selected_ids?.forEach((id: string) => allCheckedIds.add(id));
        }
      });

      // Compute unique disabled IDs by excluding current checked IDs
      disabled_ids = [...allCheckedIds].filter((id) => !selected_ids.includes(id));
    }

    return {
      selected_ids,
      disabled_ids, subCategory
    };
  }

  @post('/create-client-ef-category-mapping-custom')
  @response(200, {
    description: 'IndividualEFLocationRole model instance',
    content: {'application/json': {schema: getModelSchemaRef(ClientEfCategoryMapping)}},
  })
  async createAssignment(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {type: 'number'},
              efCategoryId: {type: 'number'},
              efStandardId: {type: 'number'},
              efGhgCatId: {type: 'number'},
              efGhgSubCatId: {type: 'number'},
              locations: {
                type: 'object',
                properties: {
                  tier1_id: {type: 'number'},
                  tier2_id: {type: 'number', nullable: true},
                  tier3_id: {type: 'number', nullable: true},
                },
              },
            },
            required: ['userProfileId', 'locations', 'efCategoryId', 'efStandardId', 'efGhgCatId', 'efGhgSubCatId'],
          },
        },
      },
    })
    requestBody: any,
  ): Promise<any> {
    const {userProfileId, efCategoryId, efStandardId, efGhgCatId, efGhgSubCatId, locations, selected_ids, created_on, created_by, modified_on, modified_by} = requestBody;

    const savedUserLocationRoles: ClientEfCategoryMapping[] = [];

    const existingUserLocationRole = await this.clientEfCategoryMappingRepository.findOne({
      where: {
        and: [
          {tier1_id: locations.tier1_id},
          {tier2_id: locations.tier2_id},
          {tier3_id: locations.tier3_id},
          {userProfileId: userProfileId},
          {efCategoryId},
          {efStandardId},
          {efGhgCatId},
          {efGhgSubCatId}
        ],
      },
    });

    if (existingUserLocationRole) {
      existingUserLocationRole.selected_ids = selected_ids;
      existingUserLocationRole.modified_on = modified_on;
      existingUserLocationRole.modified_by = modified_by;
      await this.clientEfCategoryMappingRepository.updateById(existingUserLocationRole.id, existingUserLocationRole);
      savedUserLocationRoles.push(existingUserLocationRole);
    } else {
      const userLocationRole = new ClientEfCategoryMapping({
        efCategoryId, efStandardId, efGhgCatId, efGhgSubCatId,
        selected_ids,
        tier1_id: locations.tier1_id,
        tier2_id: locations.tier2_id,
        tier3_id: locations.tier3_id,
        created_on,
        created_by,
        userProfileId,
      });

      const savedUserLocationRole = await this.clientEfCategoryMappingRepository.create(userLocationRole);
      savedUserLocationRoles.push(savedUserLocationRole);
    }

    // Handle role adjustments based on cases
    if (locations.tier1_id === 0 && locations.tier2_id === null && locations.tier3_id === null) {
      // Case 1: Remove roles based on all other cases
      await this.adjustRoles(efCategoryId, efStandardId, efGhgCatId, efGhgSubCatId, userProfileId, selected_ids, {tier1_id: {neq: 0}});
    } else if (locations.tier1_id !== 0 && locations.tier2_id === 0 && locations.tier3_id === null) {
      // Case 2: Remove roles based on Case 2 & 3
      await this.adjustRoles(efCategoryId, efStandardId, efGhgCatId, efGhgSubCatId, userProfileId, selected_ids, {tier1_id: locations.tier1_id, tier2_id: {neq: 0}});
    } else if (locations.tier1_id !== 0 && locations.tier2_id !== 0 && locations.tier3_id === 0) {
      // Case 3: Remove roles based on Case 3
      await this.adjustRoles(efCategoryId, efStandardId, efGhgCatId, efGhgSubCatId, userProfileId, selected_ids, {
        tier1_id: locations.tier1_id,
        tier2_id: locations.tier2_id,
        tier3_id: {neq: 0},
      });
    } else if (
      locations.tier1_id !== 0 &&
      locations.tier2_id !== 0 &&
      locations.tier3_id !== 0
    ) {
      // Case 4: No removal needed
    }
    let repo = await this.clientEfCategoryMappingRepository.find({where: {userProfileId: userProfileId, efCategoryId, efStandardId, efGhgCatId, efGhgSubCatId}});
    return {object: savedUserLocationRoles, data: repo};
  }

  async adjustRoles(efCategoryId: number, efStandardId: number, efGhgCatId: number, efGhgSubCatId: number, userProfileId: number, selected_ids: string[], locationConditions: any) {
    const userLocationRoles = await this.clientEfCategoryMappingRepository.find({
      where: {
        and: [
          {efCategoryId}, {efStandardId}, {efGhgCatId}, {efGhgSubCatId},
          {userProfileId: userProfileId},
          locationConditions,
        ],
      },
    });

    for (const role of userLocationRoles) {
      if (role.selected_ids) {
        role.selected_ids = role.selected_ids.filter((roleId: string) => !selected_ids.includes(roleId));
        await this.clientEfCategoryMappingRepository.updateById(role.id, role);
      }
    }
  }














}
