import {belongsTo, Entity, model, property} from '@loopback/repository';
import {LocationThree} from './location-three.model';
import {ResponseFormCollection} from './response-form-collection.model';

@model()
export class AssignDfEntityUser extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;


  @property({
    type: 'array',
    itemType: 'number',
  })
  reporter_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  reviewer_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  approver_ids?: number[];



  @property({
    type: 'string',
  })
  start_date?: string;

  @property({
    type: 'string',
  })
  end_date?: string;

  @property({
    type: 'number',
  })
  frequency?: number;



  @property({
    type: 'number',
  })
  level?: number;

  @property({
    type: 'number',
  })
  locationId?: number;
  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'any',
  })
  comments?: any;

  @property({
    type: 'number',
  })
  type?: number;
  @property({
    type: 'number',
  })
  entityAssId?: number;
  @property({
    type: 'number',
  })
  tier0_id?: number;
  @property({
    type: 'number',
  })
  tier1_id?: number;
  @property({
    type: 'number',
  })
  tier2_id?: number;
  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => ResponseFormCollection)
  dfId: number;

  @belongsTo(() => LocationThree, {name: 'lthree'})
  tier3_id: number;

  constructor(data?: Partial<AssignDfEntityUser>) {
    super(data);
  }
}

export interface AssignDfEntityUserRelations {
  // describe navigational properties here
}

export type AssignDfEntityUserWithRelations = AssignDfEntityUser & AssignDfEntityUserRelations;
