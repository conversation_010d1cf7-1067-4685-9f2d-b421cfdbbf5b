import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class NewTargets extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  baseValue?: string;

  @property({
    type: 'string',
  })
  targetValue?: string;

  @property({
    type: 'string',
  })
  baseDate?: string;

  @property({
    type: 'string',
  })
  targetDate?: string;

  @property({
    type: 'string',
  })
  baseUnit?: string;

  @property({
    type: 'string',
  })
  targetUnit?: string;

  @property({
    type: 'string',
  })
  frequency?: string;

  @property({
    type: 'string',
  })
  interim_frequency?: string;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  interim_target?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  comments?: any[];

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  newGoalsId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<NewTargets>) {
    super(data);
  }
}

export interface NewTargetsRelations {
  // describe navigational properties here
}

export type NewTargetsWithRelations = NewTargets & NewTargetsRelations;
