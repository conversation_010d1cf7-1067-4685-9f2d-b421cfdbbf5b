import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {SupplierSectionSubmission} from '../models';
import {SupplierSectionSubmissionRepository} from '../repositories';

export class SupplierSectionSubmissionController {
  constructor(
    @repository(SupplierSectionSubmissionRepository)
    public SupplierSectionSubmissionRepository: SupplierSectionSubmissionRepository
  ) { }

  @post('/supplier-section-submissions')
  @response(200, {
    description: 'SupplierSectionSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierSectionSubmission)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierSectionSubmission, {
            title: 'NewSupplierSectionSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    SupplierSectionSubmission: Omit<SupplierSectionSubmission, 'id'>,
  ): Promise<SupplierSectionSubmission> {
    return this.SupplierSectionSubmissionRepository.create(SupplierSectionSubmission);
  }

  @get('/supplier-section-submissions/count')
  @response(200, {
    description: 'SupplierSectionSubmission model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SupplierSectionSubmission) where?: Where<SupplierSectionSubmission>,
  ): Promise<Count> {
    return this.SupplierSectionSubmissionRepository.count(where);
  }

  @get('/supplier-section-submissions')
  @response(200, {
    description: 'Array of SupplierSectionSubmission model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SupplierSectionSubmission, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SupplierSectionSubmission) filter?: Filter<SupplierSectionSubmission>,
  ): Promise<SupplierSectionSubmission[]> {
    return this.SupplierSectionSubmissionRepository.find(filter);
  }

  @patch('/supplier-section-submissions')
  @response(200, {
    description: 'SupplierSectionSubmission PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierSectionSubmission, {partial: true}),
        },
      },
    })
    SupplierSectionSubmission: SupplierSectionSubmission,
    @param.where(SupplierSectionSubmission) where?: Where<SupplierSectionSubmission>,
  ): Promise<Count> {
    return this.SupplierSectionSubmissionRepository.updateAll(SupplierSectionSubmission, where);
  }

  @get('/supplier-section-submissions/{id}')
  @response(200, {
    description: 'SupplierSectionSubmission model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SupplierSectionSubmission, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SupplierSectionSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<SupplierSectionSubmission>
  ): Promise<SupplierSectionSubmission> {
    return this.SupplierSectionSubmissionRepository.findById(id, filter);
  }

  @patch('/supplier-section-submissions/{id}')
  @response(204, {
    description: 'SupplierSectionSubmission PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierSectionSubmission, {partial: true}),
        },
      },
    })
    SupplierSectionSubmission: SupplierSectionSubmission,
  ): Promise<void> {
    await this.SupplierSectionSubmissionRepository.updateById(id, SupplierSectionSubmission);
  }

  @put('/supplier-section-submissions/{id}')
  @response(204, {
    description: 'SupplierSectionSubmission PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() SupplierSectionSubmission: SupplierSectionSubmission,
  ): Promise<void> {
    await this.SupplierSectionSubmissionRepository.replaceById(id, SupplierSectionSubmission);
  }

  @del('/supplier-section-submissions/{id}')
  @response(204, {
    description: 'SupplierSectionSubmission DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.SupplierSectionSubmissionRepository.deleteById(id);
  }

  @post('/supplier-section-submissions-custom')
  @response(200, {
    description: 'SupplierSectionSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierSectionSubmission)}},
  })
  async customSectionSubmisison(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              supplierAssessmentAssignmentId: {type: 'string'},
              assessmentSectionId: {type: 'string'},
              assessmentSubSection1Id: {type: 'string'},
              assessmentSubSection2Id: {type: 'string'},
              dealerResponseFormId: {type: 'number'},
              userProfileId: {type: 'number'},
              data1: {type: 'string'},
              type: {type: 'number'},
              vendorCode: {type: 'string'}
            },
            required: ['type', 'data1', 'supplierAssessmentAssignmentId', 'assessmentSectionId', 'assessmentSubSection1Id', 'assessmentSubSection2Id', 'dealerResponseFormId', 'userProfileId'],
          },
        },
      },
    })
    requestBody: any,
  ): Promise<any> {
    const {type, data1, vendorCode, supplierAssessmentAssignmentId, assessmentSectionId, assessmentSubSection1Id, assessmentSubSection2Id, dealerResponseFormId, userProfileId} = requestBody
    const found = await this.SupplierSectionSubmissionRepository.find({where: {assessmentSectionId, assessmentSubSection1Id, assessmentSubSection2Id, supplierAssessmentAssignmentId, userProfileId}})
    if (found && found.length === 1) {
      const result = await this.SupplierSectionSubmissionRepository.updateById(found[0].id, {data1, type})
      return {status: 2, data: result}
    } else if (!found.length) {
      const result = await this.SupplierSectionSubmissionRepository.create({type, data1, vendorCode, supplierAssessmentAssignmentId, assessmentSectionId, assessmentSubSection1Id, assessmentSubSection2Id, dealerResponseFormId, userProfileId})
      return {status: 1, data: result}
    } else {
      return {status: 0}
    }
  }
  @post('/retrieve-supplier-section-submissions-custom')
  @response(200, {
    description: 'SupplierSectionSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(SupplierSectionSubmission)}},
  })
  async retrieveCustomSectionSubmisison(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              supplierAssessmentAssignmentId: {type: 'string'},
              assessmentSectionId: {type: 'string'},
              assessmentSubSection1Id: {type: 'string'},
              assessmentSubSection2Id: {type: 'string'},
              userProfileId: {type: 'number'},
              vendorCode: {type: 'string'}

            },
            required: ['supplierAssessmentAssignmentId', 'assessmentSectionId', 'assessmentSubSection1Id', 'assessmentSubSection2Id', 'userProfileId'],
          },
        },
      },
    })
    requestBody: any,
  ): Promise<any> {
    const {vendorCode, supplierAssessmentAssignmentId, assessmentSectionId, assessmentSubSection1Id, assessmentSubSection2Id, userProfileId} = requestBody
    const found = await this.SupplierSectionSubmissionRepository.find({where: {assessmentSectionId, vendorCode, assessmentSubSection1Id, assessmentSubSection2Id, supplierAssessmentAssignmentId, userProfileId}})
    if (found && found.length === 1) {
      const result = await this.SupplierSectionSubmissionRepository.findById(found[0].id)
      return {status: true, data: result}
    } else {
      return {status: false}
    }
  }

}
