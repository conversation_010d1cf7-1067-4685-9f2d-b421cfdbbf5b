import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Survey,
  Category,
} from '../models';
import {SurveyRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SurveyCategoryController {
  constructor(
    @repository(SurveyRepository)
    public surveyRepository: SurveyRepository,
  ) { }

  @get('/surveys/{id}/category', {
    responses: {
      '200': {
        description: 'Category belonging to Survey',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Category)},
          },
        },
      },
    },
  })
  async getCategory(
    @param.path.number('id') id: typeof Survey.prototype.id,
  ): Promise<Category> {
    return this.surveyRepository.category(id);
  }
}
