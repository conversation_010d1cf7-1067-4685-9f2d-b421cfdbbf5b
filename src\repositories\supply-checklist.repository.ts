import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SupplyChecklist, SupplyChecklistRelations, UserProfile} from '../models';
import {UserProfileRepository} from './user-profile.repository';

export class SupplyChecklistRepository extends DefaultCrudRepository<
  SupplyChecklist,
  typeof SupplyChecklist.prototype.id,
  SupplyChecklistRelations
> {

  public readonly createdBy: BelongsToAccessor<UserProfile, typeof SupplyChecklist.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('UserProfileRepository') protected userProfileRepositoryGetter: Getter<UserProfileRepository>,
  ) {
    super(SupplyChecklist, dataSource);
    this.createdBy = this.createBelongsToAccessorFor('createdBy', userProfileRepositoryGetter,);
    this.registerInclusionResolver('createdBy', this.createdBy.inclusionResolver);
  }
}
