import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {filterDataByTierAndLocationByLevel} from '../helper/filter-assignment-year-helper';
import {
  ComputedIndicator,
  NewMetric,
  UserProfile,
} from '../models';
import {NewCategoryRepository, UserProfileRepository} from '../repositories';
import {Helper} from '../services';
import {ClientEfCategoryMappingController} from './client-ef-category-mapping.controller';
import {UserProfileController} from './user-profile.controller';

export class UserProfileComputedIndicatorController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @inject('services.HelperProvider')
    public helper: Helper,
    @inject('controllers.UserProfileController')
    public userProfileController: UserProfileController,
    @repository(NewCategoryRepository) public newCategoryRepository: NewCategoryRepository,
    @inject('controllers.ClientEfCategoryMappingController')
    public clientEfCategoryMappingController: ClientEfCategoryMappingController,
  ) { }

  @get('/user-profiles/{id}/computed-indicators', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many ComputedIndicator',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ComputedIndicator)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ComputedIndicator>,
  ): Promise<ComputedIndicator[]> {
    return this.userProfileRepository.computedIndicators(id).find(filter);
  }

  @post('/user-profiles/{id}/computed-indicators', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(ComputedIndicator)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ComputedIndicator, {
            title: 'NewComputedIndicatorInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) computedIndicator: Omit<ComputedIndicator, 'id'>,
  ): Promise<ComputedIndicator> {
    return this.userProfileRepository.computedIndicators(id).create(computedIndicator);
  }

  @patch('/user-profiles/{id}/computed-indicators', {
    responses: {
      '200': {
        description: 'UserProfile.ComputedIndicator PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ComputedIndicator, {partial: true}),
        },
      },
    })
    computedIndicator: Partial<ComputedIndicator>,
    @param.query.object('where', getWhereSchemaFor(ComputedIndicator)) where?: Where<ComputedIndicator>,
  ): Promise<Count> {
    return this.userProfileRepository.computedIndicators(id).patch(computedIndicator, where);
  }

  @del('/user-profiles/{id}/computed-indicators', {
    responses: {
      '200': {
        description: 'UserProfile.ComputedIndicator DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(ComputedIndicator)) where?: Where<ComputedIndicator>,
  ): Promise<Count> {
    return this.userProfileRepository.computedIndicators(id).delete(where);
  }
  @post('/user-profiles/{id}/get-computed-indicator-custom')
  async getComputedIndicator(
    @param.path.number('id') id: number,
    @requestBody({
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              year: {type: 'number'},
              framework: {type: 'string'}
            }
            , required: ['year', 'framework']
          }
        }
      }
    })
    requestBody: {year: number, framework: string},
  ): Promise<any> {
    const {year, framework} = requestBody
    const data = (await this.userProfileRepository.computedIndicators(id).find({where: {reportingYear: year}})).filter((i: any) =>
      i?.overallTags?.some((tag: any) =>
        tag.trim().toLowerCase().includes(framework.trim().toLowerCase())
      )
    )
    if (data.length) {
      return {result: true, data, lastUpdatedOn: data[data.length - 1].lastUpdatedOn, message: 'Data already exist'}
    } else {
      this.getComputedIndicator(id, {year, framework})
      return {result: false, message: 'Data being update in backend, comeback after 5mins to see report'}
    }

  }

  @post('/user-profiles/{id}/update-computed-indicator-custom')
  async setComputedIndicator(
    @param.path.number('id') id: number,
    @requestBody({
      required: true,
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              year: {type: 'number'},
              framework: {type: 'string'}
            }
            , required: ['year', 'framework']
          }
        }
      }
    })
    requestBody: {year: number, framework: string},
  ): Promise<any> {
    const {year, framework} = requestBody
    const assingedIndicator = await this.userProfileRepository.assignDcfClients(id).find()
    const adminData = (await this.userProfileController.filteredUP({where: {id}}))?.[0] || {}
    const fymonth = adminData.fyStartMonth ? DateTime.fromISO(adminData.fyStartMonth, {zone: 'utc'}).plus({months: 1}).month : 1
    try {
      if (assingedIndicator.length === 1) {
        const indicator_list: NewMetric[] = []
        const assignedIndicator = assingedIndicator[0]
        const esgCategory = await this.newCategoryRepository.find({
          include: [
            {
              relation: "newTopics",
              scope: {
                include: [{
                  relation: "newMetrics", scope: {
                    include: ["newDataPoints"],
                  }
                }],
              },
            },
          ],
        });
        const shapedCategory = esgCategory.map(item => {
          if (item.newTopics) {
            item.newTopics = item.newTopics.filter(topics =>
              topics.newMetrics && topics.newMetrics.length > 0
            );
          }
          return item;
        }).filter(item => item.newTopics && item.newTopics.length > 0)
        shapedCategory.flatMap(i => i.newTopics).forEach((top) => {
          if (assignedIndicator.topic_ids && top.id && assignedIndicator.topic_ids.includes(top.id) && (top.tag === null || parseFloat(top.tag) === id)) {
            top.newMetrics.forEach((met) => {

              if ((Array.isArray(met.data1) && met.data1.length && met.data1[0].type === 0) && met.id && assignedIndicator.metric_ids && assignedIndicator.metric_ids.includes(met.id) && !indicator_list.map(i => i.id).includes(met.id) && (met.tag === null || parseFloat(met.tag) === id)) {
                indicator_list.push(met)

              }
            })
          }
        })
        const filteredLocations = await this.userProfileRepository.locationOnes(id).find({
          include: [
            {
              relation: "locationTwos",
              scope: {
                include: [{relation: "locationThrees"}],
              },
            },
          ],
        });

        const shapedSite = filteredLocations.map(item => {
          if (item.locationTwos) {
            item.locationTwos = item.locationTwos.filter(locationTwo =>
              locationTwo.locationThrees && locationTwo.locationThrees.length > 0
            );
          }
          return item;
        }).filter(item => item.locationTwos && item.locationTwos.length > 0);
        const entityAssignment = await this.userProfileRepository.assignDcfEntities(id).find()

        const entityUserAssignment = (await this.userProfileRepository.assignDcfEntityUsers(id).find())
        const locations = await this.userProfileRepository.locationOnes(id).find({"include": [{"relation": "locationTwos", "scope": {"include": [{"relation": "locationThrees"}]}}]})

        const locations0 = [0]
        const locations1 = locations.map(x => x.id).filter(x => typeof x === 'number')
        const locations2 = locations.flatMap(x => x?.locationTwos || []).map((x: any) => x?.id).filter(x => typeof x === 'number')
        const locations3 = locations.flatMap(x => x?.locationTwos && x?.locationTwos?.flatMap(y => y?.locationThrees || [])).map((x: any) => x?.id).filter(x => typeof x === 'number')
        const locationMap: any = {
          0: locations0,
          1: locations1,
          2: locations2,
          3: locations3
        };

        const filteredAssignments = entityUserAssignment.filter((assignment) => {
          return entityAssignment.some((ent: any) => {
            const tierKey = `tier${assignment.level}_ids`;
            const validLocations = locationMap[assignment?.level || 0] || [];

            // Filter out invalid IDs from ent[tierKey]
            const filteredTierIds = (ent[tierKey] || []).filter((id: number) =>
              validLocations.includes(Number(id))
            );



            // Check if assignment.locationId is present in the filtered IDs
            const isLocationMatch = filteredTierIds.includes(Number(assignment.locationId));

            const isBasicMatch =
              ent.dcfId === assignment.dcfId
            return isBasicMatch && isLocationMatch;
          });
        });

        const res = (await this.userProfileController.filterDerivedAndStandaloneWithIds(indicator_list, shapedCategory.flatMap(x => x?.newTopics?.flatMap(y => y?.newMetrics || []) || []), id)).filter((i: any) =>
          i?.overallTags?.some((tag: any) =>
            tag.trim().toLowerCase().includes(framework.trim().toLowerCase())
          )
        )
        const refinedIndicator = res.map(({newDataPoints, ...item}) => ({...item, title: item.id + ' : ' + item.title, type: item?.standalone_ids?.length === 1 && item.standalone_ids.includes(item.id) ? 1 : ((item?.standalone_ids?.length === 1 && !item.standalone_ids.includes(item.id)) || (item?.standalone_ids?.length > 1)) ? 2 : 0}))
        const assignedEF = await this.clientEfCategoryMappingController.getClientEfCategoryMappingsCustom(id)
        const shapedEFAssignment = await this.processSingleObject(assignedEF, shapedSite)
        const rawData = await this.userProfileRepository.structuredResponses(id).find({
          include: [
            {
              relation: "submitDcf",
              scope: {
                fields: {
                  id: true,
                  return_remarks: true,
                  approved_on: true,
                  locationId: true,
                  level: true,
                  reporter_modified_by: true,
                  reporter_modified_on: true,
                  reviewer_modified_on: true,
                  reviewer_modified_by: true,
                  self: true,
                  approved_by: true,
                  reject: true,
                  type: true,
                  edit: true,
                },
              },
            },
          ],
        })
        const filteredRawDataByYear = this.helper.filterSubmissionsByFiscalYear(
          rawData.filter((i: any) => i.submitDcf),
          year,
          fymonth
        );
        const refinedRawData = await Promise.all(
          filteredRawDataByYear.map(async (i: any) => ({
            formCategory: 1,
            formId: i.dcfId,
            value: i.isNull ? 0 : i.value,
            tags: refinedIndicator.find(y => y?.standalone_ids?.length === 1 && y?.dcfIds?.includes(i.dcfId))?.tags,
            actualTitle: i.title,
            title: i.label.replace(/(<([^>]+)>)/gi, "")
              ?.replace(/\n/g, " ")
              ?.replace(/&nbsp;/g, " ")
              ?.replace("&amp;", "&") || '-', approverComments: i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 3)?.remarks || "No Comments", dateOfApproval: i.submitDcf?.approved_on ? new Date(i.submitDcf?.approved_on).toLocaleString().split(",")[0] : '-', dcfId: i.dcfId, entity: this.helper.getSortedEntity(i.submitDcf.level, i.submitDcf.locationId, locations), periodFrom: i.reporting_period?.[0] || "N/A",
            periodTo:
              i?.reporting_period[i?.reporting_period?.length - 1] ||
              "N/A",
            unitOfMeasure: i?.uom || "-",
            dataType: i?.dataType || null,
            formType: i?.formType || null,
            uniqueId: i?.uniqueId || null,
            locationId: i.submitDcf?.locationId,
            level: i.submitDcf?.level,
            reporter: `${await this.userProfileController.getUserByUPID(i.submitDcf?.reporter_modified_by)}`,
            reportedDate: new Date(i.submitDcf?.reporter_modified_on)
              .toLocaleString()
              .split(",")[0],
            reporting_period: this.helper.getRPTextFormat(i.reporting_period),
            rp: i.reporting_period,
            reviewedDate: i.submitDcf?.reviewer_modified_on ? new Date(i.submitDcf?.reviewer_modified_on).toLocaleString()
              .split(",")[0] : '-',
            reporterComments: i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 1)?.remarks || "No Comments",
            reviewer: i.submitDcf?.self ? 'Self' : ` ${await this.userProfileController.getUserByUPID(i.submitDcf?.reviewer_modified_by)}`,

            efValue: i.efValue,
            submitId: i.submitDcfId,
            reviewerComments:
              i.submitDcf?.return_remarks?.reverse()?.find((x: any) => x.user_type === 2)?.remarks || "No Comments",
            approver: i.submitDcf?.approved_by
              ? `${this.userProfileController.getUserByUPID(i.submitDcf?.approved_by)}`
              : "N/A",
            status: i.submitDcf?.type ? i.submitDcf?.type === 1 ? 'Under Review' : i.submitDcf?.type === 2 ? 'Under Approval' : i.submitDcf?.type === 3 ? 'Approved' : 'Pending Submission' : 'Pending Submission'
          }))
        );
        const data = await this.processCustomMetrics(refinedIndicator, refinedRawData, shapedEFAssignment, shapedSite)
        const mappedIndicators = data.map((indicator: any) => {
          return indicator.contributingEntities.map((entity: any) => {
            return entity.contributingReportingPeriod.map((report: any) => {
              return report.contributingDataPoints.map((dataPoint: any) => {
                return new ComputedIndicator({
                  indicatorId: indicator.indicatorId,
                  indicatorTitle: indicator.indicatorTitle,
                  indicatorType: indicator.indicatorType,
                  indicatorUnit: indicator.indicatorUnit,
                  frameworkTags: indicator.frameworkTags,
                  locationId: entity.locationId,
                  level: entity.level,
                  reportingYear: this.helper.getReportingFiscalYearByReportingperiod(dataPoint.rp, fymonth),
                  reporting_period: dataPoint.rp,
                  periodFrom: dataPoint.periodFrom,
                  periodTo: dataPoint.periodTo,
                  formCategory: dataPoint.formCategory,
                  dcfId: dataPoint.dcfId,
                  value: parseFloat(dataPoint.value),
                  actualTitle: dataPoint.actualTitle,
                  title: dataPoint.title,
                  approverComments: dataPoint.approverComments,
                  dateOfApproval: dataPoint.dateOfApproval,
                  unitOfMeasure: dataPoint.unitOfMeasure,
                  computedValue: parseFloat(dataPoint.computedValue),
                  computedCo2Value: parseFloat(dataPoint.computedCo2Value),
                  computedCh4Value: parseFloat(dataPoint.computedCh4Value),
                  computedN2oValue: parseFloat(dataPoint.computedN2oValue),
                  uniqueId: dataPoint.uniqueId,
                  reporter: dataPoint.reporter,
                  reportedDate: dataPoint.reportedDate,
                  reviewedDate: dataPoint.reviewedDate,
                  reporterComments: dataPoint.reporterComments,
                  reviewer: dataPoint.reviewer,
                  reviewerComments: dataPoint.reviewerComments,
                  submitId: dataPoint.submitId,
                  emissionFactorName: dataPoint.emissionFactorName,
                  emissionFactorValue: dataPoint.emissionFactorValue,
                  emissionFactorCo2Value: dataPoint.emissionFactorCo2Value,
                  emissionFactorCh4Value: dataPoint.emissionFactorCh4Value,
                  emissionFactorN2oValue: dataPoint.emissionFactorN2oValue,
                  efkey: dataPoint.efkey,
                  efValue: parseFloat(dataPoint.efValue),
                  methodology: dataPoint.methodology,
                  approver: dataPoint.approver
                });
              });
            }).flat();
          }).flat();
        }).flat();
        return mappedIndicators
      }
    } catch (e) {
      console.log(e)
    }
  }
  processSingleObject(locations: any[], locationData: any) {
    return locations
      .map(
        ({
          tier1_id,
          tier2_id,
          tier3_id,
          efGhgCat,
          efStandard,
          efCategory,
          efGhgSubCat,
          hierarchicalData,
          efGhgCatId,
          efStandardId,
          efCategoryId,
          efGhgSubCatId,
        }) => {
          let tier1 = "Global";
          let tier2 = "All";
          let tier3 = "All";

          if (tier1_id) {
            const tier1Data = locationData.find((loc: any) => loc.id === tier1_id);
            if (tier1Data) tier1 = tier1Data.name;

            if (tier2_id) {
              const tier2Data = tier1Data.locationTwos.find(
                (loc: any) => loc.id === tier2_id
              );
              if (tier2Data) {
                tier2 = tier2Data.name;

                if (tier3_id) {
                  const tier3Data = tier2Data.locationThrees.find(
                    (loc: any) => loc.id === tier3_id
                  );
                  if (tier3Data) tier3 = tier3Data.name;
                }
              }
            }
          }

          return hierarchicalData.map((x: any) => ({
            ...x,
            ...{
              uniqueEfId:
                `S${efStandardId}-${x?.dateId ? "T" + x.dateId : "NA"
                }-G${efGhgCatId}-GS${efGhgSubCatId}-I${efCategoryId}-` +
                x.hierarchyId, uniqueId: x.hierarchyId,
              startMonth: x?.startDate || "NA",
              endMonth: x?.endDate || "NA",
              methodology: x?.methodology || "Not Found",
              tier1_id,
              tier2_id,
              tier3_id,
              tier1,
              tier2,
              tier3,
              standard: efStandard?.title || "Not Found",
              ghgcategory: efGhgCat?.title || "Not Found",
              ghgsubcategory: efGhgSubCat?.title || "Not Found",
              itemId: efCategory?.id || "",
              item: efCategory?.title || "Not Found",
              co2e: x?.co2e || "-",
              co2: x?.co2 || "-",
              ch4: x?.ch4 || "-",
              n2o: x?.n2o || "-",
            },
          }));
        }
      )
      .reduce((a, b) => [...a, ...b], []);
  }
  getValueByPercentage(value: any, percent: any) {
    const val = parseFloat(value);
    const pct = parseFloat(percent);

    if (isNaN(val) || isNaN(pct)) return 0;

    return (val * pct) / 100;
  }
  async processCustomMetrics(customMetricResponse: any, filtered: any[], efassignment: any[], rawsitelist: any[]) {
    const metricArray: any = []

    for (const indi of customMetricResponse) {

      // Process filtered data for each metric response
      await Promise.all(
        filtered
          .filter(x => indi.dcfIds.includes(x?.dcfId))
          .map(async (i) => {
            const index = metricArray.findIndex((y: any) => y.indicatorId === indi.id);
            // i.methodology = indi.type === 1 ? '-' : indi.standalone_ids.map(i => "MT" + i).join(' + ')
            if (i.isNull) {
              Object.assign(i, {value: 0})
            }
            if (i.formType === 2 && i.dataType === 1) {
              const lastDate = DateTime.fromFormat(i.reporting_period.split(' to ')[0], 'LLL-yyyy', {zone: 'utc'});
              let filteredAssignment1 = []
              if (indi.id === 172) {
                const titleParts = i.actualTitle.split('>').map((s: any) => s.trim().toLowerCase());
                filteredAssignment1 = efassignment.filter(x => x.itemId === 16 || x.itemId === 58).filter(x => {

                  const match1 = (x.subCategory1 || '')?.trim().toLowerCase().includes(titleParts[0]);
                  const match2 = (x.subCategory2 || '')?.trim().toLowerCase().includes(titleParts[1]);
                  const match3 = (x.subCategory3 || '')?.trim().toLowerCase().includes(titleParts[2]);

                  const titleMatch =
                    titleParts.length === 3 ? match1 && match2 && match3 :
                      titleParts.length === 2 ? match1 && match2 :
                        titleParts.length === 1 ? match1 : false;

                  return titleMatch && x.startMonth && x.startMonth !== 'NA';
                });


              } else {
                filteredAssignment1 = efassignment.filter(
                  x => (x.hierarchyId === i.uniqueId) && x.startMonth && x.startMonth !== 'NA'
                );
              }

              const dateIndex = filteredAssignment1.findIndex((dateRange) => {
                const startDate = DateTime.fromFormat(dateRange.startMonth, 'LLL-yyyy');
                const endDate = (dateRange.endMonth && dateRange.endMonth !== "Present")
                  ? DateTime.fromFormat(dateRange.endMonth, 'LLL-yyyy')
                  : DateTime.local();

                // Check if reporting_date falls within the range
                return lastDate >= startDate && lastDate <= endDate;
              });

              if (dateIndex !== -1) {
                const filteredLocation = filterDataByTierAndLocationByLevel(
                  [{locationId: i.locationId, level: i.level}],
                  rawsitelist,
                  filteredAssignment1[dateIndex].tier1_id,
                  filteredAssignment1[dateIndex].tier2_id,
                  filteredAssignment1[dateIndex].tier3_id
                );

                if (filteredLocation.length > 0) {
                  const obj = filteredAssignment1[dateIndex];
                  Object.assign(i, {
                    emissionFactorName: obj.standard,
                    emissionFactorValue: obj.co2e,
                    emissionFactorCo2Value: obj.co2,
                    emissionFactorCh4Value: obj.ch4,
                    emissionFactorN2oValue: obj.n2o,
                    efkey: obj.uniqueEfId,
                    methodology: (indi.id === 172 && i.dcfId === 257) ? "Electricity Consumption * 17.68 % * Emission Factor / 1000 " : i.dcfId === 10 ? '(Quantity of Refrigerant Refilled * Emission Factor)/1000' : i.dcfId === 15 ? '1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000' : i.dcfId === 257 ? 'Electricity Consumption* Emission Factor' : obj.methodology,
                    computedValue: (indi.id === 172 && i.dcfId === 257) ? ((obj.co2e / 1000) * this.getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.co2e / 1000) * i.value).toFixed(3),
                    computedCo2Value: (indi.id === 172 && i.dcfId === 257) ? ((obj.co2 / 1000) * this.getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.co2 / 1000) * i.value).toFixed(3),
                    computedCh4Value: (indi.id === 172 && i.dcfId === 257) ? ((obj.ch4 / 1000) * this.getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.ch4 / 1000) * i.value).toFixed(3),
                    computedN2oValue: (indi.id === 172 && i.dcfId === 257) ? ((obj.n2o / 1000) * this.getValueByPercentage(i.value, 17.68)).toFixed(3) : ((obj.n2o / 1000) * i.value).toFixed(3),

                  });
                }
              } else if (i?.efValue) {

                Object.assign(i, {
                  emissionFactorName: '-',
                  efkey: '-',
                  formula: '-',
                  computedValue: i?.efValue,
                })
              } else {
                // Set default values if no valid date range is found
                Object.assign(i, {
                  emissionFactorName: '-',
                  emissionFactorValue: '-',
                  efkey: "-",
                  formula: "-",
                  computedValue: "-",
                });
              }
            } else if (i.efKey) {

              const lastDate = DateTime.fromFormat(i.reporting_period.split(' to ')[0], 'LLL-yyyy', {zone: 'utc'});
              const filteredAssignment1 = efassignment.filter(
                x => (x.hierarchyId === i.efKey)
              );

              const dateIndex = filteredAssignment1.findIndex((dateRange) => {
                const startDate = DateTime.fromFormat(dateRange.startMonth, 'LLL-yyyy');
                const endDate = (dateRange.endMonth && dateRange.endMonth !== "Present")
                  ? DateTime.fromFormat(dateRange.endMonth, 'LLL-yyyy')
                  : DateTime.local();

                // Check if reporting_date falls within the range
                return lastDate >= startDate && lastDate <= endDate;
              });



              if (dateIndex !== -1) {
                const filteredLocation = filterDataByTierAndLocationByLevel(
                  [{locationId: i.locationId, level: i.level}],
                  rawsitelist,
                  filteredAssignment1[dateIndex].tier1_id,
                  filteredAssignment1[dateIndex].tier2_id,
                  filteredAssignment1[dateIndex].tier3_id
                );

                if (filteredLocation.length > 0) {
                  const obj = filteredAssignment1[dateIndex];
                  Object.assign(i, {
                    value: indi.id === 1642 ? this.getValueByPercentage(i.value, 2) : i.value,
                    emissionFactorName: obj.standard,
                    emissionFactorValue: obj.co2e,
                    emissionFactorCo2Value: obj.co2,
                    emissionFactorCh4Value: obj.ch4,
                    emissionFactorN2oValue: obj.n2o,
                    efkey: obj.uniqueEfId,
                    methodology: i.dcfId === 10 ? '(Quantity of Refrigerant Refilled * Emission Factor)/1000' : i.dcfId === 15 ? '1.By Fuel: (Fuel Consumption  * Emission Factors based on the Fuel used)/1000 2.By KM Driven: (Total KM Driven * Emission Factors based on the type of vehicle used)/1000' : i.dcfId === 257 ? 'Electricity Consumption* Emission Factor' : obj.methodology,
                    computedValue: indi.id === 1642 ? ((obj.co2e / 1000) * this.getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.co2e / 1000) * i.value).toFixed(3),
                    computedCo2Value: indi.id === 1642 ? ((obj.co2 / 1000) * this.getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.co2 / 1000) * i.value).toFixed(3),
                    computedCh4Value: indi.id === 1642 ? ((obj.ch4 / 1000) * this.getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.ch4 / 1000) * i.value).toFixed(3),
                    computedN2oValue: indi.id === 1642 ? ((obj.n2o / 1000) * this.getValueByPercentage(i.value, 2)).toFixed(3) : ((obj.n2o / 1000) * i.value).toFixed(3),

                  });
                }
              } else {
                // Set default values if no valid date range is found
                Object.assign(i, {
                  emissionFactorName: '-',
                  emissionFactorValue: '-',
                  efkey: "-",
                  formula: "-",
                  computedValue: "-",
                });
              }
            } else {
              if (i?.efValue) {
                Object.assign(i, {
                  emissionFactorName: i?.emissionFactorName || '-',
                  efkey: i?.efKey || '-',
                  formula: i?.methodology || '-',
                  computedValue: i?.efValue,
                })
              } else {
                i.computedValue = i.value
              }
            }

            // Update metricArray
            if (index === -1) {
              // Add new indicator
              metricArray.push({
                indicatorId: indi.id,
                frameworkTags: indi.overallTags, title: indi.title, type: indi?.type || null, standalone_ids: indi?.standalone_ids, unit: indi?.data1?.[0]?.unit || '-',
                contributingEntities: [{
                  title: i.entity,
                  locationId: i.locationId,
                  level: i.level,
                  contributingReportingPeriod: [{
                    title: i.reporting_period,
                    status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
                    contributingDataPoints: [i],
                  }]
                }]
              });
            } else {
              // Update existing indicator
              const entityIndex = metricArray[index].contributingEntities.findIndex(
                (y: any) => y.locationId === i.locationId && y.level === i.level
              );

              if (entityIndex === -1) {
                // Add new contributing entity
                metricArray[index].contributingEntities.push({
                  title: i.entity,
                  locationId: i.locationId,
                  level: i.level,
                  contributingReportingPeriod: [{
                    title: i.reporting_period,
                    status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
                    contributingDataPoints: [i],
                  }]
                });
              } else {

                // Update existing contributing entity
                const periodIndex = metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod.findIndex(
                  (y: any) => y.title === i.reporting_period
                );

                if (periodIndex === -1) {
                  // Add new reporting period
                  metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod.push({
                    title: i.reporting_period,
                    status: i.status, periodFrom: i.periodFrom, periodTo: i.periodTo,
                    contributingDataPoints: [i],
                  });
                } else {
                  // Add data point to existing reporting period
                  metricArray[index].contributingEntities[entityIndex].contributingReportingPeriod[periodIndex].contributingDataPoints.push(i);
                }
              }
            }
          })
      );
    }
    return metricArray
  }

}
