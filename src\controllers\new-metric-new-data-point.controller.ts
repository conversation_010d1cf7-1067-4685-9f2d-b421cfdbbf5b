import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewMetric,
  NewDataPoint,
} from '../models';
import {NewMetricRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewMetricNewDataPointController {
  constructor(
    @repository(NewMetricRepository) protected newMetricRepository: NewMetricRepository,
  ) { }

  @get('/new-metrics/{id}/new-data-points', {
    responses: {
      '200': {
        description: 'Array of NewMetric has many NewDataPoint',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewDataPoint)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewDataPoint>,
  ): Promise<NewDataPoint[]> {
    return this.newMetricRepository.newDataPoints(id).find(filter);
  }

  @post('/new-metrics/{id}/new-data-points', {
    responses: {
      '200': {
        description: 'NewMetric model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewDataPoint)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewMetric.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewDataPoint, {
            title: 'NewNewDataPointInNewMetric',
            exclude: ['id'],
            optional: ['newMetricId']
          }),
        },
      },
    }) newDataPoint: Omit<NewDataPoint, 'id'>,
  ): Promise<NewDataPoint> {
    return this.newMetricRepository.newDataPoints(id).create(newDataPoint);
  }

  // @patch('/new-metrics/{id}/new-data-points', {
  //   responses: {
  //     '200': {
  //       description: 'NewMetric.NewDataPoint PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewDataPoint, {partial: true}),
  //       },
  //     },
  //   })
  //   newDataPoint: Partial<NewDataPoint>,
  //   @param.query.object('where', getWhereSchemaFor(NewDataPoint)) where?: Where<NewDataPoint>,
  // ): Promise<Count> {
  //   return this.newMetricRepository.newDataPoints(id).patch(newDataPoint, where);
  // }

  // @del('/new-metrics/{id}/new-data-points', {
  //   responses: {
  //     '200': {
  //       description: 'NewMetric.NewDataPoint DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewDataPoint)) where?: Where<NewDataPoint>,
  // ): Promise<Count> {
  //   return this.newMetricRepository.newDataPoints(id).delete(where);
  // }
}
