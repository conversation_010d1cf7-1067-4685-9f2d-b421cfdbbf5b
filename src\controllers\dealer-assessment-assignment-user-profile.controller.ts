import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  DealerAssessmentAssignment,
  UserProfile,
} from '../models';
import {DealerAssessmentAssignmentRepository} from '../repositories';

export class DealerAssessmentAssignmentUserProfileController {
  constructor(
    @repository(DealerAssessmentAssignmentRepository)
    public dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
  ) { }

  @get('/dealer-assessment-assignments/{id}/user-profile', {
    responses: {
      '200': {
        description: 'UserProfile belonging to DealerAssessmentAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(UserProfile)},
          },
        },
      },
    },
  })
  async getUserProfile(
    @param.path.number('id') id: typeof DealerAssessmentAssignment.prototype.id,
  ): Promise<UserProfile> {
    return this.dealerAssessmentAssignmentRepository.dealer(id);
  }
}
