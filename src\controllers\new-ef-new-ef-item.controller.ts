import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,HttpErrors
} from '@loopback/rest';
import {
  NewEf,
  NewEfItem,
} from '../models';
import {NewEfItemRepository, NewEfRepository} from '../repositories';
import {authenticate} from '@loopback/authentication';



export class NewEfNewEfItemController {
  constructor(
    @repository(NewEfRepository) protected newEfRepository: NewEfRepository,
    @repository(NewEfItemRepository) protected newEfItemRepository: NewEfItemRepository,

  ) { }

  @get('/new-efs/{id}/new-ef-items', {
    responses: {
      '200': {
        description: 'Array of NewEf has many NewEfItem',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfItem)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEfItem>,
  ): Promise<NewEfItem[]> {
    return this.newEfRepository.newEfItems(id).find(filter);
  }

  @post('/new-efs/{id}/new-ef-items', {
    responses: {
      '200': {
        description: 'NewEf model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEfItem)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEf.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfItem, {
            title: 'NewNewEfItemInNewEf',
            exclude: ['id'],
            optional: ['newEfId']
          }),
        },
      },
    }) newEfItem: Omit<NewEfItem, 'id'>,
  ): Promise<NewEfItem> {
    const existingObject = await this.newEfItemRepository.findOne({
      where: {
        and: [
          {ef_id: newEfItem.ef_id},
          {newEfId: id},
        ],
      },
    });

    if (existingObject) {

      throw new HttpErrors.BadRequest(`Combination ef_id ${newEfItem.ef_id} and newEfId ${id} already exists`);

    } else {
      return this.newEfRepository.newEfItems(id).create(newEfItem)

    }

  }

  // @patch('/new-efs/{id}/new-ef-items', {
  //   responses: {
  //     '200': {
  //       description: 'NewEf.NewEfItem PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfItem, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfItem: Partial<NewEfItem>,
  //   @param.query.object('where', getWhereSchemaFor(NewEfItem)) where?: Where<NewEfItem>,
  // ): Promise<Count> {
  //   return this.newEfRepository.newEfItems(id).patch(newEfItem, where);
  // }

  // @del('/new-efs/{id}/new-ef-items', {
  //   responses: {
  //     '200': {
  //       description: 'NewEf.NewEfItem DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewEfItem)) where?: Where<NewEfItem>,
  // ): Promise<Count> {
  //   return this.newEfRepository.newEfItems(id).delete(where);
  // }
}
