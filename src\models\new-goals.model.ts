import {Entity, model, property, hasMany} from '@loopback/repository';
import {NewTargets} from './new-targets.model';
import {NewInitiatives} from './new-initiatives.model';
import {NewTargetsTwo} from './new-targets-two.model';
import {NewIndicatorTwo} from './new-indicator-two.model';

@model({settings: {strict: false}})
export class NewGoals extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'number',
  })
  category?: number;

  @property({
    type: 'number',
  })
  alignment?: number;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  boundary?: any[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  sdg?: number[];

  @property({
    type: 'array',
    itemType: 'string',
  })
  alignment_tag?: string[];

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @hasMany(() => NewTargets)
  newTargets: NewTargets[];

  @hasMany(() => NewInitiatives)
  newInitiatives: NewInitiatives[];

  @hasMany(() => NewTargetsTwo)
  newTargetsTwos: NewTargetsTwo[];

  @hasMany(() => NewIndicatorTwo)
  newIndicatorTwos: NewIndicatorTwo[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<NewGoals>) {
    super(data);
  }
}

export interface NewGoalsRelations {
  // describe navigational properties here
}

export type NewGoalsWithRelations = NewGoals & NewGoalsRelations;
