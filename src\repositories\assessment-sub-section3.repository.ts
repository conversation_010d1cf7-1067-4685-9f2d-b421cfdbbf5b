import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, Options, repository, BelongsToAccessor} from '@loopback/repository';
import {v4 as uuidv4} from 'uuid';
import {MysqlDataSource} from '../datasources';
import {AssessmentSubSection3, AssessmentSubSection3Relations, ConsolidateFormCollection} from '../models';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';

export class AssessmentSubSection3Repository extends DefaultCrudRepository<
  AssessmentSubSection3,
  typeof AssessmentSubSection3.prototype.id,
  AssessmentSubSection3Relations
> {

  public readonly srf: BelongsToAccessor<ConsolidateFormCollection, typeof AssessmentSubSection3.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>,
  ) {
    super(AssessmentSubSection3, dataSource);
    this.srf = this.createBelongsToAccessorFor('srf', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('srf', this.srf.inclusionResolver);
  }
  async create(entity: Partial<AssessmentSubSection3>, options?: Options): Promise<AssessmentSubSection3> {

    entity.id = uuidv4(); // Generate UUID before saving if `id` is missing

    return super.create(entity, options);
  }
}
