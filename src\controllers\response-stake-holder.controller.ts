import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  Response,
  StakeHolder,
} from '../models';
import {ResponseRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ResponseStakeHolderController {
  constructor(
    @repository(ResponseRepository)
    public responseRepository: ResponseRepository,
  ) { }

  @get('/responses/{id}/stake-holder', {
    responses: {
      '200': {
        description: 'StakeHolder belonging to Response',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(StakeHolder)},
          },
        },
      },
    },
  })
  async getStakeHolder(
    @param.path.number('id') id: typeof Response.prototype.id,
  ): Promise<StakeHolder> {
    return this.responseRepository.stakeHolder(id);
  }
}
