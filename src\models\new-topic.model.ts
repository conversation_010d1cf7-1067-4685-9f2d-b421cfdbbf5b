import {Entity, hasMany, model, property} from '@loopback/repository';
import {NewMetric} from './new-metric.model';

@model({settings: {strict: false}})
export class NewTopic extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'number',

  })
  extra?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  newCategoryId?: number;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'number',
  })
  cloneTopicId?: number;

  @property({
    type: 'any',
  })
  tag?: any;

  @hasMany(() => NewMetric)
  newMetrics: NewMetric[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<NewTopic>) {
    super(data);
  }
}

export interface NewTopicRelations {
  // describe navigational properties here
}

export type NewTopicWithRelations = NewTopic & NewTopicRelations;
