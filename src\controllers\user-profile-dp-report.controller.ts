import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  DpReport,
} from '../models';
import {UserProfileRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';





export class UserProfileDpReportController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/dp-reports', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many DpReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DpReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<DpReport>,
  ): Promise<DpReport[]> {
    return this.userProfileRepository.dpReports(id).find(filter);
  }

  @post('/user-profiles/{id}/dp-reports', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(DpReport)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DpReport, {
            title: 'NewDpReportInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) dpReport: Omit<DpReport, 'id'>,
  ): Promise<DpReport> {
    return this.userProfileRepository.dpReports(id).create(dpReport);
  }

  // @patch('/user-profiles/{id}/dp-reports', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.DpReport PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(DpReport, {partial: true}),
  //       },
  //     },
  //   })
  //   dpReport: Partial<DpReport>,
  //   @param.query.object('where', getWhereSchemaFor(DpReport)) where?: Where<DpReport>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.dpReports(id).patch(dpReport, where);
  // }

  // @del('/user-profiles/{id}/dp-reports', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile.DpReport DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(DpReport)) where?: Where<DpReport>,
  // ): Promise<Count> {
  //   return this.userProfileRepository.dpReports(id).delete(where);
  // }

}
