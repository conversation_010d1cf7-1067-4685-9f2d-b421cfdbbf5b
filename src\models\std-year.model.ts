import {Entity, model, property, hasMany} from '@loopback/repository';
import {StdName} from './std-name.model';

@model()
export class StdYear extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'number',
  })
  stdCountryId?: number;

  @hasMany(() => StdName)
  stdNames: StdName[];

  constructor(data?: Partial<StdYear>) {
    super(data);
  }
}

export interface StdYearRelations {
  // describe navigational properties here
}

export type StdYearWithRelations = StdYear & StdYearRelations;
