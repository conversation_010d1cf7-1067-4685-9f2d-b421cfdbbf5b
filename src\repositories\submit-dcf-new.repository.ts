import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SubmitDcfNew, SubmitDcfNewRelations} from '../models';

export class SubmitDcfNewRepository extends DefaultCrudRepository<
  SubmitDcfNew,
  typeof SubmitDcfNew.prototype.id,
  SubmitDcfNewRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SubmitDcfNew, dataSource);
  }
}
