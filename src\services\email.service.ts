import {injectable, /* inject, */ BindingScope, Provider} from '@loopback/core';

/*
 * Fix the service type. Possible options can be:
 * - import {Email} from 'your-module';
 * - export type Email = string;
 * - export interface Email {}
 */
export type Email = unknown;

@injectable({scope: BindingScope.TRANSIENT})
export class EmailProvider implements Provider<Email> {
  constructor(/* Add @inject to inject parameters */) {}

  value() {
    // Add your implementation here
    throw new Error('To be implemented');
  }
}
