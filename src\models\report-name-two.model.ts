import { Entity, model, property } from '@loopback/repository';

@model()
export class ReportNameTwo extends Entity {

  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;

  @property({
    type: 'string',
  })
  data1?: string;

  @property({
    type: 'string',
  })
  data2?: string;

  @property({
    type: 'string',
  })
  extra?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  reportNameOneId?: number;

  constructor(data?: Partial<ReportNameTwo>) {
    super(data);
  }
}

export interface ReportNameTwoRelations {
  // describe navigational properties here
}

export type ReportNameTwoWithRelations = ReportNameTwo & ReportNameTwoRelations;
