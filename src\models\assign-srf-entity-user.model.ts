import {belongsTo, Entity, model, property} from '@loopback/repository';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';

@model()
export class AssignSrfEntityUser extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;


  @property({
    type: 'array',
    itemType: 'number',
  })
  reporter_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  reviewer_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  approver_ids?: number[];

  @property({
    type: 'string',
  })
  start_date?: string;

  @property({
    type: 'string',
  })
  end_date?: string;

  @property({
    type: 'number',
  })
  frequency?: number;

  @property({
    type: 'number',
  })
  dealerType?: number;


  @property({
    type: 'number',
  })
  level?: number;

  @property({
    type: 'number',
  })
  locationId?: number;
  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'any',
  })
  comments?: any;

  @property({
    type: 'number',
  })
  type?: number;
  @property({
    type: 'number',
  })
  entityAssId?: number;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier0_id?: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier1_id?: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier2_id?: number | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier3_id?: number | null;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => ConsolidateFormCollection)
  srfId: number;

  constructor(data?: Partial<AssignSrfEntityUser>) {
    super(data);
  }
}

export interface AssignSrfEntityUserRelations {
  // describe navigational properties here
}

export type AssignSrfEntityUserWithRelations = AssignSrfEntityUser & AssignSrfEntityUserRelations;
