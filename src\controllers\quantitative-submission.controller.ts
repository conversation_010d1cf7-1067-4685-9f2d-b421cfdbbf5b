import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  Request,
  requestBody,
  response,
  RestBindings,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {QuantitativeSubmission} from '../models';
import {FormCollectionRepository, LocationOneRepository, LocationThreeRepository, LocationTwoRepository, NewDataPointRepository, QuantitativeSubmissionRepository, StructuredResponseRepository, UserProfileRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class QuantitativeSubmissionController {
  constructor(
    @repository(QuantitativeSubmissionRepository)
    public quantitativeSubmissionRepository: QuantitativeSubmissionRepository,
    @repository(StructuredResponseRepository)
    public structuredResponseRepository: StructuredResponseRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(NewDataPointRepository)
    public newDataPointRepository: NewDataPointRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @post('/quantitative-submissions')
  @response(200, {
    description: 'QuantitativeSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(QuantitativeSubmission)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeSubmission, {
            title: 'NewQuantitativeSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    quantitativeSubmission: Omit<QuantitativeSubmission, 'id'>,
  ): Promise<QuantitativeSubmission> {
    return this.quantitativeSubmissionRepository.create(quantitativeSubmission);
  }

  @get('/quantitative-submissions/count')
  @response(200, {
    description: 'QuantitativeSubmission model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QuantitativeSubmission) where?: Where<QuantitativeSubmission>,
  ): Promise<Count> {
    return this.quantitativeSubmissionRepository.count(where);
  }

  @get('/quantitative-submissions')
  @response(200, {
    description: 'Array of QuantitativeSubmission model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QuantitativeSubmission, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QuantitativeSubmission) filter?: Filter<QuantitativeSubmission>,
  ): Promise<QuantitativeSubmission[]> {
    return this.quantitativeSubmissionRepository.find(filter);
  }

  @patch('/quantitative-submissions-once')
  @response(200, {
    description: 'QuantitativeSubmission PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeSubmission, {partial: true}),
        },
      },
    })
    quantitativeSubmission: QuantitativeSubmission,
    @param.where(QuantitativeSubmission) where?: Where<QuantitativeSubmission>,
  ): Promise<Count> {
    if (where) {
      return this.quantitativeSubmissionRepository.updateAll(quantitativeSubmission, where);
    } else {
      return {count: 0}
    }

  }

  @get('/quantitative-submissions-custom/{id}')
  @response(200, {
    description: 'QuantitativeSubmission model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QuantitativeSubmission, {includeRelations: true}),
      },
    },
  })
  async customfindById(
    @param.path.number('id') id: number,
    @param.filter(QuantitativeSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<QuantitativeSubmission>
  ): Promise<any> {
    const found = await this.quantitativeSubmissionRepository.findById(id, filter)
    const result = {...found}
    let dcf = await this.formCollectionRepository.findById(result.dcfId)
    if (result.response && result.response.length && dcf) {
      if (result.response.some(x => x.type === 'number')) {
        let updatedForm: any[] = this.updateFormFields(result.response, dcf?.data1 || [])
        let requireDps = updatedForm.filter((i: any) => i.name !== undefined && i.name !== null).map((i: any) => i.name)

        const datapoint = await this.newDataPointRepository.find({where: {suffix: {inq: requireDps}}})
        let unit = datapoint.filter(x => Array.isArray(x.data1) && x.data1.length).map((x: any) => ({dp: x.suffix, unit: x?.data1[0]?.unit}))

        for (const item of updatedForm) {
          item.dpunit = unit.find(x => x.dp === item.name)?.unit || ''
        }


        return {...result, response: updatedForm}
      } else {
        return this.quantitativeSubmissionRepository.findById(id, filter);
      }



    } else {
      return this.quantitativeSubmissionRepository.findById(id, filter);
    }


  }
  @get('/quantitative-submissions/{id}')
  @response(200, {
    description: 'QuantitativeSubmission model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QuantitativeSubmission, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QuantitativeSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<QuantitativeSubmission>
  ): Promise<QuantitativeSubmission> {

    return this.quantitativeSubmissionRepository.findById(id, filter);



  }
  @patch('/quantitative-submissions/{id}')
  @response(204, {
    description: 'QuantitativeSubmission PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeSubmission, {partial: true}),
        },
      },
    })
    quantitativeSubmission: QuantitativeSubmission,
  ): Promise<void> {
    const found = await this.quantitativeSubmissionRepository.findById(id)
    if (found) {
      if (
        quantitativeSubmission.type === 0 &&
        quantitativeSubmission.reject === 1 && found.userProfileId === 289
      ) {
        await this.sendRejectionNotificationEmail(id, quantitativeSubmission);
      }
    }

    await this.quantitativeSubmissionRepository.updateById(id, quantitativeSubmission);
  }

  @put('/quantitative-submissions/{id}')
  @response(204, {
    description: 'QuantitativeSubmission PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() quantitativeSubmission: QuantitativeSubmission,
  ): Promise<void> {
    await this.quantitativeSubmissionRepository.replaceById(id, quantitativeSubmission);
  }

  @del('/quantitative-submissions/{id}')
  @response(204, {
    description: 'QuantitativeSubmission DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.quantitativeSubmissionRepository.deleteById(id);
  }

  @post('/quantitative-submissions-previous-year')
  @response(200, {
    description: 'QuantitativeSubmission Previous Year Submission success',
  })
  async prevYearQuantitativeSubmission(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {type: 'number'},
              year: {type: 'number'},
              formId: {type: 'number'},
              level: {type: 'number'},
              type: {type: 'number'},
              locationId: {type: 'number'},
            },
          },
        },
      },
    })
    data: {
      year: number;
      formId: number;
      level: number;
      type: number;
      locationId: number;
      userProfileId: number;
    },
  ): Promise<any[]> {
    const {year, formId, level, locationId, userProfileId, type} = data;
    let betweenMonths = this.getMonthYearArray(year, type);

    let result_array = await this.quantitativeSubmissionRepository.find({
      where: {userProfileId, dcfId: formId, level, locationId},
    });

    return result_array.filter(i => {
      if (i.reporting_period) {
        return betweenMonths.includes(
          i.reporting_period[i.reporting_period.length - 1],
        );
      }
    });
  }
  @post('/quantitative-submissions-previous-six-months')
  @response(200, {
    description: 'QuantitativeSubmission Previous 6 Months Submission success',
  })
  async prev6MonthQuantitativeSubmission(
    @inject(RestBindings.Http.REQUEST) req: Request,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {type: 'number'},
              rp: {type: 'string'},
              dcfId: {type: 'number'},
              level: {type: 'number'},
              locationId: {type: 'number'},
            },
          },
        },
      },
    })
    data: {
      rp: string;
      dcfId: number;
      level: number;
      locationId: number;
      userProfileId: number;
    },
  ): Promise<any> {
    const {rp, dcfId, level, locationId, userProfileId} = data;
    const timeZone = req.headers['time-zone'] || 'UTC';

    let betweenMonths = this.getPreviousSixMonths(rp);
    let result: any = []
    let result_array = await this.quantitativeSubmissionRepository.find({
      where: {userProfileId, dcfId, level, locationId, type: {neq: 0}}
    });
    let dcf = await this.formCollectionRepository.findById(dcfId)
    let form: any = dcf

    let assignment = await this.userProfileRepository.assignDcfEntityUsers(userProfileId).find({where: {dcfId, level, locationId}})
    if (dcf && dcf.data1 && dcf.data1.length && dcf.data1.filter(i => i.type === 'number').length) {
      let dplist = await this.newDataPointRepository.find({where: {suffix: {inq: dcf.data1.filter(i => i.type === 'number').map(i => i.name)}}})
      form.data1 = dcf.data1.map(i => {
        let dplist_data = dplist.find(x => x.suffix === i.name)
        if (dplist_data) {
          return ({
            dp: dplist_data.title, unit: (dplist_data.data1 && dplist_data.data1.length && dplist_data.data1[0].unit) ? dplist_data.data1[0].unit : '',
            ...i
          })
        } else {
          return ({
            dp: 'Datapoint Not Found', unit: '',
            ...i
          })
        }
      })
    }
    if (assignment.length > 0) {
      const submissionFound = await this.userProfileRepository.quantitativeSubmissions(userProfileId).find({where: {level, locationId, dcfId}})
      const assignmentFound = this.filterAssignmentsByLastSixMonths(assignment, rp, timeZone.toString())
      let result = assignmentFound.map(i => ({...i, data: submissionFound.find(x => x.reporting_period && (this.getRPTextFormat(x.reporting_period) === this.getRPTextFormat(i.reporting_period)))}))

      return {
        assignment: result, dcf: form, submission: result_array.filter(i => {

          return betweenMonths.some(x =>
            i.reporting_period && i.reporting_period.includes(x)
          );

        })
      }
    } else {
      return {
        assignment: [], dcf: form, submission: result_array.filter(i => {

          return betweenMonths.some(x =>
            i.reporting_period && i.reporting_period.includes(x)
          );

        })
      }
    }

  }
  @post('/quantitative-submissions-two-fy')
  @response(200, {
    description: 'Assignment along with reporting period, also submission if any',
  })
  async fyQuantitativeSubmission(
    @inject(RestBindings.Http.REQUEST) req: Request,
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              userProfileId: {type: 'number'},
              fymonth: {type: 'number'},
              year: {type: 'number'},
              dcfId: {type: 'number'},
              level: {type: 'number'},
              locationId: {type: 'number'},
            },
          },
        },
      },
    })
    data: {
      year: number;
      fymonth: number;
      dcfId: number;
      level: number;
      locationId: number;
      userProfileId: number;
    },
  ): Promise<any> {
    const {year, fymonth, dcfId, level, locationId, userProfileId} = data;
    const timeZone = req.headers['time-zone'] || 'UTC';

    let dcf = await this.formCollectionRepository.findById(dcfId)
    let form: any = dcf

    let assignment = await this.userProfileRepository.assignDcfEntityUsers(userProfileId).find({where: {dcfId, level, locationId}})
    if (dcf && dcf.data1 && dcf.data1.length && dcf.data1.filter(i => i.type === 'number').length) {
      let dplist = await this.newDataPointRepository.find({where: {suffix: {inq: dcf.data1.filter(i => i.type === 'number').map(i => i.name)}}})
      form.data1 = dcf.data1.map(i => {
        let dplist_data = dplist.find(x => x.suffix === i.name)
        if (dplist_data) {
          return ({
            dp: dplist_data.title, unit: (dplist_data.data1 && dplist_data.data1.length && dplist_data.data1[0].unit) ? dplist_data.data1[0].unit : '',
            ...i
          })
        } else {
          return ({
            dp: 'Datapoint Not Found', unit: '',
            ...i
          })
        }
      })
    }
    if (assignment.length > 0) {
      const submissionFound = await this.userProfileRepository.quantitativeSubmissions(userProfileId).find({where: {level, locationId, dcfId}})
      const assignmentFound = this.filterAssignmentsWithReportingPeriods(assignment, fymonth, year, timeZone.toString());
      let result = assignmentFound.map((i: any) => ({...i, data: submissionFound.find(x => x.reporting_period && (this.getRPTextFormat(x.reporting_period) === this.getRPTextFormat(i.reporting_period)))}))

      return {
        assignment: result, dcf: form
      }
    } else {
      return {
        assignment: [], dcf: form
      }
    }

  }
  @patch('/quantitative-submissions-custom/{id}')
  @response(204, {
    description: 'QuantitativeSubmission PATCH success',
  })
  async updateByIdCustom(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeSubmission, {partial: true}),
        },
      },
    })
    quantitativeSubmission: QuantitativeSubmission,
  ): Promise<void> {
    const found = await this.quantitativeSubmissionRepository.findById(id)
    if (found) {
      if (
        quantitativeSubmission.type === 0 &&
        quantitativeSubmission.reject === 1 && found.userProfileId === 289
      ) {
        await this.sendRejectionNotificationEmail(id, quantitativeSubmission);
      }
      if (quantitativeSubmission.type != null && [0, 1, 2].includes(quantitativeSubmission.type) && quantitativeSubmission.response2) {

        if (quantitativeSubmission.edit === 1) {

          if (found.edit !== 1) {

            await this.structuredResponseRepository.deleteAll({submitDcfId: id})
            await this.structuredResponseRepository.createAll(quantitativeSubmission.response2.map((y: any) => {
              const {userId, ...rest} = y; // Destructure to exclude userId
              return {
                ...rest,
                created_by: userId,
                created_on: DateTime.utc().toString(),
                reporting_period: found.reporting_period,
              };
            }));
          }
          await this.quantitativeSubmissionRepository.updateById(id, {...quantitativeSubmission, response2: []});

        } else {

          if (found.edit === 1) {

            await this.structuredResponseRepository.deleteAll({submitDcfId: id})
            await this.structuredResponseRepository.createAll(quantitativeSubmission.response2.map((y: any) => {
              const {userId, ...rest} = y; // Destructure to exclude userId
              return {
                ...rest,
                created_by: userId,
                created_on: DateTime.utc().toString(),
                reporting_period: found.reporting_period,
              };
            }));

          } else {

            const existingList = await this.structuredResponseRepository.find({where: {submitDcfId: id}})
            let existingIds: number[] = [];


            for (const item of quantitativeSubmission.response2) {
              if ('maskId' in item && 'uniqueId' in item) {
                (item as any)['reporting_period'] = found.reporting_period

                let index = existingList.find((x: any) => x.maskId === item?.maskId && x.uniqueId === item?.uniqueId && x.dcfId === found.dcfId)
                if (index) {
                  if ('userId' in item) {
                    (item as any)['modified_by'] = item['userId'];
                    delete item['userId'];
                  }
                  existingIds.push(index.id || 0)
                  try {
                    await this.structuredResponseRepository.updateById(index.id, {...item, modified_on: DateTime.utc().toString()})

                  } catch (e) {
                    console.log(e)
                  }
                } else {
                  if ('userId' in item) {
                    (item as any)['created_by'] = item['userId'];
                    delete item['userId'];
                  }
                  await this.structuredResponseRepository.create({...item, created_on: DateTime.utc().toString()})
                }
              }

            }
            const deleteIds = existingList.map(i => i.id).filter(x => x).filter((j: any) => !existingIds.includes(j))
            if (deleteIds.length) {
              await this.structuredResponseRepository.deleteAll({id: {inq: deleteIds}})
            }

          }
          await this.quantitativeSubmissionRepository.updateById(id, {...quantitativeSubmission, response2: []});
        }

      } else {
        await this.quantitativeSubmissionRepository.updateById(id, {...quantitativeSubmission, response2: []});
      }

    } else {
      console.log('as')
      throw new Error('Entry not found');
    }


  }



  private async sendRejectionNotificationEmail(id: number, quantitativeSubmission: QuantitativeSubmission): Promise<void> {
    try {
      // Step 1: Get submission
      const submissionData = await this.quantitativeSubmissionRepository.findById(id);
      console.log("submissionData", submissionData);

      const level = submissionData.level;
      const locationId = submissionData.locationId;
      const adminObj = await this.userProfileRepository.findById(submissionData.userProfileId);


      let entity: any = '';
      if (level === 0) {
        entity = 'Corporate'
      } else if (level === 1) {
        entity = (await this.locationOneRepository.findById(locationId)).name || 'NA'
      } else if (level === 2) {
        entity = (await this.locationTwoRepository.findById(locationId)).name || 'NA'
      } else if (level === 3) {
        entity = (await this.locationThreeRepository.findById(locationId)).name || 'NA'
      }

      console.log("entity", entity);

      const reviewerOrApprover =
        submissionData.type === 2 ? 'Approver' :
          submissionData.type === 1 ? 'Reviewer' :
            ''; // fallback if type is missing or invalid

      if (submissionData && reviewerOrApprover) {
        // Step 2: Use custom function instead of repository
        const assignmentList = await this.userProfileController.getAssignedIndicatorList(
          submissionData.userProfileId ?? 0,
          {where: {id: submissionData.entityUserAssId ?? 0}}
        );
        console.log("assignmentList", assignmentList.length, submissionData.entityUserAssId, submissionData.userProfileId);
        // if (assignmentList && assignmentList.length > 0)
        if ((submissionData.type === 1 || submissionData.type === 2) &&
          assignmentList && assignmentList.length > 0) {
          // dcf.title = formname
          const assignment = assignmentList[0];

          // Step 3: Get reporter and approver profiles
          const reporterProfiles = await this.userProfileController.filteredUP({where: {id: {inq: assignment?.reporter_ids || []}}})
          const reviewerProfiles = await this.userProfileController.filteredUP({where: {id: {inq: assignment?.reviewer_ids || []}}})
          const approverProfiles = await this.userProfileController.filteredUP({where: {id: {inq: assignment?.approver_ids || []}}})

          // Step 4: Log or use the profiles
          console.log('Reporter Profiles:', reporterProfiles.length);
          console.log('Approver Profiles:', approverProfiles.length);
          console.log('Reviewer Profiles:', reviewerProfiles.length);

          const formName = assignment.dcf?.title ?? 'Form Title';

          const reportingPeriod = this.getRPTextFormat([submissionData.reporting_period?.[0]]);

          const reviewerNames = reviewerProfiles.map((p: any) => p.information?.empname).join(', ');
          const reviewerEmails = reviewerProfiles.map((p: any) => p.information?.username).join(', ');

          const reporterNames = reporterProfiles.map((p: any) => p.information?.empname).join(', ');
          const reporterEmails = reporterProfiles.map((p: any) => p.information?.username).join(', ');

          const approverNames = approverProfiles.map((p: any) => p.information?.empname).join(', ');
          const approverEmails = approverProfiles.map((p: any) => p.information?.username).join(', ');



          const subject = `Action Required: ESG Submission Rejected – Please Resubmit the Data`
          const body = ` <p><strong>Dear Reporter,</strong></p>

<p style="margin: 5px 0px;"> This is to inform you that your submitted Sustainability Data Collection Form has been rejected for resubmission by the <strong>${reviewerOrApprover}</strong> during the review process. The submission has now been reopened, and you are requested to resubmit the respective form as per the <strong>${reviewerOrApprover}</strong>'s inputs.
</p>

<table border="1" cellpadding="6" cellspacing="0" style="border-collapse: collapse; font-family: Arial, sans-serif; font-size: 14px;">
  <thead style="background-color: #f0f0f0;">
    <tr>
      <th>Form</th>
      <th>Entity</th>
      <th>Reporting Period</th>
      <th>Reporter</th>
      <th>Reviewer</th>
      <th>Approver</th>
      <th>Status</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>${formName}</td>
      <td>${entity}</td>
      <td>${reportingPeriod}</td>
      <td>${reporterNames}</td>
      <td>${reviewerNames}</td>
      <td>${approverNames}</td>
      <td>Resubmission Required</td>
    </tr>
  </tbody>
</table>

 ${adminObj?.userPortalUrl ? `<p style="margin: 5px 0px;">
     Please log in to the
      <a href=${adminObj?.userPortalUrl} target="_blank">EiSqr – ESG Platform </a> to complete the data submission with supporting documents (as required) before the submission deadline.
    </p>` : ''}

   <p style="margin: 5px 0px;">
     In case of any queries, raise a ticket or alternatively, write to us at <a href="mailto:<EMAIL>"><EMAIL></a>
    </p>
<p style="margin: 5px 0px;">Thank you for your prompt attention to this matter.</p>
<p style="margin: 5px 0px;">This is an automated message. Please do not respond to this mail.</p>`

          try {

            await this.sqsService.sendEmail(reporterEmails, subject, body, [...reviewerEmails, ...approverEmails, "<EMAIL>"]);
            console.log('Rejection notification email sent successfully');
          } catch (error) {
            console.error('Error sending rejection notification email:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error in sendRejectionNotificationEmail:', error);
    }
  }



  calculateFiscalYearRange(fymonth: number, year: number) {
    if (fymonth === 1) {
      // Calendar year if fymonth is January
      return {
        start: DateTime.fromObject({year: year, month: 1, day: 1}),
        end: DateTime.fromObject({year: year, month: 12, day: 31}),
      };
    } else {
      // Fiscal year for other months
      return {
        start: DateTime.fromObject({year: year - 1, month: fymonth, day: 1}),
        end: DateTime.fromObject({year: year, month: fymonth - 1, day: 31}),
      };
    }
  }

  getReportingPeriods(startDate: any, endDate: any, frequency: number, fiscalStart: any, fiscalEnd: any, timeZone: string) {
    let start = DateTime.fromISO(startDate, {zone: 'utc'}).setZone(timeZone).startOf("month");
    const end = endDate ? DateTime.fromISO(endDate, {zone: 'utc'}).setZone(timeZone).endOf("month") : fiscalEnd;

    // Adjust start if it's before the fiscal year start
    if (start < fiscalStart) {
      start = fiscalStart;
    }

    const periods = [];
    while (start <= end && start <= fiscalEnd) {
      // Generate period array according to frequency
      const period = [];
      for (let i = 0; i < frequency; i++) {
        period.push(start.toFormat("MM-yyyy"));
        start = start.plus({months: 1});
        if (start > end || start > fiscalEnd) break;
      }
      if (period.length === frequency) {
        periods.push(period);
      }
    }
    return periods;
  }

  filterAssignmentsWithReportingPeriods(assignments: any = [], fymonth: number, year: number, timeZone: string) {
    // Get the current and previous fiscal year ranges
    const currentFiscalYear = this.calculateFiscalYearRange(fymonth, year);
    const previousFiscalYear = this.calculateFiscalYearRange(fymonth, year - 1);

    const filteredAssignments = assignments
      .filter((assignment: any) => {
        const startDate = DateTime.fromISO(assignment.start_date, {zone: 'utc'}).setZone(timeZone);
        const endDate = assignment.end_date ? DateTime.fromISO(assignment.end_date, {zone: 'utc'}).setZone(timeZone) : currentFiscalYear.end;

        // Check if assignment overlaps with either the current or previous fiscal year
        return (
          (startDate <= currentFiscalYear.end && endDate >= currentFiscalYear.start) ||
          (startDate <= previousFiscalYear.end && endDate >= previousFiscalYear.start)
        );
      })
      .map((assignment: any) => {
        // Generate reporting periods for both fiscal years, if applicable
        const reportingPeriodsCurrentYear = this.getReportingPeriods(
          assignment.start_date,
          assignment.end_date,
          assignment.frequency === 4 ? 12 : assignment.frequency === 5 ? 6 : assignment.frequency,
          currentFiscalYear.start,
          currentFiscalYear.end, timeZone
        );

        const reportingPeriodsPreviousYear = this.getReportingPeriods(
          assignment.start_date,
          assignment.end_date,
          assignment.frequency === 4 ? 12 : assignment.frequency === 5 ? 6 : assignment.frequency,
          previousFiscalYear.start,
          previousFiscalYear.end, timeZone
        );

        let currentFY = reportingPeriodsCurrentYear.map(i => ({...assignment, year, reporting_period: i}))
        let previousFY = reportingPeriodsPreviousYear.map(i => ({...assignment, year: year - 1, reporting_period: i}))
        return [...currentFY, ...previousFY]

      });

    return filteredAssignments.flatMap((i: any) => i).filter((i: any) => i);

  }
  updateFormFields(oldForm: any = [], newForm: any = []) {
    // Helper to find a field by name and type
    const findField = (form: any, field: any) => form.find((f: any) => f.name === field.name && f.type === field.type);

    // Helper to merge fields while keeping old values (excluding paragraph type)
    function mergeFields(oldField: any, newField: any) {
      const mergedField = {...newField};

      // Retain value from old field if present
      if (oldField.value !== undefined) {
        mergedField.value = oldField.value;
      }

      // Handle `values` array for selections
      if (Array.isArray(newField.values)) {
        mergedField.values = newField.values.map((newValue: any) => {
          const oldValue = oldField.values?.find((v: any) => v.value === newValue.value) || {};
          return {
            ...newValue,
            selected: oldValue.selected !== undefined ? oldValue.selected : newValue.selected,
          };
        });
      }

      return mergedField;
    }

    const result: any = [];

    // Iterate over oldForm and compare fields with newForm
    oldForm.forEach((oldField: any, index: any) => {
      const newField = newForm[index];

      // Handle paragraph types with matching indices
      if (oldField.type === "paragraph") {
        if (newField && newField.type === "paragraph" && index === newForm.indexOf(newField)) {
          result.push(newField); // Replace paragraph field only if index matches
        } else {
          result.push(oldField); // Retain old paragraph if index mismatch
        }
      } else {
        const matchingNewField = findField(newForm, oldField);
        if (matchingNewField) {
          result.push(mergeFields(oldField, matchingNewField));
        } else {
          result.push(oldField); // Keep old field if no exact match in new form
        }
      }
    });

    // Add new fields not in oldForm, ignoring paragraph types
    newForm.forEach((newField: any, index: number) => {
      if (!findField(oldForm, newField) && newField.type !== "paragraph") {
        result.push(newField);
      }
    });

    return result;
  }


  getRPTextFormat(item: string[]): string {
    if (item.length !== 0) {
      if (item.length >= 2) {
        const startDate = DateTime.fromFormat(item[0], "MM-yyyy").toFormat(
          "LLL-yyyy"
        );
        const endDate = DateTime.fromFormat(
          item[item.length - 1],
          "MM-yyyy"
        ).toFormat("LLL-yyyy");
        return `${startDate} to ${endDate}`;
      } else {
        return DateTime.fromFormat(item[0], "MM-yyyy").toFormat("LLL-yyyy");
      }
    } else {
      return ''
    }
  }
  getMonthYearArray(year: number, type: number): string[] {
    const months: string[] = [];
    let startDate: DateTime, endDate: DateTime;

    if (type === 1) {
      startDate = DateTime.fromFormat(`01-01-${year}`, 'dd-MM-yyyy');
      endDate = DateTime.fromFormat(`12-01-${year}`, 'MM-dd-yyyy');
    } else if (type === 2) {
      const prevYear = year - 1;
      startDate = DateTime.fromFormat(`01-04-${prevYear}`, 'dd-MM-yyyy');
      endDate = DateTime.fromFormat(`03-01-${year}`, 'MM-dd-yyyy');
    } else {
      throw new Error('Invalid type. Type must be 1 or 2.');
    }

    let current = startDate;

    while (current <= endDate) {
      months.push(current.toFormat('MM-yyyy'));
      current = current.plus({months: 1});
    }

    return months;
  }
  getPreviousSixMonths(dateStr: string) {
    // Extract the starting month and year
    const [startStr] = dateStr.split(" to ");
    const [monthStr, yearStr] = startStr.split("-");

    // Parse the date in Luxon (using 'Jan-2022' format)
    const startDate = DateTime.fromFormat(`${monthStr}-${yearStr}`, "MMM-yyyy");

    // Generate the previous six months
    const previousMonths = [];
    for (let i = 1; i <= 12; i++) {
      // Get the previous month and format as MM-YYYY
      const previousMonth = startDate.minus({months: i});
      previousMonths.unshift(previousMonth.toFormat("MM-yyyy"));
    }

    return previousMonths;
  }
  getMonthsBetween(startDate: any, endDate: any, frequency: number) {
    // Default to the current date if startDate or endDate is null
    const currentDate = DateTime.utc();
    startDate = startDate ? DateTime.fromISO(startDate).startOf('month') : currentDate;
    endDate = endDate ? DateTime.fromISO(endDate) : currentDate;

    const months = [];
    let current = startDate.startOf('month');

    // Loop to add months based on frequency
    while (current < endDate) {
      let nextDate = current.plus({months: frequency - 1}).startOf('month');
      if (current > endDate) {
        nextDate = endDate;
      }

      if (frequency === 1) {
        months.push(this.getMonthArray(current.toFormat('MMM-yyyy'), frequency - 1));
      } else {
        months.push(this.getMonthArray(`${current.toFormat('MMM-yyyy')} to ${nextDate.toFormat('MMM-yyyy')}`, frequency - 1));
      }

      current = nextDate.plus({months: 1}).startOf('month');
    }

    return months;
  }


  getMonthArray(dateStr: string, frequency: number) {
    const [startStr] = dateStr.split(" to ");
    const [monthStr, yearStr] = startStr.split("-");

    // Parse the date in Luxon (using 'Jan-2022' format)
    const startDate = DateTime.fromFormat(`${monthStr}-${yearStr}`, "MMM-yyyy");

    // Generate the previous months based on frequency
    const previousMonths = [];
    for (let i = 0; i <= frequency; i++) {
      const previousMonth = startDate.plus({months: i});
      previousMonths.push(previousMonth.toFormat("MM-yyyy"));
    }

    return previousMonths;
  }


  filterAssignmentsByLastSixMonths(assignments: any[], dateStr: string, timeZone: string) {
    const currentDate = DateTime.fromFormat(dateStr, "MMM-yyyy", {zone: timeZone}).startOf('month') // Start from the provided date
    const sixMonthsAgo = currentDate.minus({months: 12}).startOf('month'); // Get the date six months ago

    return assignments.map(assignment => {
      // Calculate periods for each assignment
      const periods = this.getMonthsBetween(assignment.start_date, assignment.end_date, assignment.frequency === 4 ? 12 : assignment.frequency === 5 ? 6 : assignment.frequency);

      // Filter periods that are within the last six months
      const filteredPeriods = periods.filter(period => {
        // Check if the period falls within the last six months from the current date
        const periodStart = DateTime.fromFormat(period[0], "MM-yyyy", {zone: timeZone});
        const endStart = DateTime.fromFormat(period[period.length - 1], "MM-yyyy", {zone: timeZone});


        return periodStart >= sixMonthsAgo && endStart < currentDate;
      });

      // Return the assignment with filtered periods
      return filteredPeriods.map(i => ({...assignment, reporting_period: i}))

    }).reduce((a, b) => {return [...a, ...b]}, [])


  }



}
