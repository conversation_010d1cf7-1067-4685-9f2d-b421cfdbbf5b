import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Survey,
  Response,
} from '../models';
import {SurveyRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SurveyResponseController {
  constructor(
    @repository(SurveyRepository) protected surveyRepository: SurveyRepository,
  ) { }

  @get('/surveys/{id}/responses', {
    responses: {
      '200': {
        description: 'Array of Survey has many Response',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(Response)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<Response>,
  ): Promise<Response[]> {
    return this.surveyRepository.responses(id).find(filter);
  }

  @post('/surveys/{id}/responses', {
    responses: {
      '200': {
        description: 'Survey model instance',
        content: {'application/json': {schema: getModelSchemaRef(Response)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Survey.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Response, {
            title: 'NewResponseInSurvey',
            exclude: ['id'],
            optional: ['surveyId']
          }),
        },
      },
    }) response: Omit<Response, 'id'>,
  ): Promise<Response> {
    return this.surveyRepository.responses(id).create(response);
  }

  @patch('/surveys/{id}/responses', {
    responses: {
      '200': {
        description: 'Survey.Response PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Response, {partial: true}),
        },
      },
    })
    response: Partial<Response>,
    @param.query.object('where', getWhereSchemaFor(Response)) where?: Where<Response>,
  ): Promise<Count> {
    return this.surveyRepository.responses(id).patch(response, where);
  }

  @del('/surveys/{id}/responses', {
    responses: {
      '200': {
        description: 'Survey.Response DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(Response)) where?: Where<Response>,
  ): Promise<Count> {
    return this.surveyRepository.responses(id).delete(where);
  }
}
