import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewIndicator, NewIndicatorRelations} from '../models';

export class NewIndicatorRepository extends DefaultCrudRepository<
  NewIndicator,
  typeof NewIndicator.prototype.id,
  NewIndicatorRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(NewIndicator, dataSource);
  }
}
