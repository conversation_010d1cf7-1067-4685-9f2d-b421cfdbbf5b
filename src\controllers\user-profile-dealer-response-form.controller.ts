import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  DealerResponseForm,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileDealerResponseFormController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/dealer-response-forms', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many DealerResponseForm',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(DealerResponseForm)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<DealerResponseForm>,
  ): Promise<DealerResponseForm[]> {
    return this.userProfileRepository.dealerResponseForms(id).find(filter);
  }

  @post('/user-profiles/{id}/dealer-response-forms', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerResponseForm)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerResponseForm, {
            title: 'NewDealerResponseFormInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) dealerResponseForm: Omit<DealerResponseForm, 'id'>,
  ): Promise<DealerResponseForm> {
    return this.userProfileRepository.dealerResponseForms(id).create(dealerResponseForm);
  }

  @patch('/user-profiles/{id}/dealer-response-forms', {
    responses: {
      '200': {
        description: 'UserProfile.DealerResponseForm PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerResponseForm, {partial: true}),
        },
      },
    })
    dealerResponseForm: Partial<DealerResponseForm>,
    @param.query.object('where', getWhereSchemaFor(DealerResponseForm)) where?: Where<DealerResponseForm>,
  ): Promise<Count> {
    return this.userProfileRepository.dealerResponseForms(id).patch(dealerResponseForm, where);
  }

  @del('/user-profiles/{id}/dealer-response-forms', {
    responses: {
      '200': {
        description: 'UserProfile.DealerResponseForm DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(DealerResponseForm)) where?: Where<DealerResponseForm>,
  ): Promise<Count> {
    return this.userProfileRepository.dealerResponseForms(id).delete(where);
  }
}
