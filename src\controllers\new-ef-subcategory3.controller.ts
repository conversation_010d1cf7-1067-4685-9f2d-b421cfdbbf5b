import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewEfSubcategory3} from '../models';
import {NewEfSubcategory3Repository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfSubcategory3Controller {
  constructor(
    @repository(NewEfSubcategory3Repository)
    public newEfSubcategory3Repository : NewEfSubcategory3Repository,
  ) {}

  @post('/new-ef-subcategory3s')
  @response(200, {
    description: 'NewEfSubcategory3 model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewEfSubcategory3)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory3, {
            title: 'NewNewEfSubcategory3',
            exclude: ['id'],
          }),
        },
      },
    })
    newEfSubcategory3: Omit<NewEfSubcategory3, 'id'>,
  ): Promise<NewEfSubcategory3> {
    return this.newEfSubcategory3Repository.create(newEfSubcategory3);
  }

  @get('/new-ef-subcategory3s/count')
  @response(200, {
    description: 'NewEfSubcategory3 model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewEfSubcategory3) where?: Where<NewEfSubcategory3>,
  ): Promise<Count> {
    return this.newEfSubcategory3Repository.count(where);
  }

  @get('/new-ef-subcategory3s')
  @response(200, {
    description: 'Array of NewEfSubcategory3 model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewEfSubcategory3, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewEfSubcategory3) filter?: Filter<NewEfSubcategory3>,
  ): Promise<NewEfSubcategory3[]> {
    return this.newEfSubcategory3Repository.find(filter);
  }

  @patch('/new-ef-subcategory3s')
  @response(200, {
    description: 'NewEfSubcategory3 PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory3, {partial: true}),
        },
      },
    })
    newEfSubcategory3: NewEfSubcategory3,
    @param.where(NewEfSubcategory3) where?: Where<NewEfSubcategory3>,
  ): Promise<Count> {
    return this.newEfSubcategory3Repository.updateAll(newEfSubcategory3, where);
  }

  @get('/new-ef-subcategory3s/{id}')
  @response(200, {
    description: 'NewEfSubcategory3 model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewEfSubcategory3, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewEfSubcategory3, {exclude: 'where'}) filter?: FilterExcludingWhere<NewEfSubcategory3>
  ): Promise<NewEfSubcategory3> {
    return this.newEfSubcategory3Repository.findById(id, filter);
  }

  @patch('/new-ef-subcategory3s/{id}')
  @response(204, {
    description: 'NewEfSubcategory3 PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory3, {partial: true}),
        },
      },
    })
    newEfSubcategory3: NewEfSubcategory3,
  ): Promise<void> {
    await this.newEfSubcategory3Repository.updateById(id, newEfSubcategory3);
  }

  @put('/new-ef-subcategory3s/{id}')
  @response(204, {
    description: 'NewEfSubcategory3 PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newEfSubcategory3: NewEfSubcategory3,
  ): Promise<void> {
    await this.newEfSubcategory3Repository.replaceById(id, newEfSubcategory3);
  }

  @del('/new-ef-subcategory3s/{id}')
  @response(204, {
    description: 'NewEfSubcategory3 DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newEfSubcategory3Repository.deleteById(id);
  }
}
