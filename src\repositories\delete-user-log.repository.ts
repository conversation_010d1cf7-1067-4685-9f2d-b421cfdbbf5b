import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DeleteUserLog, DeleteUserLogRelations} from '../models';

export class DeleteUserLogRepository extends DefaultCrudRepository<
  DeleteUserLog,
  typeof DeleteUserLog.prototype.id,
  DeleteUserLogRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(DeleteUserLog, dataSource);
  }
}
