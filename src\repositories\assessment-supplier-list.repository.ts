import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssessmentSupplierList, AssessmentSupplierListRelations} from '../models';

export class AssessmentSupplierListRepository extends DefaultCrudRepository<
  AssessmentSupplierList,
  typeof AssessmentSupplierList.prototype.id,
  AssessmentSupplierListRelations
> {



  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource
  ) {
    super(AssessmentSupplierList, dataSource);

  }
}
