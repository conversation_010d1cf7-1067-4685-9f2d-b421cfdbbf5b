import {Entity, model, property} from '@loopback/repository';

@model()
export class AssignRfEntity extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'array',
    itemType: 'number',
  })
  other_ids?: number[];
  @property({
    type: 'array',
    itemType: 'number',
  })
  city_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  country_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  site_ids?: number[];

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'number',
  })
  rfid?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<AssignRfEntity>) {
    super(data);
  }
}

export interface AssignRfEntityRelations {
  // describe navigational properties here
}

export type AssignRfEntityWithRelations = AssignRfEntity & AssignRfEntityRelations;
