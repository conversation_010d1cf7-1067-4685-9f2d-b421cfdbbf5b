import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {CertificationLevel, CertificationLevelRelations} from '../models';

export class CertificationLevelRepository extends DefaultCrudRepository<
  CertificationLevel,
  typeof CertificationLevel.prototype.id,
  CertificationLevelRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(CertificationLevel, dataSource);
  }
}
