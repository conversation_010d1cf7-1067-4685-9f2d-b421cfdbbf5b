import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AuditorAssignmentSubmission,
  SupplierAssessmentAssignment,
} from '../models';
import {SupplierAssessmentAssignmentRepository} from '../repositories';

export class SupplierAssessmentAssignmentAuditorAssignmentSubmissionController {
  constructor(
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
  ) { }



  @get('/supplier-assessment-assignments/{id}/auditor-assignment-submission', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment has one AuditorAssignmentSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(AuditorAssignmentSubmission),
          },
        },
      },
    },
  })
  async get(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<AuditorAssignmentSubmission>,
  ): Promise<AuditorAssignmentSubmission> {
    return this.supplierAssessmentAssignmentRepository.auditorAssignmentSubmission(id).get(filter);
  }

  @post('/supplier-assessment-assignments/{id}/auditor-assignment-submission', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(AuditorAssignmentSubmission)}},
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof SupplierAssessmentAssignment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditorAssignmentSubmission, {
            title: 'NewAuditorAssignmentSubmissionInSupplierAssessmentAssignment',
            exclude: ['id'],
            optional: ['supplierAssessmentAssignmentId']
          }),
        },
      },
    }) AuditorAssignmentSubmission: Omit<AuditorAssignmentSubmission, 'id'>,
  ): Promise<AuditorAssignmentSubmission> {
    return this.supplierAssessmentAssignmentRepository.auditorAssignmentSubmission(id).create(AuditorAssignmentSubmission);
  }

  @patch('/supplier-assessment-assignments/{id}/auditor-assignment-submission', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment.auditorAssignmentSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditorAssignmentSubmission, {partial: true}),
        },
      },
    })
    AuditorAssignmentSubmission: Partial<AuditorAssignmentSubmission>,
    @param.query.object('where', getWhereSchemaFor(AuditorAssignmentSubmission)) where?: Where<AuditorAssignmentSubmission>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.auditorAssignmentSubmission(id).patch(AuditorAssignmentSubmission, where);
  }

  @del('/supplier-assessment-assignments/{id}/auditor-assignment-submission', {
    responses: {
      '200': {
        description: 'SupplierAssessmentAssignment.auditorAssignmentSubmission DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(AuditorAssignmentSubmission)) where?: Where<AuditorAssignmentSubmission>,
  ): Promise<Count> {
    return this.supplierAssessmentAssignmentRepository.auditorAssignmentSubmission(id).delete(where);
  }
}
