import {Entity, model, property} from '@loopback/repository';

@model()
export class SapFuel extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;


  @property({
    type: 'string',
  })
  Location?: string;


  @property({
    type: 'string',
  })
  Date?: string;

  @property({
    type: 'string',
  })
  DPA0336?: string; // Stores the mapped value of Qty

  @property({
    type: 'string',
  })
  DPA0132?: string; // Stores the mapped value of UOM

  @property({
    type: 'string',
  })
  DPA0131?: string; // Stores the mapped value of Fuel

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier0_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier1_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier2_id?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  tier3_id?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  fetched_on?: string | null;

  @property({
    type: 'number',
  })
  level?: number; // Stores the mapped value of Qty

  @property({
    type: 'number',
  })
  locationId?: number; // Stores the mapped value of Qty

  @property({
    type: 'number',
  })
  userProfileId?: number; // Stores the mapped value of Qty

  constructor(data?: Partial<SapFuel>) {
    super(data);
  }
}

export interface SapFuelRelations {
  // describe navigational properties here
}

export type SapFuelWithRelations = SapFuel & SapFuelRelations;
