import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssessmentSubSection3,
  ConsolidateFormCollection,
} from '../models';
import {AssessmentSubSection3Repository} from '../repositories';

export class AssessmentSubSection3ConsolidateFormCollectionController {
  constructor(
    @repository(AssessmentSubSection3Repository)
    public assessmentSubSection3Repository: AssessmentSubSection3Repository,
  ) { }

  @get('/assessment-sub-section3s/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to AssessmentSubSection3',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ConsolidateFormCollection),
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.string('id') id: typeof AssessmentSubSection3.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.assessmentSubSection3Repository.srf(id);
  }
}
