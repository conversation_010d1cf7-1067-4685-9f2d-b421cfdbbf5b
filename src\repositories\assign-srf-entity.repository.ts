import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignSrfEntity, AssignSrfEntityRelations, ConsolidateFormCollection} from '../models';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';

export class AssignSrfEntityRepository extends DefaultCrudRepository<
  AssignSrfEntity,
  typeof AssignSrfEntity.prototype.id,
  AssignSrfEntityRelations
> {

  public readonly srf: BelongsToAccessor<ConsolidateFormCollection, typeof AssignSrfEntity.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>,
  ) {
    super(AssignSrfEntity, dataSource);
    this.srf = this.createBelongsToAccessorFor('srf', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('srf', this.srf.inclusionResolver);
  }
}
