import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SurveyTitle, SurveyTitleRelations, Survey} from '../models';
import {SurveyRepository} from './survey.repository';

export class SurveyTitleRepository extends DefaultCrudRepository<
  SurveyTitle,
  typeof SurveyTitle.prototype.id,
  SurveyTitleRelations
> {

  public readonly surveys: HasManyRepositoryFactory<Survey, typeof SurveyTitle.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('SurveyRepository') protected surveyRepositoryGetter: Getter<SurveyRepository>,
  ) {
    super(SurveyTitle, dataSource);
    this.surveys = this.createHasManyRepositoryFactoryFor('surveys', surveyRepositoryGetter,);
    this.registerInclusionResolver('surveys', this.surveys.inclusionResolver);
  }
}
