import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class StakeHolder extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  categories?: string;

  @property({
    type: 'string',
  })
  email?: string;

  @property({
    type: 'string',
  })
  company?: string;

  @property({
    type: 'string',
  })
  stakeholder?: string;

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<StakeHolder>) {
    super(data);
  }
}

export interface StakeHolderRelations {
  // describe navigational properties here
}

export type StakeHolderWithRelations = StakeHolder & StakeHolderRelations;
