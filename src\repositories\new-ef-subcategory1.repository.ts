import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEfSubcategory1, NewEfSubcategory1Relations, NewEfSubcategory2} from '../models';
import {NewEfSubcategory2Repository} from './new-ef-subcategory2.repository';

export class NewEfSubcategory1Repository extends DefaultCrudRepository<
  NewEfSubcategory1,
  typeof NewEfSubcategory1.prototype.id,
  NewEfSubcategory1Relations
> {

  public readonly newEfSubcategory2s: HasManyRepositoryFactory<NewEfSubcategory2, typeof NewEfSubcategory1.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfSubcategory2Repository') protected newEfSubcategory2RepositoryGetter: Getter<NewEfSubcategory2Repository>,
  ) {
    super(NewEfSubcategory1, dataSource);
    this.newEfSubcategory2s = this.createHasManyRepositoryFactoryFor('newEfSubcategory2s', newEfSubcategory2RepositoryGetter,);
    this.registerInclusionResolver('newEfSubcategory2s', this.newEfSubcategory2s.inclusionResolver);
  }
}
