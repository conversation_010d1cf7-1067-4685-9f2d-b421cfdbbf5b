import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SubTopic, SubTopicRelations, Topic} from '../models';
import {TopicRepository} from './topic.repository';

export class SubTopicRepository extends DefaultCrudRepository<
  SubTopic,
  typeof SubTopic.prototype.id,
  SubTopicRelations
> {

  public readonly topic: BelongsToAccessor<Topic, typeof SubTopic.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('TopicRepository') protected topicRepositoryGetter: Getter<TopicRepository>,
  ) {
    super(SubTopic, dataSource);
    this.topic = this.createBelongsToAccessorFor('topic', topicRepositoryGetter,);
    this.registerInclusionResolver('topic', this.topic.inclusionResolver);
  }
}
