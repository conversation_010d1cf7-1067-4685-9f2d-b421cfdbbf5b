import {Entity, model, property} from '@loopback/repository';

@model()
export class IndicatorSection extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'array',
    itemType: 'number',
  })
  metric_ids?: number[];

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<IndicatorSection>) {
    super(data);
  }
}

export interface IndicatorSectionRelations {
  // describe navigational properties here
}

export type IndicatorSectionWithRelations = IndicatorSection & IndicatorSectionRelations;
