import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ReportNameTwo, ReportNameTwoRelations} from '../models';

export class ReportNameTwoRepository extends DefaultCrudRepository<
  ReportNameTwo,
  typeof ReportNameTwo.prototype.id,
  ReportNameTwoRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(ReportNameTwo, dataSource);
  }
}
