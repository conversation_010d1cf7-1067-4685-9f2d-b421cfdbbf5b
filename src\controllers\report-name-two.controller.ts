import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ReportNameTwo} from '../models';
import {ReportNameTwoRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ReportNameTwoController {
  constructor(
    @repository(ReportNameTwoRepository)
    public reportNameTwoRepository : ReportNameTwoRepository,
  ) {}

  @post('/report-name-twos')
  @response(200, {
    description: 'ReportNameTwo model instance',
    content: {'application/json': {schema: getModelSchemaRef(ReportNameTwo)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportNameTwo, {
            title: 'NewReportNameTwo',
            exclude: ['id'],
          }),
        },
      },
    })
    reportNameTwo: Omit<ReportNameTwo, 'id'>,
  ): Promise<ReportNameTwo> {
    return this.reportNameTwoRepository.create(reportNameTwo);
  }

  @get('/report-name-twos/count')
  @response(200, {
    description: 'ReportNameTwo model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ReportNameTwo) where?: Where<ReportNameTwo>,
  ): Promise<Count> {
    return this.reportNameTwoRepository.count(where);
  }

  @get('/report-name-twos')
  @response(200, {
    description: 'Array of ReportNameTwo model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ReportNameTwo, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ReportNameTwo) filter?: Filter<ReportNameTwo>,
  ): Promise<ReportNameTwo[]> {
    return this.reportNameTwoRepository.find(filter);
  }

  // @patch('/report-name-twos')
  // @response(200, {
  //   description: 'ReportNameTwo PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ReportNameTwo, {partial: true}),
  //       },
  //     },
  //   })
  //   reportNameTwo: ReportNameTwo,
  //   @param.where(ReportNameTwo) where?: Where<ReportNameTwo>,
  // ): Promise<Count> {
  //   return this.reportNameTwoRepository.updateAll(reportNameTwo, where);
  // }

  @get('/report-name-twos/{id}')
  @response(200, {
    description: 'ReportNameTwo model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ReportNameTwo, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ReportNameTwo, {exclude: 'where'}) filter?: FilterExcludingWhere<ReportNameTwo>
  ): Promise<ReportNameTwo> {
    return this.reportNameTwoRepository.findById(id, filter);
  }

  @patch('/report-name-twos/{id}')
  @response(204, {
    description: 'ReportNameTwo PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ReportNameTwo, {partial: true}),
        },
      },
    })
    reportNameTwo: ReportNameTwo,
  ): Promise<void> {
    await this.reportNameTwoRepository.updateById(id, reportNameTwo);
  }

  @put('/report-name-twos/{id}')
  @response(204, {
    description: 'ReportNameTwo PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() reportNameTwo: ReportNameTwo,
  ): Promise<void> {
    await this.reportNameTwoRepository.replaceById(id, reportNameTwo);
  }

  @del('/report-name-twos/{id}')
  @response(204, {
    description: 'ReportNameTwo DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.reportNameTwoRepository.deleteById(id);
  }
}
