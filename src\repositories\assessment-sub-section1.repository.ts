import {Getter, inject} from '@loopback/core';
import {DefaultCrudRepository, HasManyRepositoryFactory, Options, repository, BelongsToAccessor} from '@loopback/repository';
import {v4 as uuidv4} from 'uuid';
import {MysqlDataSource} from '../datasources';
import {AssessmentSubSection1, AssessmentSubSection1Relations, AssessmentSubSection2, ConsolidateFormCollection} from '../models';
import {AssessmentSubSection2Repository} from './assessment-sub-section2.repository';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';

export class AssessmentSubSection1Repository extends DefaultCrudRepository<
  AssessmentSubSection1,
  typeof AssessmentSubSection1.prototype.id,
  AssessmentSubSection1Relations
> {

  public readonly assessmentSubSection2s: HasManyRepositoryFactory<AssessmentSubSection2, typeof AssessmentSubSection1.prototype.id>;

  public readonly srf: BelongsToAccessor<ConsolidateFormCollection, typeof AssessmentSubSection1.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('AssessmentSubSection2Repository') protected assessmentSubSection2RepositoryGetter: Getter<AssessmentSubSection2Repository>, @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>,
  ) {
    super(AssessmentSubSection1, dataSource);
    this.srf = this.createBelongsToAccessorFor('srf', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('srf', this.srf.inclusionResolver);
    this.assessmentSubSection2s = this.createHasManyRepositoryFactoryFor('assessmentSubSection2s', assessmentSubSection2RepositoryGetter,);
    this.registerInclusionResolver('assessmentSubSection2s', this.assessmentSubSection2s.inclusionResolver);
  }
  async create(entity: Partial<AssessmentSubSection1>, options?: Options): Promise<AssessmentSubSection1> {

    entity.id = uuidv4(); // Generate UUID before saving if `id` is missing

    return super.create(entity, options);
  }
}
