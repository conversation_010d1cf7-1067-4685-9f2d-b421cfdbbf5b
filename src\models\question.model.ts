import {Entity, model, property, hasMany} from '@loopback/repository';
import {SubQuestion} from './sub-question.model';

@model({settings: {strict: false}})
export class Question extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  value: object;

  @hasMany(() => SubQuestion)
  subQuestions: SubQuestion[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Question>) {
    super(data);
  }
}

export interface QuestionRelations {
  // describe navigational properties here
}

export type QuestionWithRelations = Question & QuestionRelations;
