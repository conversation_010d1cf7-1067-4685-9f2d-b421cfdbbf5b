import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewTopic, NewTopicRelations, NewMetric} from '../models';
import {NewMetricRepository} from './new-metric.repository';

export class NewTopicRepository extends DefaultCrudRepository<
  NewTopic,
  typeof NewTopic.prototype.id,
  NewTopicRelations
> {

  public readonly newMetrics: HasManyRepositoryFactory<NewMetric, typeof NewTopic.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewMetricRepository') protected newMetricRepositoryGetter: Getter<NewMetricRepository>,
  ) {
    super(NewTopic, dataSource);
    this.newMetrics = this.createHasManyRepositoryFactoryFor('newMetrics', newMetricRepositoryGetter,);
    this.registerInclusionResolver('newMetrics', this.newMetrics.inclusionResolver);
  }
}
