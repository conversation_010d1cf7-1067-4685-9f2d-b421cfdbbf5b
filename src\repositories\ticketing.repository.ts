import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {Ticketing, TicketingRelations} from '../models';

export class TicketingRepository extends DefaultCrudRepository<
  Ticketing,
  typeof Ticketing.prototype.id,
  TicketingRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(Ticketing, dataSource);
  }
}
