import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SaveSurvey, SaveSurveyRelations} from '../models';

export class SaveSurveyRepository extends DefaultCrudRepository<
  SaveSurvey,
  typeof SaveSurvey.prototype.id,
  SaveSurveyRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SaveSurvey, dataSource);
  }
}
