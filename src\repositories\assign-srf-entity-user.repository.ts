import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignSrfEntityUser, AssignSrfEntityUserRelations, ConsolidateFormCollection} from '../models';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';

export class AssignSrfEntityUserRepository extends DefaultCrudRepository<
  AssignSrfEntityUser,
  typeof AssignSrfEntityUser.prototype.id,
  AssignSrfEntityUserRelations
> {

  public readonly srf: BelongsToAccessor<ConsolidateFormCollection, typeof AssignSrfEntityUser.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>,
  ) {
    super(AssignSrfEntityUser, dataSource);
    this.srf = this.createBelongsToAccessorFor('srf', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('srf', this.srf.inclusionResolver);
  }
}
