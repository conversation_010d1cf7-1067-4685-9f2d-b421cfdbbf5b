import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {TopLevelComponent} from '../models';
import {TopLevelComponentRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class TopLevelComponentController {
  constructor(
    @repository(TopLevelComponentRepository)
    public topLevelComponentRepository : TopLevelComponentRepository,
  ) {}

  @post('/top-level-components')
  @response(200, {
    description: 'TopLevelComponent model instance',
    content: {'application/json': {schema: getModelSchemaRef(TopLevelComponent)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TopLevelComponent, {
            title: 'NewTopLevelComponent',
            exclude: ['id'],
          }),
        },
      },
    })
    topLevelComponent: Omit<TopLevelComponent, 'id'>,
  ): Promise<TopLevelComponent> {
    return this.topLevelComponentRepository.create(topLevelComponent);
  }

  @get('/top-level-components/count')
  @response(200, {
    description: 'TopLevelComponent model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(TopLevelComponent) where?: Where<TopLevelComponent>,
  ): Promise<Count> {
    return this.topLevelComponentRepository.count(where);
  }

  @get('/top-level-components')
  @response(200, {
    description: 'Array of TopLevelComponent model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(TopLevelComponent, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(TopLevelComponent) filter?: Filter<TopLevelComponent>,
  ): Promise<TopLevelComponent[]> {
    return this.topLevelComponentRepository.find(filter);
  }

  // @patch('/top-level-components')
  // @response(200, {
  //   description: 'TopLevelComponent PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(TopLevelComponent, {partial: true}),
  //       },
  //     },
  //   })
  //   topLevelComponent: TopLevelComponent,
  //   @param.where(TopLevelComponent) where?: Where<TopLevelComponent>,
  // ): Promise<Count> {
  //   return this.topLevelComponentRepository.updateAll(topLevelComponent, where);
  // }

  @get('/top-level-components/{id}')
  @response(200, {
    description: 'TopLevelComponent model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(TopLevelComponent, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(TopLevelComponent, {exclude: 'where'}) filter?: FilterExcludingWhere<TopLevelComponent>
  ): Promise<TopLevelComponent> {
    return this.topLevelComponentRepository.findById(id, filter);
  }

  @patch('/top-level-components/{id}')
  @response(204, {
    description: 'TopLevelComponent PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(TopLevelComponent, {partial: true}),
        },
      },
    })
    topLevelComponent: TopLevelComponent,
  ): Promise<void> {
    await this.topLevelComponentRepository.updateById(id, topLevelComponent);
  }

  @put('/top-level-components/{id}')
  @response(204, {
    description: 'TopLevelComponent PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() topLevelComponent: TopLevelComponent,
  ): Promise<void> {
    await this.topLevelComponentRepository.replaceById(id, topLevelComponent);
  }

  @del('/top-level-components/{id}')
  @response(204, {
    description: 'TopLevelComponent DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.topLevelComponentRepository.deleteById(id);
  }
}
