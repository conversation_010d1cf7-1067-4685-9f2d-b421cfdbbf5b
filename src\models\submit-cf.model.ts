import {Entity, model, property} from '@loopback/repository';

@model()
export class SubmitCf extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  cf?: number;

  @property({
    type: 'number',
  })
  user_type?: number;

  @property({
    type: 'number',
  })
  site?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  reporting_period?: any[];

  @property({
    type: 'array',
    itemType: 'any',
  })
  response?: any[];

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  edit?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  data1?: any[];

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  data2?: string;

  @property({
    type: 'string',
  })
  approved_on?: string;

  @property({
    type: 'string',
  })
  rejected_on?: string;

  @property({
    type: 'number',
  })
  frequencycd?: number;

  @property({
    type: 'number',
  })
  reject?: number;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'number',
  })
  approved_by?: number;

  @property({
    type: 'number',
  })
  reviewed_by?: number;
  @property({
    type: 'any',
  })
  return_remarks?: any;
  @property({
    type: 'string',
  })
  reviewed_on?: string;

  @property({
    type: 'boolean',
  })
  self?: boolean;

  @property({
    type: 'any',
  })
  assignmentId?: any;



  @property({
    type: 'number',
  })
  form_type?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;


  constructor(data?: Partial<SubmitCf>) {
    super(data);
  }
}

export interface SubmitCfRelations {
  // describe navigational properties here
}

export type SubmitCfWithRelations = SubmitCf & SubmitCfRelations;
