import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Certification,
  CertIssueAuthority,
} from '../models';
import {CertificationRepository} from '../repositories';

export class CertificationCertIssueAuthorityController {
  constructor(
    @repository(CertificationRepository) protected certificationRepository: CertificationRepository,
  ) { }

  @get('/certifications/{id}/cert-issue-authorities', {
    responses: {
      '200': {
        description: 'Array of Certification has many CertIssueAuthority',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(CertIssueAuthority)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<CertIssueAuthority>,
  ): Promise<CertIssueAuthority[]> {
    return this.certificationRepository.certIssueAuthorities(id).find(filter);
  }

  @post('/certifications/{id}/cert-issue-authorities', {
    responses: {
      '200': {
        description: 'Certification model instance',
        content: {'application/json': {schema: getModelSchemaRef(CertIssueAuthority)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Certification.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertIssueAuthority, {
            title: 'NewCertIssueAuthorityInCertification',
            exclude: ['id'],
            optional: ['certificationId']
          }),
        },
      },
    }) certIssueAuthority: Omit<CertIssueAuthority, 'id'>,
  ): Promise<CertIssueAuthority> {
    return this.certificationRepository.certIssueAuthorities(id).create(certIssueAuthority);
  }

  @patch('/certifications/{id}/cert-issue-authorities', {
    responses: {
      '200': {
        description: 'Certification.CertIssueAuthority PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertIssueAuthority, {partial: true}),
        },
      },
    })
    certIssueAuthority: Partial<CertIssueAuthority>,
    @param.query.object('where', getWhereSchemaFor(CertIssueAuthority)) where?: Where<CertIssueAuthority>,
  ): Promise<Count> {
    return this.certificationRepository.certIssueAuthorities(id).patch(certIssueAuthority, where);
  }

  @del('/certifications/{id}/cert-issue-authorities', {
    responses: {
      '200': {
        description: 'Certification.CertIssueAuthority DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(CertIssueAuthority)) where?: Where<CertIssueAuthority>,
  ): Promise<Count> {
    return this.certificationRepository.certIssueAuthorities(id).delete(where);
  }
}
