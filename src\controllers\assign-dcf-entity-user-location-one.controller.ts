import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignDcfEntityUser,
  LocationOne,
} from '../models';
import {AssignDcfEntityUserRepository} from '../repositories';

export class AssignDcfEntityUserLocationOneController {
  constructor(
    @repository(AssignDcfEntityUserRepository)
    public assignDcfEntityUserRepository: AssignDcfEntityUserRepository,
  ) { }

  @get('/assign-dcf-entity-users/{id}/location-one', {
    responses: {
      '200': {
        description: 'LocationOne belonging to AssignDcfEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(LocationOne),
          },
        },
      },
    },
  })
  async getLocationOne(
    @param.path.number('id') id: typeof AssignDcfEntityUser.prototype.id,
  ): Promise<LocationOne> {
    return this.assignDcfEntityUserRepository.lone(id);
  }
}
