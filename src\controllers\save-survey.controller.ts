import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SaveSurvey} from '../models';
import {SaveSurveyRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class SaveSurveyController {
  constructor(
    @repository(SaveSurveyRepository)
    public saveSurveyRepository : SaveSurveyRepository,
  ) {}

  @post('/save-surveys')
  @response(200, {
    description: 'SaveSurvey model instance',
    content: {'application/json': {schema: getModelSchemaRef(SaveSurvey)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SaveSurvey, {
            title: 'NewSaveSurvey',
            exclude: ['id'],
          }),
        },
      },
    })
    saveSurvey: Omit<SaveSurvey, 'id'>,
  ): Promise<SaveSurvey> {
    
    return this.saveSurveyRepository.create(saveSurvey);
  }

  @get('/save-surveys/count')
  @response(200, {
    description: 'SaveSurvey model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SaveSurvey) where?: Where<SaveSurvey>,
  ): Promise<Count> {
    return this.saveSurveyRepository.count(where);
  }

  @get('/save-surveys')
  @response(200, {
    description: 'Array of SaveSurvey model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SaveSurvey, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SaveSurvey) filter?: Filter<SaveSurvey>,
  ): Promise<SaveSurvey[]> {
    return this.saveSurveyRepository.find(filter);
  }

  // @patch('/save-surveys')
  // @response(200, {
  //   description: 'SaveSurvey PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SaveSurvey, {partial: true}),
  //       },
  //     },
  //   })
  //   saveSurvey: SaveSurvey,
  //   @param.where(SaveSurvey) where?: Where<SaveSurvey>,
  // ): Promise<Count> {
  //   return this.saveSurveyRepository.updateAll(saveSurvey, where);
  // }

  @get('/save-surveys/{id}')
  @response(200, {
    description: 'SaveSurvey model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SaveSurvey, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SaveSurvey, {exclude: 'where'}) filter?: FilterExcludingWhere<SaveSurvey>
  ): Promise<SaveSurvey> {
    return this.saveSurveyRepository.findById(id, filter);
  }

  @patch('/save-surveys/{id}')
  @response(204, {
    description: 'SaveSurvey PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SaveSurvey, {partial: true}),
        },
      },
    })
    saveSurvey: SaveSurvey,
  ): Promise<void> {
    await this.saveSurveyRepository.updateById(id, saveSurvey);
  }

  @put('/save-surveys/{id}')
  @response(204, {
    description: 'SaveSurvey PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() saveSurvey: SaveSurvey,
  ): Promise<void> {
    await this.saveSurveyRepository.replaceById(id, saveSurvey);
  }

  @del('/save-surveys/{id}')
  @response(204, {
    description: 'SaveSurvey DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.saveSurveyRepository.deleteById(id);
  }
}
