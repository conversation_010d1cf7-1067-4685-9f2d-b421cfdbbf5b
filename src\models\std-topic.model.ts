import {Entity, model, property} from '@loopback/repository';

@model()
export class StdTopic extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'array',
    itemType: 'any',
  })
  data1?: any[];

  @property({
    type: 'any',
  })
  data2?: any;

  @property({
    type: 'number',
  })
  stdScopeId?: number;

  constructor(data?: Partial<StdTopic>) {
    super(data);
  }
}

export interface StdTopicRelations {
  // describe navigational properties here
}

export type StdTopicWithRelations = StdTopic & StdTopicRelations;
