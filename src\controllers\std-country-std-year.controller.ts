import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  StdCountry,
  StdYear,
} from '../models';
import {StdCountryRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class StdCountryStdYearController {
  constructor(
    @repository(StdCountryRepository) protected stdCountryRepository: StdCountryRepository,
  ) { }

  @get('/std-countries/{id}/std-years', {
    responses: {
      '200': {
        description: 'Array of StdCountry has many StdYear',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(StdYear)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<StdYear>,
  ): Promise<StdYear[]> {
    return this.stdCountryRepository.stdYears(id).find(filter);
  }

  @post('/std-countries/{id}/std-years', {
    responses: {
      '200': {
        description: 'StdCountry model instance',
        content: {'application/json': {schema: getModelSchemaRef(StdYear)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof StdCountry.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(StdYear, {
            title: 'NewStdYearInStdCountry',
            exclude: ['id'],
            optional: ['stdCountryId']
          }),
        },
      },
    }) stdYear: Omit<StdYear, 'id'>,
  ): Promise<StdYear> {
    return this.stdCountryRepository.stdYears(id).create(stdYear);
  }

  // @patch('/std-countries/{id}/std-years', {
  //   responses: {
  //     '200': {
  //       description: 'StdCountry.StdYear PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(StdYear, {partial: true}),
  //       },
  //     },
  //   })
  //   stdYear: Partial<StdYear>,
  //   @param.query.object('where', getWhereSchemaFor(StdYear)) where?: Where<StdYear>,
  // ): Promise<Count> {
  //   return this.stdCountryRepository.stdYears(id).patch(stdYear, where);
  // }

  // @del('/std-countries/{id}/std-years', {
  //   responses: {
  //     '200': {
  //       description: 'StdCountry.StdYear DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(StdYear)) where?: Where<StdYear>,
  // ): Promise<Count> {
  //   return this.stdCountryRepository.stdYears(id).delete(where);
  // }
}
