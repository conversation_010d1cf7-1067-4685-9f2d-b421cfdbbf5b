import {DefaultCrudRepository} from '@loopback/repository';
import {SapCollection, SapCollectionRelations} from '../models';
import {MysqlDataSource} from '../datasources';
import {inject} from '@loopback/core';

export class SapCollectionRepository extends DefaultCrudRepository<
  SapCollection,
  typeof SapCollection.prototype.id,
  SapCollectionRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SapCollection, dataSource);
  }
}
