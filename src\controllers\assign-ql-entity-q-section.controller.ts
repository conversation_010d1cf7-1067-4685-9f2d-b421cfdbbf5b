import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignQlEntity,
  QSection,
} from '../models';
import {AssignQlEntityRepository} from '../repositories';

export class AssignQlEntityQSectionController {
  constructor(
    @repository(AssignQlEntityRepository)
    public assignQlEntityRepository: AssignQlEntityRepository,
  ) { }

  @get('/assign-ql-entities/{id}/q-section', {
    responses: {
      '200': {
        description: 'QSection belonging to AssignQlEntity',
        content: {
          'application/json': {
            schema: getModelSchemaRef(QSection),
          },
        },
      },
    },
  })
  async getQSection(
    @param.path.number('id') id: typeof AssignQlEntity.prototype.id,
  ): Promise<QSection> {
    return this.assignQlEntityRepository.qSection(id);
  }
}
