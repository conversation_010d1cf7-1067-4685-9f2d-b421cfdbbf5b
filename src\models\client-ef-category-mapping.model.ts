import {belongsTo, Entity, model, property} from '@loopback/repository';
import {GhgCategory} from './ghg-category.model';
import {GhgSubCategory} from './ghg-sub-category.model';
import {NewEfCategory} from './new-ef-category.model';
import {NewEfStd} from './new-ef-std.model';

@model()
export class ClientEfCategoryMapping extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;


  @property({
    type: 'number',
  })
  tier1_id?: number;

  @property({
    type: 'any',
  })
  tier2_id?: any;

  @property({
    type: 'any',
  })
  tier3_id?: any;

  @property({
    type: 'array',
    itemType: 'string',
  })
  selected_ids?: string[];


  @property({
    type: 'array',
    itemType: 'string',
  })
  checked_ids?: string[];


  @property({
    type: 'array',
    itemType: 'string',
  })
  partial_ids?: string[];


  @property({
    type: 'string',
  })
  created_on?: string;


  @property({
    type: 'string',
  })
  modified_on?: string;


  @property({
    type: 'number',
  })
  created_by?: number;


  @property({
    type: 'number',
  })
  modified_by?: number;

  @belongsTo(() => NewEfStd)
  efStandardId: number;

  @belongsTo(() => NewEfCategory)
  efCategoryId: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => GhgSubCategory)
  efGhgSubCatId: number;

  @belongsTo(() => GhgCategory)
  efGhgCatId: number;

  constructor(data?: Partial<ClientEfCategoryMapping>) {
    super(data);
  }
}

export interface ClientEfCategoryMappingRelations {
  // describe navigational properties here
}

export type ClientEfCategoryMappingWithRelations = ClientEfCategoryMapping & ClientEfCategoryMappingRelations;
