import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {ModuleName, ModuleNameRelations, ScopeName} from '../models';
import {ScopeNameRepository} from './scope-name.repository';

export class ModuleNameRepository extends DefaultCrudRepository<
  ModuleName,
  typeof ModuleName.prototype.id,
  ModuleNameRelations
> {

  public readonly scopeNames: HasManyRepositoryFactory<ScopeName, typeof ModuleName.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('ScopeNameRepository') protected scopeNameRepositoryGetter: Getter<ScopeNameRepository>,
  ) {
    super(ModuleName, dataSource);
    this.scopeNames = this.createHasManyRepositoryFactoryFor('scopeNames', scopeNameRepositoryGetter,);
    this.registerInclusionResolver('scopeNames', this.scopeNames.inclusionResolver);
  }
}
