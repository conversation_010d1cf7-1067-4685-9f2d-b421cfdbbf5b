import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {CertIssueAuthority} from '../models';
import {CertIssueAuthorityRepository} from '../repositories';

export class CertIssueAuthorityController {
  constructor(
    @repository(CertIssueAuthorityRepository)
    public certIssueAuthorityRepository : CertIssueAuthorityRepository,
  ) {}

  @post('/cert-issue-authorities')
  @response(200, {
    description: 'CertIssueAuthority model instance',
    content: {'application/json': {schema: getModelSchemaRef(CertIssueAuthority)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertIssueAuthority, {
            title: 'NewCertIssueAuthority',
            exclude: ['id'],
          }),
        },
      },
    })
    certIssueAuthority: Omit<CertIssueAuthority, 'id'>,
  ): Promise<CertIssueAuthority> {
    return this.certIssueAuthorityRepository.create(certIssueAuthority);
  }

  @get('/cert-issue-authorities/count')
  @response(200, {
    description: 'CertIssueAuthority model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(CertIssueAuthority) where?: Where<CertIssueAuthority>,
  ): Promise<Count> {
    return this.certIssueAuthorityRepository.count(where);
  }

  @get('/cert-issue-authorities')
  @response(200, {
    description: 'Array of CertIssueAuthority model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(CertIssueAuthority, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(CertIssueAuthority) filter?: Filter<CertIssueAuthority>,
  ): Promise<CertIssueAuthority[]> {
    return this.certIssueAuthorityRepository.find(filter);
  }

  @patch('/cert-issue-authorities')
  @response(200, {
    description: 'CertIssueAuthority PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertIssueAuthority, {partial: true}),
        },
      },
    })
    certIssueAuthority: CertIssueAuthority,
    @param.where(CertIssueAuthority) where?: Where<CertIssueAuthority>,
  ): Promise<Count> {
    return this.certIssueAuthorityRepository.updateAll(certIssueAuthority, where);
  }

  @get('/cert-issue-authorities/{id}')
  @response(200, {
    description: 'CertIssueAuthority model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(CertIssueAuthority, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(CertIssueAuthority, {exclude: 'where'}) filter?: FilterExcludingWhere<CertIssueAuthority>
  ): Promise<CertIssueAuthority> {
    return this.certIssueAuthorityRepository.findById(id, filter);
  }

  @patch('/cert-issue-authorities/{id}')
  @response(204, {
    description: 'CertIssueAuthority PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CertIssueAuthority, {partial: true}),
        },
      },
    })
    certIssueAuthority: CertIssueAuthority,
  ): Promise<void> {
    await this.certIssueAuthorityRepository.updateById(id, certIssueAuthority);
  }

  @put('/cert-issue-authorities/{id}')
  @response(204, {
    description: 'CertIssueAuthority PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() certIssueAuthority: CertIssueAuthority,
  ): Promise<void> {
    await this.certIssueAuthorityRepository.replaceById(id, certIssueAuthority);
  }

  @del('/cert-issue-authorities/{id}')
  @response(204, {
    description: 'CertIssueAuthority DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.certIssueAuthorityRepository.deleteById(id);
  }
}
