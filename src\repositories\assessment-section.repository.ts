import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, Options, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {v4 as uuidv4} from 'uuid';
import {MysqlDataSource} from '../datasources';
import {AssessmentSection, AssessmentSectionRelations, AssessmentSubSection1} from '../models';
import {AssessmentSubSection1Repository} from './assessment-sub-section1.repository';

export class AssessmentSectionRepository extends DefaultCrudRepository<
  AssessmentSection,
  typeof AssessmentSection.prototype.id,
  AssessmentSectionRelations
> {

  public readonly assessmentSubSection1s: HasManyRepositoryFactory<AssessmentSubSection1, typeof AssessmentSection.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('AssessmentSubSection1Repository') protected assessmentSubSection1RepositoryGetter: Getter<AssessmentSubSection1Repository>,
  ) {
    super(AssessmentSection, dataSource);
    this.assessmentSubSection1s = this.createHasManyRepositoryFactoryFor('assessmentSubSection1s', assessmentSubSection1RepositoryGetter,);
    this.registerInclusionResolver('assessmentSubSection1s', this.assessmentSubSection1s.inclusionResolver);
  }
  async create(entity: Partial<AssessmentSection>, options?: Options): Promise<AssessmentSection> {

    entity.id = uuidv4(); // Generate UUID before saving if `id` is missing

    return super.create(entity, options);
  }
}
