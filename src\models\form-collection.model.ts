import {Entity, hasMany, model, property} from '@loopback/repository';
import {AssignDcfUser} from './assign-dcf-user.model';

@model({settings: {strict: false}})
export class FormCollection extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  tags?: any;


  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  standardId?: any;
  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  categoryId?: any;
  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  categoryAltId?: any;
  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  subCategoryDpIds?: any;
  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  subCategoryOrder?: any;
  @property({
    type: 'any',

    mysql: {
      dataType: "LONGTEXT"
    }
  })
  calculationDpIds?: any;



  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      nullable: true,
    }
  })
  comments?: string | null;

  @property({
    type: 'number',
  })
  curator_id?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'number',
  })
  modifier_id?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  updated?: string;

  @hasMany(() => AssignDcfUser)
  assignDcfUsers: AssignDcfUser[];


  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<FormCollection>) {
    super(data);
  }
}

export interface FormCollectionRelations {
  // describe navigational properties here
}

export type FormCollectionWithRelations = FormCollection & FormCollectionRelations;
