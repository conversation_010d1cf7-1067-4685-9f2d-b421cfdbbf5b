import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class ScopeTwo extends Entity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'array',
    itemType: 'any',
  })
  data?: any[];

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<ScopeTwo>) {
    super(data);
  }
}

export interface ScopeTwoRelations {
  // describe navigational properties here
}

export type ScopeTwoWithRelations = ScopeTwo & ScopeTwoRelations;
