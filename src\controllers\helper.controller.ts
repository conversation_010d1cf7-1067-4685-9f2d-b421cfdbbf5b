import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
} from '@loopback/rest';
import {Helper} from '../models';
import {HelperRepository} from '../repositories';

export class HelperController {
  constructor(
    @repository(HelperRepository)
    public helperRepository: HelperRepository,
  ) { }

  @post('/helpers', {
    responses: {
      '200': {
        description: 'Helper model instance',
        content: {'application/json': {schema: getModelSchemaRef(Helper)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Helper, {
            title: 'NewHelper',
            exclude: ['id'],
          }),
        },
      },
    })
    helper: Omit<Helper, 'id'>,
  ): Promise<Helper> {
    return this.helperRepository.create(helper);
  }

  @get('/helpers/count', {
    responses: {
      '200': {
        description: 'Helper model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(Helper) where?: Where<Helper>,
  ): Promise<Count> {
    return this.helperRepository.count(where);
  }

  @get('/helpers', {
    responses: {
      '200': {
        description: 'Array of Helper model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Helper, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(Helper) filter?: Filter<Helper>,
  ): Promise<Helper[]> {
    return this.helperRepository.find(filter);
  }

  @patch('/helpers', {
    responses: {
      '200': {
        description: 'Helper PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Helper, {partial: true}),
        },
      },
    })
    helper: Helper,
    @param.where(Helper) where?: Where<Helper>,
  ): Promise<Count> {
    return this.helperRepository.updateAll(helper, where);
  }

  @get('/helpers/{id}', {
    responses: {
      '200': {
        description: 'Helper model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Helper, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(Helper, {exclude: 'where'}) filter?: FilterExcludingWhere<Helper>
  ): Promise<Helper> {
    return this.helperRepository.findById(id, filter);
  }

  @patch('/helpers/{id}', {
    responses: {
      '204': {
        description: 'Helper PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Helper, {partial: true}),
        },
      },
    })
    helper: Helper,
  ): Promise<void> {
    await this.helperRepository.updateById(id, helper);
  }

  @put('/helpers/{id}', {
    responses: {
      '204': {
        description: 'Helper PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() helper: Helper,
  ): Promise<void> {
    await this.helperRepository.replaceById(id, helper);
  }

  @del('/helpers/{id}', {
    responses: {
      '204': {
        description: 'Helper DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.helperRepository.deleteById(id);
  }
}
