import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ManagementQuestion} from '../models';
import {ManagementQuestionRepository} from '../repositories';

export class ManagementQuestionController {
  constructor(
    @repository(ManagementQuestionRepository)
    public managementQuestionRepository : ManagementQuestionRepository,
  ) {}

  @post('/management-questions')
  @response(200, {
    description: 'ManagementQuestion model instance',
    content: {'application/json': {schema: getModelSchemaRef(ManagementQuestion)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ManagementQuestion, {
            title: 'NewManagementQuestion',
            exclude: ['id'],
          }),
        },
      },
    })
    managementQuestion: Omit<ManagementQuestion, 'id'>,
  ): Promise<ManagementQuestion> {
    return this.managementQuestionRepository.create(managementQuestion);
  }

  @get('/management-questions/count')
  @response(200, {
    description: 'ManagementQuestion model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ManagementQuestion) where?: Where<ManagementQuestion>,
  ): Promise<Count> {
    return this.managementQuestionRepository.count(where);
  }

  @get('/management-questions')
  @response(200, {
    description: 'Array of ManagementQuestion model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ManagementQuestion, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ManagementQuestion) filter?: Filter<ManagementQuestion>,
  ): Promise<ManagementQuestion[]> {
    return this.managementQuestionRepository.find(filter);
  }

  @patch('/management-questions')
  @response(200, {
    description: 'ManagementQuestion PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ManagementQuestion, {partial: true}),
        },
      },
    })
    managementQuestion: ManagementQuestion,
    @param.where(ManagementQuestion) where?: Where<ManagementQuestion>,
  ): Promise<Count> {
    return this.managementQuestionRepository.updateAll(managementQuestion, where);
  }

  @get('/management-questions/{id}')
  @response(200, {
    description: 'ManagementQuestion model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ManagementQuestion, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ManagementQuestion, {exclude: 'where'}) filter?: FilterExcludingWhere<ManagementQuestion>
  ): Promise<ManagementQuestion> {
    return this.managementQuestionRepository.findById(id, filter);
  }

  @patch('/management-questions/{id}')
  @response(204, {
    description: 'ManagementQuestion PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ManagementQuestion, {partial: true}),
        },
      },
    })
    managementQuestion: ManagementQuestion,
  ): Promise<void> {
    await this.managementQuestionRepository.updateById(id, managementQuestion);
  }

  @put('/management-questions/{id}')
  @response(204, {
    description: 'ManagementQuestion PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() managementQuestion: ManagementQuestion,
  ): Promise<void> {
    await this.managementQuestionRepository.replaceById(id, managementQuestion);
  }

  @del('/management-questions/{id}')
  @response(204, {
    description: 'ManagementQuestion DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.managementQuestionRepository.deleteById(id);
  }
}
