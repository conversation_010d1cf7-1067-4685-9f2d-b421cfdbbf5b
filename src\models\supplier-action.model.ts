import {belongsTo, Entity, hasMany, model, property} from '@loopback/repository';
import {AssessmentSubSection2} from './assessment-sub-section2.model';
import {SupplierActionHistory} from './supplier-action-history.model';
import {VendorCode} from './vendor-code.model';

@model()
export class SupplierAction extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;
  @property({
    type: 'string',
  })
  finding?: string;


  @property({
    type: 'any',
  })
  actionId?: any;

  @property({
    type: 'any',
  })
  actionData?: any;

  @property({
    type: 'number',
  })
  categoryOfFinding?: number;


  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  nonComplianceType?: number | null;


  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  applicableLaw?: number | null;


  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  otherLaw?: string | null;

  @property({
    type: 'array',
    itemType: 'object',
    jsonSchema: {
      nullable: true,
    }
  })
  auditorAttachments?: object[] | null;

  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  description?: string;
  @property({
    type: 'number',
  })
  priority?: number;



  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  actionDueDate?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  status?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  type?: number | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  reject?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  actionTargetDate?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanSubmittedOn?: string | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanSubmittedBy?: number | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanRejectedOn?: string | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanRejectedBy?: number | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanApprovedOn?: string | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanApprovedBy?: number | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  rootCause?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  actionPlanApproverComments?: string | null;
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  proposedCorrectiveAction?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  auditor_modified_by?: number | null;


  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  auditor_modified_on?: string | null;


  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  supplier_modified_by?: number | null;


  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  supplier_modified_on?: string | null;


  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  approved_on?: string | null;

  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    }
  })
  approved_by?: number | null;


  @property({
    type: 'string',
  })
  created_on?: string;



  @property({
    type: 'number',
  })
  created_by?: number;



  @property({
    type: 'string',
  })
  supplierAssessmentAssignmentId?: string;

  @belongsTo(() => VendorCode)
  vendorCodeId: number;

  @belongsTo(() => AssessmentSubSection2)
  assessmentSubSection2Id: string;

  @hasMany(() => SupplierActionHistory)
  supplierActionHistories: SupplierActionHistory[];

  constructor(data?: Partial<SupplierAction>) {
    super(data);
  }
}

export interface SupplierActionRelations {
  // describe navigational properties here
}

export type SupplierActionWithRelations = SupplierAction & SupplierActionRelations;
