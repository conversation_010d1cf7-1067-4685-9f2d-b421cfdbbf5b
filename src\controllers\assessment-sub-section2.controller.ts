import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssessmentSubSection2} from '../models';
import {AssessmentSubSection2Repository} from '../repositories';

export class AssessmentSubSection2Controller {
  constructor(
    @repository(AssessmentSubSection2Repository)
    public assessmentSubSection2Repository : AssessmentSubSection2Repository,
  ) {}

  @post('/assessment-sub-section2s')
  @response(200, {
    description: 'AssessmentSubSection2 model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssessmentSubSection2)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection2, {
            title: 'NewAssessmentSubSection2',
            exclude: ['id'],
          }),
        },
      },
    })
    assessmentSubSection2: Omit<AssessmentSubSection2, 'id'>,
  ): Promise<AssessmentSubSection2> {
    return this.assessmentSubSection2Repository.create(assessmentSubSection2);
  }

  @get('/assessment-sub-section2s/count')
  @response(200, {
    description: 'AssessmentSubSection2 model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssessmentSubSection2) where?: Where<AssessmentSubSection2>,
  ): Promise<Count> {
    return this.assessmentSubSection2Repository.count(where);
  }

  @get('/assessment-sub-section2s')
  @response(200, {
    description: 'Array of AssessmentSubSection2 model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssessmentSubSection2, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssessmentSubSection2) filter?: Filter<AssessmentSubSection2>,
  ): Promise<AssessmentSubSection2[]> {
    return this.assessmentSubSection2Repository.find(filter);
  }

  @patch('/assessment-sub-section2s')
  @response(200, {
    description: 'AssessmentSubSection2 PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection2, {partial: true}),
        },
      },
    })
    assessmentSubSection2: AssessmentSubSection2,
    @param.where(AssessmentSubSection2) where?: Where<AssessmentSubSection2>,
  ): Promise<Count> {
    return this.assessmentSubSection2Repository.updateAll(assessmentSubSection2, where);
  }

  @get('/assessment-sub-section2s/{id}')
  @response(200, {
    description: 'AssessmentSubSection2 model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssessmentSubSection2, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(AssessmentSubSection2, {exclude: 'where'}) filter?: FilterExcludingWhere<AssessmentSubSection2>
  ): Promise<AssessmentSubSection2> {
    return this.assessmentSubSection2Repository.findById(id, filter);
  }

  @patch('/assessment-sub-section2s/{id}')
  @response(204, {
    description: 'AssessmentSubSection2 PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssessmentSubSection2, {partial: true}),
        },
      },
    })
    assessmentSubSection2: AssessmentSubSection2,
  ): Promise<void> {
    await this.assessmentSubSection2Repository.updateById(id, assessmentSubSection2);
  }

  @put('/assessment-sub-section2s/{id}')
  @response(204, {
    description: 'AssessmentSubSection2 PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() assessmentSubSection2: AssessmentSubSection2,
  ): Promise<void> {
    await this.assessmentSubSection2Repository.replaceById(id, assessmentSubSection2);
  }

  @del('/assessment-sub-section2s/{id}')
  @response(204, {
    description: 'AssessmentSubSection2 DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.assessmentSubSection2Repository.deleteById(id);
  }
}
