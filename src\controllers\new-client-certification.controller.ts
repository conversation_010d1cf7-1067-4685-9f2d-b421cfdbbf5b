import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {NewClientCertification} from '../models';
import {NewClientCertificationRepository} from '../repositories';

export class NewClientCertificationController {
  constructor(
    @repository(NewClientCertificationRepository)
    public newClientCertificationRepository : NewClientCertificationRepository,
  ) {}

  @post('/new-client-certifications')
  @response(200, {
    description: 'NewClientCertification model instance',
    content: {'application/json': {schema: getModelSchemaRef(NewClientCertification)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewClientCertification, {
            title: 'NewNewClientCertification',
            exclude: ['id'],
          }),
        },
      },
    })
    newClientCertification: Omit<NewClientCertification, 'id'>,
  ): Promise<NewClientCertification> {
    return this.newClientCertificationRepository.create(newClientCertification);
  }

  @get('/new-client-certifications/count')
  @response(200, {
    description: 'NewClientCertification model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(NewClientCertification) where?: Where<NewClientCertification>,
  ): Promise<Count> {
    return this.newClientCertificationRepository.count(where);
  }

  @get('/new-client-certifications')
  @response(200, {
    description: 'Array of NewClientCertification model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(NewClientCertification, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(NewClientCertification) filter?: Filter<NewClientCertification>,
  ): Promise<NewClientCertification[]> {
    return this.newClientCertificationRepository.find(filter);
  }

  @patch('/new-client-certifications')
  @response(200, {
    description: 'NewClientCertification PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewClientCertification, {partial: true}),
        },
      },
    })
    newClientCertification: NewClientCertification,
    @param.where(NewClientCertification) where?: Where<NewClientCertification>,
  ): Promise<Count> {
    return this.newClientCertificationRepository.updateAll(newClientCertification, where);
  }

  @get('/new-client-certifications/{id}')
  @response(200, {
    description: 'NewClientCertification model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(NewClientCertification, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(NewClientCertification, {exclude: 'where'}) filter?: FilterExcludingWhere<NewClientCertification>
  ): Promise<NewClientCertification> {
    return this.newClientCertificationRepository.findById(id, filter);
  }

  @patch('/new-client-certifications/{id}')
  @response(204, {
    description: 'NewClientCertification PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewClientCertification, {partial: true}),
        },
      },
    })
    newClientCertification: NewClientCertification,
  ): Promise<void> {
    await this.newClientCertificationRepository.updateById(id, newClientCertification);
  }

  @put('/new-client-certifications/{id}')
  @response(204, {
    description: 'NewClientCertification PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() newClientCertification: NewClientCertification,
  ): Promise<void> {
    await this.newClientCertificationRepository.replaceById(id, newClientCertification);
  }

  @del('/new-client-certifications/{id}')
  @response(204, {
    description: 'NewClientCertification DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.newClientCertificationRepository.deleteById(id);
  }
}
