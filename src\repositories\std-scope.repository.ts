import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {StdScope, StdScopeRelations, StdTopic} from '../models';
import {StdTopicRepository} from './std-topic.repository';

export class StdScopeRepository extends DefaultCrudRepository<
  StdScope,
  typeof StdScope.prototype.id,
  StdScopeRelations
> {

  public readonly stdTopics: HasManyRepositoryFactory<StdTopic, typeof StdScope.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('StdTopicRepository') protected stdTopicRepositoryGetter: Getter<StdTopicRepository>,
  ) {
    super(StdScope, dataSource);
    this.stdTopics = this.createHasManyRepositoryFactoryFor('stdTopics', stdTopicRepositoryGetter,);
    this.registerInclusionResolver('stdTopics', this.stdTopics.inclusionResolver);
  }
}
