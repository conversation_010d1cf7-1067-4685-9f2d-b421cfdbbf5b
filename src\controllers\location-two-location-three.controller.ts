import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  LocationTwo,
  LocationThree,
} from '../models';
import {LocationTwoRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class LocationTwoLocationThreeController {
  constructor(
    @repository(LocationTwoRepository) protected locationTwoRepository: LocationTwoRepository,
  ) { }

  @get('/location-twos/{id}/location-threes', {
    responses: {
      '200': {
        description: 'Array of LocationTwo has many LocationThree',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(LocationThree)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<LocationThree>,
  ): Promise<LocationThree[]> {
    return this.locationTwoRepository.locationThrees(id).find(filter);
  }

  @post('/location-twos/{id}/location-threes', {
    responses: {
      '200': {
        description: 'LocationTwo model instance',
        content: {'application/json': {schema: getModelSchemaRef(LocationThree)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof LocationTwo.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(LocationThree, {
            title: 'NewLocationThreeInLocationTwo',
            exclude: ['id'],
            optional: ['locationTwoId']
          }),
        },
      },
    }) locationThree: Omit<LocationThree, 'id'>,
  ): Promise<LocationThree> {
    return this.locationTwoRepository.locationThrees(id).create(locationThree);
  }

  // @patch('/location-twos/{id}/location-threes', {
  //   responses: {
  //     '200': {
  //       description: 'LocationTwo.LocationThree PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(LocationThree, {partial: true}),
  //       },
  //     },
  //   })
  //   locationThree: Partial<LocationThree>,
  //   @param.query.object('where', getWhereSchemaFor(LocationThree)) where?: Where<LocationThree>,
  // ): Promise<Count> {
  //   return this.locationTwoRepository.locationThrees(id).patch(locationThree, where);
  // }

  // @del('/location-twos/{id}/location-threes', {
  //   responses: {
  //     '200': {
  //       description: 'LocationTwo.LocationThree DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(LocationThree)) where?: Where<LocationThree>,
  // ): Promise<Count> {
  //   return this.locationTwoRepository.locationThrees(id).delete(where);
  // }
  
}
