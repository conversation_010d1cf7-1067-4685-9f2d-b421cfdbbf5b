import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  QuantitativeSubmission,
  FormCollection,
} from '../models';
import {QuantitativeSubmissionRepository} from '../repositories';

export class QuantitativeSubmissionFormCollectionController {
  constructor(
    @repository(QuantitativeSubmissionRepository)
    public quantitativeSubmissionRepository: QuantitativeSubmissionRepository,
  ) { }

  @get('/quantitative-submissions/{id}/form-collection', {
    responses: {
      '200': {
        description: 'FormCollection belonging to QuantitativeSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(FormCollection),
          },
        },
      },
    },
  })
  async getFormCollection(
    @param.path.number('id') id: typeof QuantitativeSubmission.prototype.id,
  ): Promise<FormCollection> {
    return this.quantitativeSubmissionRepository.dcf(id);
  }
}
