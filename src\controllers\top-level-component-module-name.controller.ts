import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  TopLevelComponent,
  ModuleName,
} from '../models';
import {TopLevelComponentRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class TopLevelComponentModuleNameController {
  constructor(
    @repository(TopLevelComponentRepository) protected topLevelComponentRepository: TopLevelComponentRepository,
  ) { }

  @get('/top-level-components/{id}/module-names', {
    responses: {
      '200': {
        description: 'Array of TopLevelComponent has many ModuleName',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ModuleName)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<ModuleName>,
  ): Promise<ModuleName[]> {
    return this.topLevelComponentRepository.moduleNames(id).find(filter);
  }

  @post('/top-level-components/{id}/module-names', {
    responses: {
      '200': {
        description: 'TopLevelComponent model instance',
        content: {'application/json': {schema: getModelSchemaRef(ModuleName)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof TopLevelComponent.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ModuleName, {
            title: 'NewModuleNameInTopLevelComponent',
            exclude: ['id'],
            optional: ['topLevelComponentId']
          }),
        },
      },
    }) moduleName: Omit<ModuleName, 'id'>,
  ): Promise<ModuleName> {
    return this.topLevelComponentRepository.moduleNames(id).create(moduleName);
  }

  // @patch('/top-level-components/{id}/module-names', {
  //   responses: {
  //     '200': {
  //       description: 'TopLevelComponent.ModuleName PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ModuleName, {partial: true}),
  //       },
  //     },
  //   })
  //   moduleName: Partial<ModuleName>,
  //   @param.query.object('where', getWhereSchemaFor(ModuleName)) where?: Where<ModuleName>,
  // ): Promise<Count> {
  //   return this.topLevelComponentRepository.moduleNames(id).patch(moduleName, where);
  // }

  // @del('/top-level-components/{id}/module-names', {
  //   responses: {
  //     '200': {
  //       description: 'TopLevelComponent.ModuleName DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(ModuleName)) where?: Where<ModuleName>,
  // ): Promise<Count> {
  //   return this.topLevelComponentRepository.moduleNames(id).delete(where);
  // }
  
}
