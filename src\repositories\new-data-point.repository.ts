import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewDataPoint, NewDataPointRelations} from '../models';

export class NewDataPointRepository extends DefaultCrudRepository<
  NewDataPoint,
  typeof NewDataPoint.prototype.id,
  NewDataPointRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(NewDataPoint, dataSource);
  }
}
