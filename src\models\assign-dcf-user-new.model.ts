import {Entity, model, property} from '@loopback/repository';

@model()
export class AssignDcfUserNew extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'number',
  })
  site?: number;



  @property({
    type: 'array',
    itemType: 'any',
  })
  comments?: any[];

  @property({
    type: 'number',
  })
  frequency?: number;

  @property({
    type: 'number',
  })
  creator_id?: number;

  @property({
    type: 'number',
  })
  modifier_id?: number;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'any',
  })
  approver_id?: any;

  @property({
    type: 'number',
  })
  user_id?: number;

  @property({
    type: 'number',
  })
  reviewer_id?: number;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'string',
  })
  start_date?: string;

  @property({
    type: 'string',
  })
  end_date?: string;

  @property({
    type: 'number',
  })
  standard?: number;

  @property({
    type: 'number',
  })
  dcfId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<AssignDcfUserNew>) {
    super(data);
  }
}

export interface AssignDcfUserNewRelations {
  // describe navigational properties here
}

export type AssignDcfUserNewWithRelations = AssignDcfUserNew & AssignDcfUserNewRelations;
