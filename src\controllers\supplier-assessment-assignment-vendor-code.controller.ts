import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  SupplierAssessmentAssignment,
  VendorCode,
} from '../models';
import {SupplierAssessmentAssignmentRepository} from '../repositories';

export class SupplierAssessmentAssignmentVendorCodeController {
  constructor(
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
  ) { }

  @get('/supplier-assessment-assignments/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to SupplierAssessmentAssignment',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.string('id') id: typeof SupplierAssessmentAssignment.prototype.id,
  ): Promise<VendorCode> {
    return this.supplierAssessmentAssignmentRepository.vendor(id);
  }
}
