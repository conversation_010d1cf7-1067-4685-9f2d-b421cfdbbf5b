import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MongoDataSource} from '../datasources';
import {ScopeTwo, ScopeTwoRelations} from '../models';

export class ScopeTwoRepository extends DefaultCrudRepository<
  ScopeTwo,
  typeof ScopeTwo.prototype.id,
  ScopeTwoRelations
> {
  constructor(
    @inject('datasources.mongo') dataSource: MongoDataSource,
  ) {
    super(ScopeTwo, dataSource);
  }
}
