import {Entity, model, property, hasMany} from '@loopback/repository';
import {CertIssueAuthority} from './cert-issue-authority.model';
import {CertificationLevel} from './certification-level.model';

@model()
export class Certification extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @hasMany(() => CertIssueAuthority)
  certIssueAuthorities: CertIssueAuthority[];

  @hasMany(() => CertificationLevel)
  certificationLevels: CertificationLevel[];

  constructor(data?: Partial<Certification>) {
    super(data);
  }
}

export interface CertificationRelations {
  // describe navigational properties here
}

export type CertificationWithRelations = Certification & CertificationRelations;
