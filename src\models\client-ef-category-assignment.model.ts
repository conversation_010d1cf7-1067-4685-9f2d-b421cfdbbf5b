import {Entity, model, property} from '@loopback/repository';

@model()
export class ClientEfCategoryAssignment extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'array',
    itemType: 'string',
  })
  selected_ids?: string[];




  @property({
    type: 'string',
  })
  created_on?: string;


  @property({
    type: 'string',
  })
  modified_on?: string;


  @property({
    type: 'number',
  })
  created_by?: number;


  @property({
    type: 'number',
  })
  modified_by?: number;
  @property({
    type: 'number',
  })
  efCategoryId?: number;
  @property({
    type: 'number',
  })
  efStandardId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<ClientEfCategoryAssignment>) {
    super(data);
  }
}

export interface ClientEfCategoryAssignmentRelations {
  // describe navigational properties here
}

export type ClientEfCategoryAssignmentWithRelations = ClientEfCategoryAssignment & ClientEfCategoryAssignmentRelations;
