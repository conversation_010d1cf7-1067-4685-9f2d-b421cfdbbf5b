import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {GhgCategory, GhgCategoryRelations, GhgSubCategory} from '../models';
import {GhgSubCategoryRepository} from './ghg-sub-category.repository';

export class GhgCategoryRepository extends DefaultCrudRepository<
  GhgCategory,
  typeof GhgCategory.prototype.id,
  GhgCategoryRelations
> {

  public readonly ghgSubCategories: HasManyRepositoryFactory<GhgSubCategory, typeof GhgCategory.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('GhgSubCategoryRepository') protected ghgSubCategoryRepositoryGetter: Getter<GhgSubCategoryRepository>,
  ) {
    super(GhgCategory, dataSource);
    this.ghgSubCategories = this.createHasManyRepositoryFactoryFor('ghgSubCategories', ghgSubCategoryRepositoryGetter,);
    this.registerInclusionResolver('ghgSubCategories', this.ghgSubCategories.inclusionResolver);
  }
}
