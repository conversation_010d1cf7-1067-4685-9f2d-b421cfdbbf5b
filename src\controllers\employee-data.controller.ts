import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {EmployeeData} from '../models';
import {EmployeeDataRepository} from '../repositories';
import {EmployeeDataService} from '../services';

export class EmployeeDataController {
  constructor(
    @inject('repositories.EmployeeDataRepository')
    public employeeDataRepository: EmployeeDataRepository,
    @inject('services.EmployeeDataService')
    public employeeDataService: EmployeeDataService,
  ) { }

  @post('/employee-data')
  @response(200, {
    description: 'EmployeeData model instance',
    content: {'application/json': {schema: getModelSchemaRef(EmployeeData)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EmployeeData, {
            title: 'NewEmployeeData',
            exclude: ['id'],
          }),
        },
      },
    })
    employeeData: Omit<EmployeeData, 'id'>,
  ): Promise<EmployeeData> {
    return this.employeeDataRepository.create(employeeData);
  }

  @get('/employee-data/count')
  @response(200, {
    description: 'EmployeeData model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(EmployeeData) where?: Where<EmployeeData>,
  ): Promise<Count> {
    return this.employeeDataRepository.count(where);
  }

  @get('/employee-data')
  @response(200, {
    description: 'Array of EmployeeData model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmployeeData, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(EmployeeData) filter?: Filter<EmployeeData>,
  ): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find(filter);
  }

  @get('/employee-data/active')
  @response(200, {
    description: 'Array of active EmployeeData model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmployeeData, {includeRelations: true}),
        },
      },
    },
  })
  async findActive(): Promise<EmployeeData[]> {
    return this.employeeDataService.getActiveEmployees();
  }

  @get('/employee-data/inactive')
  @response(200, {
    description: 'Array of inactive EmployeeData model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmployeeData, {includeRelations: true}),
        },
      },
    },
  })
  async findInactive(): Promise<EmployeeData[]> {
    return this.employeeDataService.getInactiveEmployees();
  }

  @get('/employee-data/by-location/{locationId}')
  @response(200, {
    description: 'Array of EmployeeData model instances by location',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmployeeData, {includeRelations: true}),
        },
      },
    },
  })
  async findByLocation(
    @param.path.number('locationId') locationId: number,
  ): Promise<EmployeeData[]> {
    return this.employeeDataService.getEmployeesByLocation(locationId);
  }

  @get('/employee-data/by-grade/{grade}')
  @response(200, {
    description: 'Array of EmployeeData model instances by grade classification',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmployeeData, {includeRelations: true}),
        },
      },
    },
  })
  async findByGrade(
    @param.path.string('grade') grade: string,
  ): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {EmployeeGrade: grade}
    });
  }

  @get('/employee-data/by-role/{role}')
  @response(200, {
    description: 'Array of EmployeeData model instances by role type',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmployeeData, {includeRelations: true}),
        },
      },
    },
  })
  async findByRole(
    @param.path.string('role') role: string,
  ): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {EmployeeRoleType: role}
    });
  }

  @get('/employee-data/by-category/{category}')
  @response(200, {
    description: 'Array of EmployeeData model instances by category',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EmployeeData, {includeRelations: true}),
        },
      },
    },
  })
  async findByCategory(
    @param.path.string('category') category: string,
  ): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {EmployeeCategory: category}
    });
  }

  @get('/employee-data/{id}')
  @response(200, {
    description: 'EmployeeData model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(EmployeeData, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(EmployeeData, {exclude: 'where'}) filter?: FilterExcludingWhere<EmployeeData>
  ): Promise<EmployeeData> {
    return this.employeeDataRepository.findById(id, filter);
  }

  @patch('/employee-data/{id}')
  @response(204, {
    description: 'EmployeeData PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(EmployeeData, {partial: true}),
        },
      },
    })
    employeeData: EmployeeData,
  ): Promise<void> {
    await this.employeeDataRepository.updateById(id, employeeData);
  }

  @put('/employee-data/{id}')
  @response(204, {
    description: 'EmployeeData PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() employeeData: EmployeeData,
  ): Promise<void> {
    await this.employeeDataRepository.replaceById(id, employeeData);
  }

  @del('/employee-data/{id}')
  @response(204, {
    description: 'EmployeeData DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.employeeDataRepository.deleteById(id);
  }

  @del('/employee-data')
  @response(200, {
    description: 'EmployeeData DELETE ALL success',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            count: {
              type: 'number',
            },
            message: {
              type: 'string',
            },
          },
        },
      },
    },
  })
  async deleteAll(
    @param.where(EmployeeData) where?: Where<EmployeeData>,
  ): Promise<{count: number; message: string}> {
    const result = await this.employeeDataRepository.deleteAll(where);
    return {
      count: result.count,
      message: `Successfully deleted ${result.count} employee records`,
    };
  }

  @post('/employee-data/analytics')
  @response(200, {
    description: 'Employee Analytics Data',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              period: {type: 'string'},
              startDate: {type: 'string'},
              endDate: {type: 'string'},
              dataPoints: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    category: {type: 'string'},
                    dataType: {type: 'string'},
                    count: {type: 'number'},
                    dpId: {type: 'string'},
                    period: {type: 'string'},
                  },
                },
              },
              totalDataPoints: {type: 'number'},
            },
          },
        },
      },
    },
  })
  async getEmployeeAnalytics(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              type: {
                type: 'number',
                description: '1-Monthly, 2-Bi-Monthly, 3-Quarterly, 4-Annually, 5-Bi-Annually (Returns 26 categories × 3 data types = 78 data points per period)',
              },
              fromDate: {
                type: 'string',
                description: 'Start date in ddMMyyyy format',
              },
              toDate: {
                type: 'string',
                description: 'End date in ddMMyyyy format',
              },
            },
            required: ['type', 'fromDate', 'toDate'],
          },
        },
      },
    })
    requestBody: {
      type: number;
      fromDate: string;
      toDate: string;
    },
  ): Promise<any[]> {
    const {type, fromDate, toDate} = requestBody;
    return this.employeeDataService.getEmployeeAnalytics(type, fromDate, toDate);
  }

  @get('/employee-data/debug/distribution')
  @response(200, {
    description: 'Employee Data Distribution for Debugging',
  })
  async getDataDistribution(): Promise<any> {
    // Get distribution by EmployeeGrade
    const gradeDistribution = await this.employeeDataRepository.execute(
      'SELECT EmployeeGrade, COUNT(*) as count FROM EmployeeData GROUP BY EmployeeGrade',
      []
    );

    // Get distribution by EmployeeCategory
    const categoryDistribution = await this.employeeDataRepository.execute(
      'SELECT EmployeeCategory, COUNT(*) as count FROM EmployeeData GROUP BY EmployeeCategory',
      []
    );

    // Get distribution by EmployeeRoleType
    const roleDistribution = await this.employeeDataRepository.execute(
      'SELECT EmployeeRoleType, COUNT(*) as count FROM EmployeeData GROUP BY EmployeeRoleType',
      []
    );

    // Get cross-distribution: Category + Grade
    const categoryGradeDistribution = await this.employeeDataRepository.execute(
      'SELECT EmployeeCategory, EmployeeGrade, COUNT(*) as count FROM EmployeeData GROUP BY EmployeeCategory, EmployeeGrade',
      []
    );

    // Get cross-distribution: Role + Grade
    const roleGradeDistribution = await this.employeeDataRepository.execute(
      'SELECT EmployeeRoleType, EmployeeGrade, COUNT(*) as count FROM EmployeeData GROUP BY EmployeeRoleType, EmployeeGrade',
      []
    );

    // Get Permanent Employees with "Other" grade to debug
    const permanentOtherGrades = await this.employeeDataRepository.execute(
      'SELECT raw_grade, COUNT(*) as count FROM EmployeeData WHERE EmployeeCategory = "Permanent" AND EmployeeGrade = "Other" GROUP BY raw_grade ORDER BY count DESC',
      []
    );

    return {
      gradeDistribution,
      categoryDistribution,
      roleDistribution,
      categoryGradeDistribution,
      roleGradeDistribution,
      permanentOtherGrades,
      totalRecords: await this.employeeDataRepository.count()
    };
  }

  @post('/employee-data/reprocess-grades')
  @response(200, {
    description: 'Reprocess all employee grades with updated mapping',
  })
  async reprocessGrades(): Promise<{message: string, updated: number}> {
    return this.employeeDataService.reprocessAllGrades();
  }

  @get('/employee-data/test-grade-mapping/{grade}')
  @response(200, {
    description: 'Test grade mapping for a specific grade',
  })
  async testGradeMapping(
    @param.path.string('grade') grade: string,
  ): Promise<{inputGrade: string, mappedGrade: string}> {
    return this.employeeDataService.testGradeMapping(grade);
  }

  @post('/employee-data/retention-rate-by-period')
  @response(200, {
    description: 'Retention rate for each period in the range (by gender)',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              periodName: {type: 'string'},
              gender: {type: 'string'},
              efValue: {type: 'number'},
              unitOfMeasure: {type: 'string'},
              sapId: {type: 'number'},
              title: {type: 'string'},
              periodFrom: {type: 'string'},
              periodTo: {type: 'string'},
              value: {type: 'number'},
              reporting_period: {type: 'string'},
              rp: {type: 'array', items: {type: 'string'}},
              entity: {type: 'string'},
              locationId: {type: 'number'},
              level: {type: 'number'},
              methodology: {type: 'string'},
            },
          },
        },
      },
    },
  })
  async getRetentionRateByPeriod(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              type: {type: 'number'},
              fromDate: {type: 'string'},
              toDate: {type: 'string'},
            },
            required: ['type', 'fromDate', 'toDate'],
          },
        },
      },
    })
    requestBody: {type: number; fromDate: string; toDate: string},
  ): Promise<any[]> {
    const {type, fromDate, toDate} = requestBody;
    const periods = this.employeeDataService['generatePeriods'](
      type,
      DateTime.fromFormat(fromDate, 'ddMMyyyy'),
      DateTime.fromFormat(toDate, 'ddMMyyyy'),
    );
    const results = [];
    for (let i = 0; i < periods.length; i++) {
      const period = periods[i];
      // Previous period is always previous month
      const prevEnd = period.startDate.minus({months: 1}).endOf('month');
      // Build rp array (MM-yyyy for each month in the period)
      const rpArr: string[] = [];
      let rpCursor = period.startDate.startOf('month');
      while (rpCursor <= period.endDate) {
        rpArr.push(rpCursor.toFormat('MM-yyyy'));
        rpCursor = rpCursor.plus({months: 1});
      }
      const periodFrom = rpArr.length > 0 ? rpArr[0] : '';
      const periodTo = rpArr.length > 1 ? rpArr[rpArr.length - 1] : periodFrom;
      for (const gender of ['Male', 'Female']) {
        const baseCategory = {filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee', EmployeeGender: gender}};
        // Employees at end of current period
        const employeesAtEnd = await this.employeeDataService.calculateTotalEmployees({
          startDate: period.endDate,
          endDate: period.endDate
        }, baseCategory);
        // Employees at end of previous period
        const employeesAtPrevEnd = await this.employeeDataService.calculateTotalEmployees({
          startDate: prevEnd,
          endDate: prevEnd
        }, baseCategory);
        const efValue = employeesAtPrevEnd > 0 ? ((employeesAtEnd / employeesAtPrevEnd) * 100) : 0;
        results.push({
          periodName: period.periodName,
          gender,
          efValue: Math.round(efValue * 100) / 100,
          unitOfMeasure: 'nos',
          sapId: 8,
          title: `Permanent ${gender} Employee`,
          periodFrom,
          periodTo,
          value: employeesAtEnd,
          reporting_period: period.periodName,
          rp: rpArr,
          entity: 'India',
          locationId: 103,
          level: 1,
          methodology: '(Number of employees at the end of a period / Number of employees at the start of the period) * 100'
        });
      }
    }
    return results;
  }

  @post('/employee-data/attrition-rate-by-period')
  @response(200, {
    description: 'Attrition rate for each period in the range (by gender)',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              periodName: {type: 'string'},
              gender: {type: 'string'},
              efValue: {type: 'number'},
              unitOfMeasure: {type: 'string'},
              sapId: {type: 'number'},
              title: {type: 'string'},
              periodFrom: {type: 'string'},
              periodTo: {type: 'string'},
              value: {type: 'number'},
              reporting_period: {type: 'string'},
              rp: {type: 'array', items: {type: 'string'}},
              entity: {type: 'string'},
              locationId: {type: 'number'},
              level: {type: 'number'},
              methodology: {type: 'string'},
            },
          },
        },
      },
    },
  })
  async getAttritionRateByPeriod(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              type: {type: 'number'},
              fromDate: {type: 'string'},
              toDate: {type: 'string'},
            },
            required: ['type', 'fromDate', 'toDate'],
          },
        },
      },
    })
    requestBody: {type: number; fromDate: string; toDate: string},
  ): Promise<any[]> {
    const {type, fromDate, toDate} = requestBody;
    const periods = this.employeeDataService['generatePeriods'](
      type,
      DateTime.fromFormat(fromDate, 'ddMMyyyy'),
      DateTime.fromFormat(toDate, 'ddMMyyyy'),
    );
    const results = [];
    for (let i = 0; i < periods.length; i++) {
      const period = periods[i];
      // Build rp array (MM-yyyy for each month in the period)
      const rpArr: string[] = [];
      let rpCursor = period.startDate.startOf('month');
      while (rpCursor <= period.endDate) {
        rpArr.push(rpCursor.toFormat('MM-yyyy'));
        rpCursor = rpCursor.plus({months: 1});
      }
      const periodFrom = rpArr.length > 0 ? rpArr[0] : '';
      const periodTo = rpArr.length > 1 ? rpArr[rpArr.length - 1] : periodFrom;
      for (const gender of ['Male', 'Female']) {
        const baseCategory = {filters: {EmployeeCategory: 'Permanent', EmployeeRoleType: 'Employee', EmployeeGender: gender}};
        // Number of leavers during the period
        const leavers = await this.employeeDataService.calculateTurnover(period, baseCategory);
        // Average number of employees during the period
        const employeesAtStart = await this.employeeDataService.calculateTotalEmployees({
          startDate: period.startDate,
          endDate: period.startDate
        }, baseCategory);
        const employeesAtEnd = await this.employeeDataService.calculateTotalEmployees({
          startDate: period.endDate,
          endDate: period.endDate
        }, baseCategory);
        const avgEmployees = (employeesAtStart + employeesAtEnd) / 2;
        const efValue = avgEmployees > 0 ? ((leavers / avgEmployees) * 100) : 0;
        results.push({
          periodName: period.periodName,
          gender,
          efValue: Math.round(efValue * 100) / 100,
          unitOfMeasure: 'nos',
          sapId: 8,
          title: `Permanent ${gender} Employee`,
          periodFrom,
          periodTo,
          value: leavers,
          reporting_period: period.periodName,
          rp: rpArr,
          entity: 'India',
          locationId: 103,
          level: 1,
          methodology: '(Number of employees who left during the period / Number of employees during the period) * 100'
        });
      }
    }
    return results;
  }
}
