import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignQlEntityUser, AssignQlEntityUserRelations, QCategory, QTopic, QSection, ConsolidateFormCollection} from '../models';
import {QCategoryRepository} from './q-category.repository';
import {QTopicRepository} from './q-topic.repository';
import {QSectionRepository} from './q-section.repository';
import {ConsolidateFormCollectionRepository} from './consolidate-form-collection.repository';

export class AssignQlEntityUserRepository extends DefaultCrudRepository<
  AssignQlEntityUser,
  typeof AssignQlEntityUser.prototype.id,
  AssignQlEntityUserRelations
> {

  public readonly qCategory: BelongsToAccessor<QCategory, typeof AssignQlEntityUser.prototype.id>;

  public readonly qTopic: BelongsToAccessor<QTopic, typeof AssignQlEntityUser.prototype.id>;

  public readonly qSection: BelongsToAccessor<QSection, typeof AssignQlEntityUser.prototype.id>;

  public readonly srf: BelongsToAccessor<ConsolidateFormCollection, typeof AssignQlEntityUser.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('QCategoryRepository') protected qCategoryRepositoryGetter: Getter<QCategoryRepository>, @repository.getter('QTopicRepository') protected qTopicRepositoryGetter: Getter<QTopicRepository>, @repository.getter('QSectionRepository') protected qSectionRepositoryGetter: Getter<QSectionRepository>, @repository.getter('ConsolidateFormCollectionRepository') protected consolidateFormCollectionRepositoryGetter: Getter<ConsolidateFormCollectionRepository>,
  ) {
    super(AssignQlEntityUser, dataSource);
    this.srf = this.createBelongsToAccessorFor('srf', consolidateFormCollectionRepositoryGetter,);
    this.registerInclusionResolver('srf', this.srf.inclusionResolver);
    this.qSection = this.createBelongsToAccessorFor('qSection', qSectionRepositoryGetter,);
    this.registerInclusionResolver('qSection', this.qSection.inclusionResolver);
    this.qTopic = this.createBelongsToAccessorFor('qTopic', qTopicRepositoryGetter,);
    this.registerInclusionResolver('qTopic', this.qTopic.inclusionResolver);
    this.qCategory = this.createBelongsToAccessorFor('qCategory', qCategoryRepositoryGetter,);
    this.registerInclusionResolver('qCategory', this.qCategory.inclusionResolver);
  }
}
