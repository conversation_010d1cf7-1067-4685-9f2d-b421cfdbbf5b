import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  QualitativeSubmission,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileQualitativeSubmissionController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/qualitative-submissions', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many QualitativeSubmission',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QualitativeSubmission)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QualitativeSubmission>,
  ): Promise<QualitativeSubmission[]> {
    return this.userProfileRepository.qualitativeSubmissions(id).find(filter);
  }

  @post('/user-profiles/{id}/qualitative-submissions', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(QualitativeSubmission)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeSubmission, {
            title: 'NewQualitativeSubmissionInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) qualitativeSubmission: Omit<QualitativeSubmission, 'id'>,
  ): Promise<QualitativeSubmission> {
    return this.userProfileRepository.qualitativeSubmissions(id).create(qualitativeSubmission);
  }

  @patch('/user-profiles/{id}/qualitative-submissions', {
    responses: {
      '200': {
        description: 'UserProfile.QualitativeSubmission PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QualitativeSubmission, {partial: true}),
        },
      },
    })
    qualitativeSubmission: Partial<QualitativeSubmission>,
    @param.query.object('where', getWhereSchemaFor(QualitativeSubmission)) where?: Where<QualitativeSubmission>,
  ): Promise<Count> {
    return this.userProfileRepository.qualitativeSubmissions(id).patch(qualitativeSubmission, where);
  }

  @del('/user-profiles/{id}/qualitative-submissions', {
    responses: {
      '200': {
        description: 'UserProfile.QualitativeSubmission DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(QualitativeSubmission)) where?: Where<QualitativeSubmission>,
  ): Promise<Count> {
    return this.userProfileRepository.qualitativeSubmissions(id).delete(where);
  }
}
