import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  HttpErrors,
  param,
  patch,
  post,
  put,
  requestBody,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {Action, DealerAssessmentAssignment, DealerAuditorChecklistSubmission} from '../models';
import {
  ActionRepository,
  DealerAssessmentAssignmentRepository,
  DealerAuditorChecklistSubmissionRepository,
  UserProfileRepository,
  UserRoleAuthorizationRepository
} from '../repositories';

export class DealerAssessmentAssignmentController {
  constructor(
    @repository(DealerAssessmentAssignmentRepository)
    public dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(DealerAuditorChecklistSubmissionRepository)
    public dealerAuditorChecklistSubmissionRepository: DealerAuditorChecklistSubmissionRepository,
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
  ) { }

  @post('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {'application/json': {schema: getModelSchemaRef(DealerAssessmentAssignment)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {
            title: 'NewDealerAssessmentAssignment',
            exclude: ['id'],
          }),
        },
      },
    })
    dealerAssessmentAssignment: Omit<DealerAssessmentAssignment, 'id'>,
  ): Promise<DealerAssessmentAssignment> {
    return this.dealerAssessmentAssignmentRepository.create(dealerAssessmentAssignment);
  }

  @get('/dealer-assessment-assignments/count', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(DealerAssessmentAssignment) where?: Where<DealerAssessmentAssignment>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.count(where);
  }

  @get('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'Array of DealerAssessmentAssignment model instances',
        content: {
          'application/json': {
            schema: {
              type: 'array',
              items: getModelSchemaRef(DealerAssessmentAssignment, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(DealerAssessmentAssignment) filter?: Filter<DealerAssessmentAssignment>,
  ): Promise<DealerAssessmentAssignment[]> {
    return this.dealerAssessmentAssignmentRepository.find(filter);
  }

  @patch('/dealer-assessment-assignments', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {partial: true}),
        },
      },
    })
    dealerAssessmentAssignment: DealerAssessmentAssignment,
    @param.where(DealerAssessmentAssignment) where?: Where<DealerAssessmentAssignment>,
  ): Promise<Count> {
    return this.dealerAssessmentAssignmentRepository.updateAll(dealerAssessmentAssignment, where);
  }

  @get('/dealer-assessment-assignments/{id}', {
    responses: {
      '200': {
        description: 'DealerAssessmentAssignment model instance',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DealerAssessmentAssignment, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(DealerAssessmentAssignment, {exclude: 'where'}) filter?: FilterExcludingWhere<DealerAssessmentAssignment>
  ): Promise<DealerAssessmentAssignment> {
    return this.dealerAssessmentAssignmentRepository.findById(id, filter);
  }

  @patch('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment PATCH success',
      },
    },
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(DealerAssessmentAssignment, {partial: true}),
        },
      },
    })
    dealerAssessmentAssignment: DealerAssessmentAssignment,
  ): Promise<void> {
    await this.dealerAssessmentAssignmentRepository.updateById(id, dealerAssessmentAssignment);
  }

  @put('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment PUT success',
      },
    },
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() dealerAssessmentAssignment: DealerAssessmentAssignment,
  ): Promise<void> {
    await this.dealerAssessmentAssignmentRepository.replaceById(id, dealerAssessmentAssignment);
  }

  @del('/dealer-assessment-assignments/{id}', {
    responses: {
      '204': {
        description: 'DealerAssessmentAssignment DELETE success',
      },
    },
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.dealerAssessmentAssignmentRepository.deleteById(id);
  }

  @post('/dealer-auditor-checklist-submissions-status')
  async getAssignment(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            userId: {
              type: 'number'
            },
            archive: {type: 'boolean', nullable: true},

          },
          required: ['userId'],
        },
      },
    },
  })
  requestBody: {userId: number, archive: boolean}
  ): Promise<any> {
    const {userId, archive} = requestBody
    try {
      const found = await this.userProfileRepository.findById(userId)
      if (found && found.clientId) {
        const roleCheck = await this.userRoleAuthorizationRepository.find({where: {user_id: userId, userProfileId: found.clientId}})
        if (roleCheck && roleCheck.length && roleCheck.some(x => x.roles?.includes(19))) {
          const assignmentList = await this.userProfileRepository.dealerAssessmentAssignments(found.clientId).find({"include": [{"relation": "dealer", "scope": {"fields": {"information": true, "dealerCode": true}}}, {relation: "vendor"}, {relation: "form"}, {relation: "dealerAuditorChecklistSubmission"}]})
          if (assignmentList.length && assignmentList.some(x => x.assessors?.includes(userId))) {
            const assignedList = assignmentList.filter(x => x.assessors?.includes(userId) && (!x.dealerAuditorChecklistSubmission?.type || x?.dealerAuditorChecklistSubmission?.type === 22))
            return {status: 2, archiveData: archive ? assignmentList.filter(x => x.assessors?.includes(userId) && (x.dealerAuditorChecklistSubmission && x.dealerAuditorChecklistSubmission?.type === 1)) : [], data: assignedList.map(({form, ...x}: any) => ({...x, checklistTitle: form?.title || '', maskId: 'MSI-' + (x?.vendor?.code || 'NA') + '-' + DateTime.fromISO(x.created_on, {zone: 'Asia/Calcutta'}).toFormat('ddMMyyyy')})), message: 'Checklist Found'}
          } else {
            return {status: 1, data: [], message: 'Checklist is not assigned'}
          }

        } else {
          return {status: 0, data: [], message: 'You are not authorized as MSI Dealer Accessor'}
        }
      } else {
        return {status: 0, data: [], message: 'Invalid Dealer'}
      }

    } catch (e) {
      return {status: false, data: [], message: e}
    }

  }

  @del('/dealer-assignment-calibration-action/{id}/cascade', {
    responses: {
      '200': {
        description: 'Delete counts for assignment and related records',
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                checklistSubmissionsCount: {type: 'number'},
                actionsCount: {type: 'number'},
                assignmentCount: {type: 'number'}
              }
            }
          }
        }
      },
      '404': {
        description: 'Dealer Assessment Assignment not found',
      },
      '500': {
        description: 'Internal Server Error',
      },
    },
  })
  async deleteSupplierAssessmentAssignmentCascade(
    @param.path.number('id') id: number,
  ): Promise<{checklistSubmissionsCount: number; actionsCount: number; assignmentCount: number}> {
    try {
      // First check if the assignment exists
      const assignment = await this.dealerAssessmentAssignmentRepository.findById(id);
      if (!assignment) {
        throw new HttpErrors.NotFound('Dealer Assessment Assignment not found');
      }

      // Delete related dealer auditor checklist submissions and get count
      const checklistSubmissionsCount = (await this.dealerAuditorChecklistSubmissionRepository.deleteAll({
        dealerAssessmentAssignmentId: id
      } as Where<DealerAuditorChecklistSubmission>)).count;

      // Delete related actions and get count
      const actionsCount = (await this.actionRepository.deleteAll({
        appId: id
      } as Where<Action>)).count;

      // Finally delete the supplier assessment assignment
      await this.dealerAssessmentAssignmentRepository.deleteById(id);

      return {
        checklistSubmissionsCount,
        actionsCount,
        assignmentCount: 1 // Since we deleted one assignment
      };
    } catch (error) {
      if (error.code === 'ENTITY_NOT_FOUND') {
        throw new HttpErrors.NotFound('Dealer Assessment Assignment not found');
      }
      throw new HttpErrors.InternalServerError('Error occurred while deleting records');
    }
  }
}
