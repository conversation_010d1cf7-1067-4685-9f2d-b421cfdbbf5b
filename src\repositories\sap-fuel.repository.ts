import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SapFuel, SapFuelRelations} from '../models';

export class SapFuelRepository extends DefaultCrudRepository<
  SapFuel,
  typeof SapFuel.prototype.id,
  SapFuelRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SapFuel, dataSource);
  }
}
