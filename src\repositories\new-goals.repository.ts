import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewGoals, NewGoalsRelations, NewTargets, NewInitiatives, NewTargetsTwo, NewIndicatorTwo} from '../models';
import {NewTargetsRepository} from './new-targets.repository';
import {NewInitiativesRepository} from './new-initiatives.repository';
import {NewTargetsTwoRepository} from './new-targets-two.repository';
import {NewIndicatorTwoRepository} from './new-indicator-two.repository';

export class NewGoalsRepository extends DefaultCrudRepository<
  NewGoals,
  typeof NewGoals.prototype.id,
  NewGoalsRelations
> {

  public readonly newTargets: HasManyRepositoryFactory<NewTargets, typeof NewGoals.prototype.id>;

  public readonly newInitiatives: HasManyRepositoryFactory<NewInitiatives, typeof NewGoals.prototype.id>;

  public readonly newTargetsTwos: HasManyRepositoryFactory<NewTargetsTwo, typeof NewGoals.prototype.id>;

  public readonly newIndicatorTwos: HasManyRepositoryFactory<NewIndicatorTwo, typeof NewGoals.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewTargetsRepository') protected newTargetsRepositoryGetter: Getter<NewTargetsRepository>, @repository.getter('NewInitiativesRepository') protected newInitiativesRepositoryGetter: Getter<NewInitiativesRepository>, @repository.getter('NewTargetsTwoRepository') protected newTargetsTwoRepositoryGetter: Getter<NewTargetsTwoRepository>, @repository.getter('NewIndicatorTwoRepository') protected newIndicatorTwoRepositoryGetter: Getter<NewIndicatorTwoRepository>,
  ) {
    super(NewGoals, dataSource);
    this.newIndicatorTwos = this.createHasManyRepositoryFactoryFor('newIndicatorTwos', newIndicatorTwoRepositoryGetter,);
    this.registerInclusionResolver('newIndicatorTwos', this.newIndicatorTwos.inclusionResolver);
    this.newTargetsTwos = this.createHasManyRepositoryFactoryFor('newTargetsTwos', newTargetsTwoRepositoryGetter,);
    this.registerInclusionResolver('newTargetsTwos', this.newTargetsTwos.inclusionResolver);
    this.newInitiatives = this.createHasManyRepositoryFactoryFor('newInitiatives', newInitiativesRepositoryGetter,);
    this.registerInclusionResolver('newInitiatives', this.newInitiatives.inclusionResolver);
    this.newTargets = this.createHasManyRepositoryFactoryFor('newTargets', newTargetsRepositoryGetter,);
    this.registerInclusionResolver('newTargets', this.newTargets.inclusionResolver);
  }
}
