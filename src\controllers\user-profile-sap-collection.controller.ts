import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  SapCollection,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileSapCollectionController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/sap-collections', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SapCollection',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SapCollection)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SapCollection>,
  ): Promise<SapCollection[]> {
    return this.userProfileRepository.sapCollections(id).find(filter);
  }

  @post('/user-profiles/{id}/sap-collections', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SapCollection)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapCollection, {
            title: 'NewSapCollectionInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) sapCollection: Omit<SapCollection, 'id'>,
  ): Promise<SapCollection> {
    return this.userProfileRepository.sapCollections(id).create(sapCollection);
  }

  @patch('/user-profiles/{id}/sap-collections', {
    responses: {
      '200': {
        description: 'UserProfile.SapCollection PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SapCollection, {partial: true}),
        },
      },
    })
    sapCollection: Partial<SapCollection>,
    @param.query.object('where', getWhereSchemaFor(SapCollection)) where?: Where<SapCollection>,
  ): Promise<Count> {
    return this.userProfileRepository.sapCollections(id).patch(sapCollection, where);
  }

  @del('/user-profiles/{id}/sap-collections', {
    responses: {
      '200': {
        description: 'UserProfile.SapCollection DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SapCollection)) where?: Where<SapCollection>,
  ): Promise<Count> {
    return this.userProfileRepository.sapCollections(id).delete(where);
  }
}
