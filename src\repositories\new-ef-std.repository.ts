import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEfStd, NewEfStdRelations, NewEfDate, NewEfCategory, GhgCategory} from '../models';
import {NewEfDateRepository} from './new-ef-date.repository';
import {NewEfCategoryRepository} from './new-ef-category.repository';
import {GhgCategoryRepository} from './ghg-category.repository';

export class NewEfStdRepository extends DefaultCrudRepository<
  NewEfStd,
  typeof NewEfStd.prototype.id,
  NewEfStdRelations
> {

  public readonly newEfDates: HasManyRepositoryFactory<NewEfDate, typeof NewEfStd.prototype.id>;

  public readonly newEfCategories: HasManyRepositoryFactory<NewEfCategory, typeof NewEfStd.prototype.id>;

  public readonly ghgCategories: HasManyRepositoryFactory<GhgCategory, typeof NewEfStd.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfDateRepository') protected newEfDateRepositoryGetter: Getter<NewEfDateRepository>, @repository.getter('NewEfCategoryRepository') protected newEfCategoryRepositoryGetter: Getter<NewEfCategoryRepository>, @repository.getter('GhgCategoryRepository') protected ghgCategoryRepositoryGetter: Getter<GhgCategoryRepository>,
  ) {
    super(NewEfStd, dataSource);
    this.ghgCategories = this.createHasManyRepositoryFactoryFor('ghgCategories', ghgCategoryRepositoryGetter,);
    this.registerInclusionResolver('ghgCategories', this.ghgCategories.inclusionResolver);
    this.newEfCategories = this.createHasManyRepositoryFactoryFor('newEfCategories', newEfCategoryRepositoryGetter,);
    this.registerInclusionResolver('newEfCategories', this.newEfCategories.inclusionResolver);
    this.newEfDates = this.createHasManyRepositoryFactoryFor('newEfDates', newEfDateRepositoryGetter,);
    this.registerInclusionResolver('newEfDates', this.newEfDates.inclusionResolver);
  }
}
