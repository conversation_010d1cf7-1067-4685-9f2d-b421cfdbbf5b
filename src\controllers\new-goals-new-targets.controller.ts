import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewGoals,
  NewTargets,
} from '../models';
import {NewGoalsRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewGoalsNewTargetsController {
  constructor(
    @repository(NewGoalsRepository) protected newGoalsRepository: NewGoalsRepository,
  ) { }

  @get('/new-goals/{id}/new-targets', {
    responses: {
      '200': {
        description: 'Array of NewGoals has many NewTargets',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewTargets)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewTargets>,
  ): Promise<NewTargets[]> {
    return this.newGoalsRepository.newTargets(id).find(filter);
  }

  @post('/new-goals/{id}/new-targets', {
    responses: {
      '200': {
        description: 'NewGoals model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewTargets)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewGoals.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewTargets, {
            title: 'NewNewTargetsInNewGoals',
            exclude: ['id'],
            optional: ['newGoalsId']
          }),
        },
      },
    }) newTargets: Omit<NewTargets, 'id'>,
  ): Promise<NewTargets> {
    return this.newGoalsRepository.newTargets(id).create(newTargets);
  }

  // @patch('/new-goals/{id}/new-targets', {
  //   responses: {
  //     '200': {
  //       description: 'NewGoals.NewTargets PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewTargets, {partial: true}),
  //       },
  //     },
  //   })
  //   newTargets: Partial<NewTargets>,
  //   @param.query.object('where', getWhereSchemaFor(NewTargets)) where?: Where<NewTargets>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.newTargets(id).patch(newTargets, where);
  // }

  // @del('/new-goals/{id}/new-targets', {
  //   responses: {
  //     '200': {
  //       description: 'NewGoals.NewTargets DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewTargets)) where?: Where<NewTargets>,
  // ): Promise<Count> {
  //   return this.newGoalsRepository.newTargets(id).delete(where);
  // }
  
}
