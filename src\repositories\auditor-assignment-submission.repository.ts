import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, BelongsToAccessor} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AuditorAssignmentSubmission, AuditorAssignmentSubmissionRelations, VendorCode} from '../models';
import {VendorCodeRepository} from './vendor-code.repository';

export class AuditorAssignmentSubmissionRepository extends DefaultCrudRepository<
  AuditorAssignmentSubmission,
  typeof AuditorAssignmentSubmission.prototype.id,
  AuditorAssignmentSubmissionRelations
> {

  public readonly vendor: BelongsToAccessor<VendorCode, typeof AuditorAssignmentSubmission.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('VendorCodeRepository') protected vendorCodeRepositoryGetter: Getter<VendorCodeRepository>,
  ) {
    super(AuditorAssignmentSubmission, dataSource);
    this.vendor = this.createBelongsToAccessorFor('vendor', vendorCodeRepositoryGetter,);
    this.registerInclusionResolver('vendor', this.vendor.inclusionResolver);
  }
}
