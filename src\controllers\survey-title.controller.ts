import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {SurveyTitle} from '../models';
import {SurveyTitleRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';


export class SurveyTitleController {
  constructor(
    @repository(SurveyTitleRepository)
    public surveyTitleRepository : SurveyTitleRepository,
  ) {}

  @post('/survey-titles')
  @response(200, {
    description: 'SurveyTitle model instance',
    content: {'application/json': {schema: getModelSchemaRef(SurveyTitle)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SurveyTitle, {
            title: 'NewSurveyTitle',
            exclude: ['id'],
          }),
        },
      },
    })
    surveyTitle: Omit<SurveyTitle, 'id'>,
  ): Promise<SurveyTitle> {
    return this.surveyTitleRepository.create(surveyTitle);
  }

  @get('/survey-titles/count')
  @response(200, {
    description: 'SurveyTitle model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(SurveyTitle) where?: Where<SurveyTitle>,
  ): Promise<Count> {
    return this.surveyTitleRepository.count(where);
  }

  @get('/survey-titles')
  @response(200, {
    description: 'Array of SurveyTitle model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SurveyTitle, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(SurveyTitle) filter?: Filter<SurveyTitle>,
  ): Promise<SurveyTitle[]> {
    return this.surveyTitleRepository.find(filter);
  }

  // @patch('/survey-titles')
  // @response(200, {
  //   description: 'SurveyTitle PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SurveyTitle, {partial: true}),
  //       },
  //     },
  //   })
  //   surveyTitle: SurveyTitle,
  //   @param.where(SurveyTitle) where?: Where<SurveyTitle>,
  // ): Promise<Count> {
  //   return this.surveyTitleRepository.updateAll(surveyTitle, where);
  // }

  @get('/survey-titles/{id}')
  @response(200, {
    description: 'SurveyTitle model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(SurveyTitle, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(SurveyTitle, {exclude: 'where'}) filter?: FilterExcludingWhere<SurveyTitle>
  ): Promise<SurveyTitle> {
    return this.surveyTitleRepository.findById(id, filter);
  }

  @patch('/survey-titles/{id}')
  @response(204, {
    description: 'SurveyTitle PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SurveyTitle, {partial: true}),
        },
      },
    })
    surveyTitle: SurveyTitle,
  ): Promise<void> {
    await this.surveyTitleRepository.updateById(id, surveyTitle);
  }

  @put('/survey-titles/{id}')
  @response(204, {
    description: 'SurveyTitle PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() surveyTitle: SurveyTitle,
  ): Promise<void> {
    await this.surveyTitleRepository.replaceById(id, surveyTitle);
  }

  @del('/survey-titles/{id}')
  @response(204, {
    description: 'SurveyTitle DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.surveyTitleRepository.deleteById(id);
  }
}
