import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Topic} from './topic.model';

@model({settings: {strict: false}})
export class SubTopic extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    limit: 1024,
  })
  description?: string;

  @property({
    type: 'string',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  standards: object;

  @belongsTo(() => Topic)
  topicId: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<SubTopic>) {
    super(data);
  }
}

export interface SubTopicRelations {
  // describe navigational properties here
}

export type SubTopicWithRelations = SubTopic & SubTopicRelations;
