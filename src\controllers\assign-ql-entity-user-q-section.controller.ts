import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignQlEntityUser,
  QSection,
} from '../models';
import {AssignQlEntityUserRepository} from '../repositories';

export class AssignQlEntityUserQSectionController {
  constructor(
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
  ) { }

  @get('/assign-ql-entity-users/{id}/q-section', {
    responses: {
      '200': {
        description: 'QSection belonging to AssignQlEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(QSection),
          },
        },
      },
    },
  })
  async getQSection(
    @param.path.number('id') id: typeof AssignQlEntityUser.prototype.id,
  ): Promise<QSection> {
    return this.assignQlEntityUserRepository.qSection(id);
  }
}
