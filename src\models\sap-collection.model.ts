import {Entity, model, property} from '@loopback/repository';

@model()
export class SapCollection extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  sapId?: string;

  @property({
    type: 'array', itemType: 'number'
  })
  indicatorIds?: number[];

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'string',
  })
  url?: string;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<SapCollection>) {
    super(data);
  }
}

export interface SapCollectionRelations {
  // describe navigational properties here
}

export type SapCollectionWithRelations = SapCollection & SapCollectionRelations;
