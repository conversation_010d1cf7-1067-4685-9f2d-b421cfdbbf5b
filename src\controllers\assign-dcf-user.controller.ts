import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {AssignDcfUser} from '../models';
import {AssignDcfUserRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class AssignDcfUserController {
  constructor(
    @repository(AssignDcfUserRepository)
    public assignDcfUserRepository : AssignDcfUserRepository,
  ) {}

  @post('/assign-dcf-users')
  @response(200, {
    description: 'AssignDcfUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDcfUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfUser, {
            title: 'NewAssignDcfUser',
            exclude: ['id'],
          }),
        },
      },
    })
    assignDcfUser: Omit<AssignDcfUser, 'id'>,
  ): Promise<AssignDcfUser> {
    return this.assignDcfUserRepository.create(assignDcfUser);
  }

  @get('/assign-dcf-users/count')
  @response(200, {
    description: 'AssignDcfUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDcfUser) where?: Where<AssignDcfUser>,
  ): Promise<Count> {
    return this.assignDcfUserRepository.count(where);
  }

  @get('/assign-dcf-users')
  @response(200, {
    description: 'Array of AssignDcfUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDcfUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDcfUser) filter?: Filter<AssignDcfUser>,
  ): Promise<AssignDcfUser[]> {
    return this.assignDcfUserRepository.find(filter);
  }

  // @patch('/assign-dcf-users')
  // @response(200, {
  //   description: 'AssignDcfUser PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(AssignDcfUser, {partial: true}),
  //       },
  //     },
  //   })
  //   assignDcfUser: AssignDcfUser,
  //   @param.where(AssignDcfUser) where?: Where<AssignDcfUser>,
  // ): Promise<Count> {
  //   return this.assignDcfUserRepository.updateAll(assignDcfUser, where);
  // }

  @get('/assign-dcf-users/{id}')
  @response(200, {
    description: 'AssignDcfUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDcfUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDcfUser, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDcfUser>
  ): Promise<AssignDcfUser> {
    return this.assignDcfUserRepository.findById(id, filter);
  }

  @patch('/assign-dcf-users/{id}')
  @response(204, {
    description: 'AssignDcfUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfUser, {partial: true}),
        },
      },
    })
    assignDcfUser: AssignDcfUser,
  ): Promise<void> {
    await this.assignDcfUserRepository.updateById(id, assignDcfUser);
  }

  @put('/assign-dcf-users/{id}')
  @response(204, {
    description: 'AssignDcfUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignDcfUser: AssignDcfUser,
  ): Promise<void> {
    await this.assignDcfUserRepository.replaceById(id, assignDcfUser);
  }

  @del('/assign-dcf-users/{id}')
  @response(204, {
    description: 'AssignDcfUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDcfUserRepository.deleteById(id);
  }
}
