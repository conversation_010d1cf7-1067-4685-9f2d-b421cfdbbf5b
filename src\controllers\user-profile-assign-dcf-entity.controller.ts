import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  AssignDcfEntity,
  UserProfile,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileAssignDcfEntityController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/assign-dcf-entities', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many AssignDcfEntity',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(AssignDcfEntity)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<AssignDcfEntity>,
  ): Promise<AssignDcfEntity[]> {
    return this.userProfileRepository.assignDcfEntities(id).find(filter);
  }

  @post('/user-profiles/{id}/assign-dcf-entities', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(AssignDcfEntity)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntity, {
            title: 'NewAssignDcfEntityInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) assignDcfEntity: Omit<AssignDcfEntity, 'id'>,
  ): Promise<any> {


    const {dcfId} = assignDcfEntity

    const find = await this.userProfileRepository.assignDcfEntities(id).find({where: {dcfId}})
    console.log(find)
    if (find.length === 1) {
      return find[0]
    } else if (find.length === 0) {
      return this.userProfileRepository.assignDcfEntities(id).create(assignDcfEntity);
    } else {
      throw new HttpErrors.BadRequest(`Assignment already already exists`);

    }




    return this.userProfileRepository.assignDcfEntities(id).create(assignDcfEntity);
  }

  @patch('/user-profiles/{id}/assign-dcf-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignDcfEntity PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntity, {partial: true}),
        },
      },
    })
    assignDcfEntity: Partial<AssignDcfEntity>,
    @param.query.object('where', getWhereSchemaFor(AssignDcfEntity)) where?: Where<AssignDcfEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignDcfEntities(id).patch(assignDcfEntity, where);
  }

  @del('/user-profiles/{id}/assign-dcf-entities', {
    responses: {
      '200': {
        description: 'UserProfile.AssignDcfEntity DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(AssignDcfEntity)) where?: Where<AssignDcfEntity>,
  ): Promise<Count> {
    return this.userProfileRepository.assignDcfEntities(id).delete(where);
  }
}
