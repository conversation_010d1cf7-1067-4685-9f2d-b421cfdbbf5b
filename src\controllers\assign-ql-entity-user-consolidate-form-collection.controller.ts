import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignQlEntityUser,
  ConsolidateFormCollection,
} from '../models';
import {AssignQlEntityUserRepository} from '../repositories';

export class AssignQlEntityUserConsolidateFormCollectionController {
  constructor(
    @repository(AssignQlEntityUserRepository)
    public assignQlEntityUserRepository: AssignQlEntityUserRepository,
  ) { }

  @get('/assign-ql-entity-users/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to AssignQlEntityUser',
        content: {
          'application/json': {
            schema: getModelSchemaRef(ConsolidateFormCollection),
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.number('id') id: typeof AssignQlEntityUser.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.assignQlEntityUserRepository.srf(id);
  }
}
