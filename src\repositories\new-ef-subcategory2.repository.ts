import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewEfSubcategory2, NewEfSubcategory2Relations, NewEfSubcategory3} from '../models';
import {NewEfSubcategory3Repository} from './new-ef-subcategory3.repository';

export class NewEfSubcategory2Repository extends DefaultCrudRepository<
  NewEfSubcategory2,
  typeof NewEfSubcategory2.prototype.id,
  NewEfSubcategory2Relations
> {

  public readonly newEfSubcategory3s: HasManyRepositoryFactory<NewEfSubcategory3, typeof NewEfSubcategory2.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewEfSubcategory3Repository') protected newEfSubcategory3RepositoryGetter: Getter<NewEfSubcategory3Repository>,
  ) {
    super(NewEfSubcategory2, dataSource);
    this.newEfSubcategory3s = this.createHasManyRepositoryFactoryFor('newEfSubcategory3s', newEfSubcategory3RepositoryGetter,);
    this.registerInclusionResolver('newEfSubcategory3s', this.newEfSubcategory3s.inclusionResolver);
  }
}
