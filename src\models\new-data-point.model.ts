import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class NewDataPoint extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  extra?: any[];

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  newMetricId?: number;

  @property({
    type: 'number',
  })
  order?: number;

  @property({
    type: 'number',
  })
  cloneMetricId?: number;
  @property({
    type: 'number',
  })
  cloneDataPointId?: number;
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<NewDataPoint>) {
    super(data);
  }
}

export interface NewDataPointRelations {
  // describe navigational properties here
}

export type NewDataPointWithRelations = NewDataPoint & NewDataPointRelations;
