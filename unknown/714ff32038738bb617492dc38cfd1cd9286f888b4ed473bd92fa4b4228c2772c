import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {QRequirement} from '../models';
import {QRequirementRepository} from '../repositories';

export class QRequirementController {
  constructor(
    @repository(QRequirementRepository)
    public qRequirementRepository : QRequirementRepository,
  ) {}

  @post('/q-requirements')
  @response(200, {
    description: 'QRequirement model instance',
    content: {'application/json': {schema: getModelSchemaRef(QRequirement)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QRequirement, {
            title: 'NewQRequirement',
            exclude: ['id'],
          }),
        },
      },
    })
    qRequirement: Omit<QRequirement, 'id'>,
  ): Promise<QRequirement> {
    return this.qRequirementRepository.create(qRequirement);
  }

  @get('/q-requirements/count')
  @response(200, {
    description: 'QRequirement model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(QRequirement) where?: Where<QRequirement>,
  ): Promise<Count> {
    return this.qRequirementRepository.count(where);
  }

  @get('/q-requirements')
  @response(200, {
    description: 'Array of QRequirement model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(QRequirement, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(QRequirement) filter?: Filter<QRequirement>,
  ): Promise<QRequirement[]> {
    return this.qRequirementRepository.find(filter);
  }

  @patch('/q-requirements')
  @response(200, {
    description: 'QRequirement PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QRequirement, {partial: true}),
        },
      },
    })
    qRequirement: QRequirement,
    @param.where(QRequirement) where?: Where<QRequirement>,
  ): Promise<Count> {
    return this.qRequirementRepository.updateAll(qRequirement, where);
  }

  @get('/q-requirements/{id}')
  @response(200, {
    description: 'QRequirement model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(QRequirement, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(QRequirement, {exclude: 'where'}) filter?: FilterExcludingWhere<QRequirement>
  ): Promise<QRequirement> {
    return this.qRequirementRepository.findById(id, filter);
  }

  @patch('/q-requirements/{id}')
  @response(204, {
    description: 'QRequirement PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QRequirement, {partial: true}),
        },
      },
    })
    qRequirement: QRequirement,
  ): Promise<void> {
    await this.qRequirementRepository.updateById(id, qRequirement);
  }

  @put('/q-requirements/{id}')
  @response(204, {
    description: 'QRequirement PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() qRequirement: QRequirement,
  ): Promise<void> {
    await this.qRequirementRepository.replaceById(id, qRequirement);
  }

  @del('/q-requirements/{id}')
  @response(204, {
    description: 'QRequirement DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.qRequirementRepository.deleteById(id);
  }
}
