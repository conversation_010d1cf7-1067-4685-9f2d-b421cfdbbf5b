import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {AssignDcfEntityUser} from '../models';
import {AssignDcfEntityUserRepository, FormCollectionRepository, LocationOneRepository, LocationThreeRepository, LocationTwoRepository, QuantitativeSubmissionRepository, UserProfileRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';

export class AssignDcfEntityUserController {
  constructor(
    @repository(AssignDcfEntityUserRepository)
    public assignDcfEntityUserRepository: AssignDcfEntityUserRepository,
    @repository(QuantitativeSubmissionRepository)
    public quantitativeSubmissionRepository: QuantitativeSubmissionRepository,
    @repository(UserProfileRepository)
    public userProfileRepository: UserProfileRepository,
    @repository(FormCollectionRepository)
    public formCollectionRepository: FormCollectionRepository,
    @repository(LocationOneRepository)
    public locationOneRepository: LocationOneRepository,
    @repository(LocationTwoRepository)
    public locationTwoRepository: LocationTwoRepository,
    @repository(LocationThreeRepository)
    public locationThreeRepository: LocationThreeRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }
  @post('/assign-dcf-entity-users')
  @response(200, {
    description: 'AssignDcfEntityUser model instance',
    content: {'application/json': {schema: getModelSchemaRef(AssignDcfEntityUser)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntityUser, {
            title: 'NewAssignDcfEntityUser',
            exclude: ['id'],
          }),
        },
      },
    })
    assignDcfEntityUser: Omit<AssignDcfEntityUser, 'id'>,
  ): Promise<AssignDcfEntityUser> {

    return this.assignDcfEntityUserRepository.create(assignDcfEntityUser);
  }

  @get('/assign-dcf-entity-users/count')
  @response(200, {
    description: 'AssignDcfEntityUser model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AssignDcfEntityUser) where?: Where<AssignDcfEntityUser>,
  ): Promise<Count> {
    return this.assignDcfEntityUserRepository.count(where);
  }

  @get('/assign-dcf-entity-users')
  @response(200, {
    description: 'Array of AssignDcfEntityUser model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AssignDcfEntityUser, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AssignDcfEntityUser) filter?: Filter<AssignDcfEntityUser>,
  ): Promise<AssignDcfEntityUser[]> {
    return this.assignDcfEntityUserRepository.find(filter);
  }

  @patch('/assign-dcf-entity-users')
  @response(200, {
    description: 'AssignDcfEntityUser PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntityUser, {partial: true}),
        },
      },
    })
    assignDcfEntityUser: AssignDcfEntityUser,
    @param.where(AssignDcfEntityUser) where?: Where<AssignDcfEntityUser>,
  ): Promise<Count> {
    if (where) {
      return this.assignDcfEntityUserRepository.updateAll(assignDcfEntityUser, where);
    } else {
      return {count: 0}
    }


  }

  @get('/assign-dcf-entity-users/{id}')
  @response(200, {
    description: 'AssignDcfEntityUser model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AssignDcfEntityUser, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AssignDcfEntityUser, {exclude: 'where'}) filter?: FilterExcludingWhere<AssignDcfEntityUser>
  ): Promise<AssignDcfEntityUser> {
    return this.assignDcfEntityUserRepository.findById(id, filter);
  }

  @patch('/assign-dcf-entity-users/{id}')
  @response(204, {
    description: 'AssignDcfEntityUser PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntityUser, {partial: true}),
        },
      },
    })
    assignDcfEntityUser: AssignDcfEntityUser,
  ): Promise<void> {

    await this.assignDcfEntityUserRepository.updateById(id, assignDcfEntityUser);

  }
  @patch('/assign-dcf-entity-users-custom/{id}')
  @response(204, {
    description: 'AssignDcfEntityUser PATCH success',
  })
  async updateByIdCustom(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AssignDcfEntityUser, {partial: true}),
        },
      },
    })
    assignDcfEntityUser: AssignDcfEntityUser,
  ): Promise<any> {
    const frequency_list = [{name: 'Monthly', id: 1}, {name: 'Bi-Monthly', id: 2}, {name: 'Quarterly', id: 3}, {name: 'Annually', id: 4}, {name: 'Bi-Annually', id: 5}]

    const existingObject = await this.assignDcfEntityUserRepository.findById(id);

    if (!existingObject || !existingObject?.userProfileId) {
      return {msg: `Nothing to Update`};
    }
    if (existingObject?.end_date) {
      delete assignDcfEntityUser.end_date
    }

    let count = 0
    const {reviewer_ids, reporter_ids, tier0_id, tier1_id, frequency, start_date, tier2_id, tier3_id, level, dcfId, userProfileId, entityAssId} = existingObject;
    console.log(existingObject.userProfileId, assignDcfEntityUser?.reporter_ids)
    if (assignDcfEntityUser?.reporter_ids && existingObject?.userProfileId !== 94) {
      let newReporterIds = assignDcfEntityUser?.reporter_ids || []
      let newReviewerIds = assignDcfEntityUser?.reviewer_ids || []
      const adminDetail = (await this.userProfileController.filteredUP({where: {id: existingObject?.userProfileId}}))?.[0]
      const form = await this.formCollectionRepository.findById(existingObject.dcfId)
      let entity: any = 'NA'
      if (level === 0) {
        entity = 'Corporate'
      } else if (level === 1) {
        entity = (await this.locationOneRepository.findById(tier1_id)).name
      } else if (level === 2) {
        entity = (await this.locationTwoRepository.findById(tier2_id)).name
      } else if (level === 3) {
        entity = (await this.locationThreeRepository.findById(tier3_id)).name

      }
      let userData: any = []
      const {added, removed} = this.compareIdsRobust(reporter_ids || [], newReporterIds)
      const {added: added2, removed: removed2} = this.compareIdsRobust(reviewer_ids || [], newReviewerIds)
      const allusers = Array.from(new Set([...added, ...added2, ...removed, ...removed2, ...newReporterIds, ...newReviewerIds]))
      if (allusers.length) {
        userData = await this.userProfileController.getUserProfileList({where: {id: {inq: allusers}}})
      }
      const reporterList = newReporterIds.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))
      const reviewerList = newReviewerIds.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))

      console.log('2', added, removed)
      if (added.length) {
        const reporterAddedMailIds = added.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))
        for (const reporter of reporterAddedMailIds) {
          if (reporter.to && reporter.name) {
            const body = `<p>Dear ${reporter.name}</p> <p style='margin: 5px 0px;'>You have been assigned as the <strong>Data Reporter</strong> for <strong> "${form.title}"</strong> for the reporting entity <strong>${entity}</strong>. Please find the assignment details below:</p> <ul> <li style='margin: 5px 0px;'><strong>Data Frequency:</strong> ${frequency_list?.find(x => x.id === frequency)?.name || ''}</li> <li style='margin: 5px 0px;'><strong>Reporting Period Start:</strong> ${DateTime.fromISO(start_date || '', {zone: 'utc'}).plus({days: 1}).toFormat('LLL-yyyy')}</li> <li style='margin: 5px 0px;'><strong>Assigned Reviewer:</strong>${newReviewerIds.length ? reviewerList.map(i => i.name).join(',') : 'Self Review'}</li> <li style='margin: 5px 0px;'><strong>Submission Due Date:</strong> 20th of the month succeeding the reporting period</li></ul> <p style='margin: 5px 0px;'>In case of any queries, raise a ticket or alternatively, write to us on ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>To proceed, please click on <a href=${adminDetail?.userPortalUrl} >EiSqr – ESG Platform</a> to log.</p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p> `
            const info = await this.sqsService.sendEmail(reporter.to, 'Assignment of Sustainability Data Collection Form – NAVIGOS', body, []).then((info) => {
              console.log(info)
              return info
            }).catch((err) => {
              console.log(err)
              return err
            })
          }

        }

      }
      // if (removed.length) {
      //   const reporterRemovedMailIds = removed.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))
      //   console.log(reporterRemovedMailIds, 'removedreporter')
      //   for (const reporter of reporterRemovedMailIds) {
      //     if (reporter.to && reporter.name) {
      //       const body = `<p>Dear ${reporter.name}</p> <p style='margin: 5px 0px;'>You have been de-assigned as a <strong>Data Reporter</strong> for <strong>${form.title}</strong> for the reporting entity <strong>${entity}</strong>. <p style='margin: 5px 0px;'>For clarification reach out to  ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p> `
      //       const info = await this.sqsService.sendEmail(reporter.to, 'Unassignment of Sustainability Data Collection Form – NAVIGOS', body, []).then((info) => {
      //         console.log(info)
      //         return info
      //       }).catch((err) => {
      //         console.log(err)
      //         return err
      //       })
      //     }

      //   }
      // }
      if (added2.length) {
        const reviewerAddedMailIds = added2.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))
        console.log(reviewerAddedMailIds, 'addedreviewer')
        for (const reviewer of reviewerAddedMailIds) {
          if (reviewer.to && reviewer.name) {
            const body = `<p>Dear ${reviewer.name}</p> <p style='margin: 5px 0px;'>You have been assigned as the <strong>Data Reviewer</strong> for <strong>"${form.title}"</strong> for the reporting entity <strong>${entity}</strong>. Please find the assignment details below:</p> <ul> <li style='margin: 5px 0px;'><strong>Data Frequency:</strong> ${frequency_list?.find(x => x.id === frequency)?.name || ''}</li> <li style='margin: 5px 0px;'><strong>Reporting Period Start:</strong> ${DateTime.fromISO(start_date || '', {zone: 'utc'}).plus({days: 1}).toFormat('LLL-yyyy')}</li> <li style='margin: 5px 0px;'><strong>Assigned Reporter:</strong>${newReporterIds.length ? reporterList.map(i => i.name).join(',') : ''}</li> <li style='margin: 5px 0px;'><strong>Submission Due Date:</strong> 25th of the month succeeding the reporting period </li></ul> <p style='margin: 5px 0px;'>In case of any queries, raise a ticket or alternatively, write to us on ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>To proceed, please click on <a href=${adminDetail?.userPortalUrl} >EiSqr – ESG Platform </a> to log in and complete the data review by the due date.</p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p> `
            const info = await this.sqsService.sendEmail(reviewer.to, 'Assignment of Sustainability Data Collection Form for Review  – NAVIGOS', body, []).then((info) => {
              console.log(info)
              return info
            }).catch((err) => {
              console.log(err)
              return err
            })
          }

        }
      }
      // if (removed2.length) {
      //   const reviewerRemovedMailIds = removed2.map(x => userData.find((y: any) => y.id === x) || '').filter(x => x).map(x => ({to: x.email, name: x?.information?.empname || x?.information?.companyname || ''}))
      //   console.log(reviewerRemovedMailIds, 'removedreviewer')
      //   for (const reviewer of reviewerRemovedMailIds) {
      //     if (reviewer.to && reviewer.name) {
      //       const body = `<p>Dear ${reviewer.name}</p> <p style='margin: 5px 0px;'>You have been de-assigned as a <strong>Data Reviewer</strong> for <strong>${form.title}</strong> for the reporting entity <strong>${entity}</strong>. <p style='margin: 5px 0px;'>For clarification reach out to  ${adminDetail.id === 289 ? '<EMAIL>' : adminDetail.email}</p><p style='margin: 5px 0px;'>Thank you for your cooperation.</p><p style='font-style:italic'>This email is an automated notification. Please do not reply to this message</p> `
      //       const info = await this.sqsService.sendEmail(reviewer.to, 'Unassignment of Sustainability Data Collection Form – NAVIGOS', body, []).then((info) => {
      //         console.log(info)
      //         return info
      //       }).catch((err) => {
      //         console.log(err)
      //         return err
      //       })
      //     }

      //   }
      // }

    }

    if (userProfileId && entityAssId && level && reviewer_ids?.length && !assignDcfEntityUser.reviewer_ids?.length) {
      const submittedData = await this.userProfileRepository.quantitativeSubmissions(userProfileId).find({
        where: {entityAssId, tier0_id, tier1_id, tier2_id, tier3_id, level, dcfId, entityUserAssId: id, type: 1}
      });

      if (submittedData.length) {
        try {
          const dt = DateTime.utc().toString();
          for (const submission of submittedData) {
            count++
            submission.type = 2;
            submission.reject = 0;
            submission.self = true;
            submission.last_modified_on = dt;
            submission.reviewed_on = dt;
            submission.reviewed_by = submission.last_modified_by;
            submission.reviewer_modified_by = submission.last_modified_by;
            submission.reviewer_modified_on = dt;
            await this.quantitativeSubmissionRepository.updateById(submission.id, submission);
          }

          await this.assignDcfEntityUserRepository.updateById(id, assignDcfEntityUser);
          return {msg: `${submittedData.length} submission(s) moved to approver stage, since these are stuck in reviewer stage`};

        } catch (e) {
          return {msg: `Err: ${existingObject.id}-${existingObject.entityAssId}: Found some submissions, but can't resolve (${submittedData.length - count} / ${submittedData.length}), share this error code/screenshot with the admin team for support`};
        }
      } else {
        await this.assignDcfEntityUserRepository.updateById(id, assignDcfEntityUser);
        return {msg: `Assignment Updated Successfully`};
      }
    } else {

      await this.assignDcfEntityUserRepository.updateById(id, assignDcfEntityUser);
      return {msg: `Assignment Updated Successfully`};
    }
  }

  @put('/assign-dcf-entity-users/{id}')
  @response(204, {
    description: 'AssignDcfEntityUser PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() assignDcfEntityUser: AssignDcfEntityUser,
  ): Promise<void> {
    await this.assignDcfEntityUserRepository.replaceById(id, assignDcfEntityUser);
  }

  @del('/assign-dcf-entity-users/{id}')
  @response(204, {
    description: 'AssignDcfEntityUser DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.assignDcfEntityUserRepository.deleteById(id);
  }
  @post('/delete-assignment-and-submissions')
  @response(200, {
    description: 'Qualitative Assignment & Submission Deleted',

  })
  async deleteAssignmentAndSubmissions(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              assignmentId: {
                type: 'number',
              },
              userProfileId: {
                type: 'number',
              },
              locations: {
                type: 'object',
                properties: {
                  tier0_id: {type: 'number', nullable: true},
                  tier1_id: {type: 'number', nullable: true},
                  tier2_id: {type: 'number', nullable: true},
                  tier3_id: {type: 'number', nullable: true},
                },
              },

              dcfId: {
                type: 'number',
              },
              validate: {
                type: 'string',
              }

            },
            required: ['dcfId', 'locations', 'userProfileId', 'validate'],
          }
        }
      },
    })
    requestBody: any
  ): Promise<any> {
    const {dcfId, locations, userProfileId, validate, assignmentId} = requestBody
    const dt = DateTime.utc().toFormat('yyyy-LLL-dd')
    if (validate === dt) {
      const assignments = await this.assignDcfEntityUserRepository.find({
        where: {
          userProfileId,
          ...locations, id: assignmentId,
          dcfId,
        },
      });
      let count = 0
      if (assignments && assignments.length > 0) {

        for (const assignment of assignments) {
          const submissions = await this.quantitativeSubmissionRepository.find({
            where: {
              entityAssId: assignment.entityAssId,
              entityUserAssId: assignment.id,
              userProfileId: userProfileId, dcfId,
              ...locations,
            },
          });


          for (const submission of submissions) {
            count++
            await this.quantitativeSubmissionRepository.deleteById(submission.id);
          }


          await this.assignDcfEntityUserRepository.deleteById(assignment.id);
        }

        return {result: true, assignmentCount: assignments.length, submissionCount: count, message: ` ${assignments.length} Assignments and related ${count} submissions deleted successfully`};
      } else {
        // Step 6: If no assignments found, return false
        return {result: false, message: 'No assignments found with the given conditions'};
      }

    }
    else {
      return {result: false, message: 'Validation Error'};
    }
  }
  compareIdsRobust(oldIds: number[], newIds: number[]) {
    const addedIds = [];
    const removedIds = [];

    // Handle null or undefined inputs gracefully
    const safeOldIds = oldIds || [];
    const safeNewIds = newIds || [];

    const oldIdSet = new Set(safeOldIds);
    const newIdSet = new Set(safeNewIds);

    for (const newId of safeNewIds) {
      if (!oldIdSet.has(newId)) {
        addedIds.push(newId);
      }
    }

    for (const oldId of safeOldIds) {
      if (!newIdSet.has(oldId)) {
        removedIds.push(oldId);
      }
    }

    return {added: addedIds.filter(i => i), removed: removedIds.filter(i => i)};
  }
}
