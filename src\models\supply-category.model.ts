import {Entity, hasMany, model, property} from '@loopback/repository';
import {SupplySection} from './supply-section.model';

@model()
export class SupplyCategory extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'string',
  })
  createdAt?: string;

  @property({
    type: 'string',
  })
  updatedAt?: string;

  @property({
    type: 'any',
  })
  remarks?: any;

  @property({
    type: 'string',
  })
  status?: string;



  @hasMany(() => SupplySection)
  supplySections: SupplySection[];

  constructor(data?: Partial<SupplyCategory>) {
    super(data);
  }
}

export interface SupplyCategoryRelations {
  // describe navigational properties here
}

export type SupplyCategoryWithRelations = SupplyCategory & SupplyCategoryRelations;
