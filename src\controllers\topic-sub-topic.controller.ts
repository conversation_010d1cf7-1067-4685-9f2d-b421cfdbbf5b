import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Topic,
  SubTopic,
} from '../models';
import {TopicRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class TopicSubTopicController {
  constructor(
    @repository(TopicRepository) protected topicRepository: TopicRepository,
  ) { }

  @get('/topics/{id}/sub-topics', {
    responses: {
      '200': {
        description: 'Array of Topic has many SubTopic',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SubTopic)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SubTopic>,
  ): Promise<SubTopic[]> {
    return this.topicRepository.subTopics(id).find(filter);
  }

  @post('/topics/{id}/sub-topics', {
    responses: {
      '200': {
        description: 'Topic model instance',
        content: {'application/json': {schema: getModelSchemaRef(SubTopic)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof Topic.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubTopic, {
            title: 'NewSubTopicInTopic',
            exclude: ['id'],
            optional: ['topicId']
          }),
        },
      },
    }) subTopic: Omit<SubTopic, 'id'>,
  ): Promise<SubTopic> {
    return this.topicRepository.subTopics(id).create(subTopic);
  }

  @patch('/topics/{id}/sub-topics', {
    responses: {
      '200': {
        description: 'Topic.SubTopic PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SubTopic, {partial: true}),
        },
      },
    })
    subTopic: Partial<SubTopic>,
    @param.query.object('where', getWhereSchemaFor(SubTopic)) where?: Where<SubTopic>,
  ): Promise<Count> {
    return this.topicRepository.subTopics(id).patch(subTopic, where);
  }

  @del('/topics/{id}/sub-topics', {
    responses: {
      '200': {
        description: 'Topic.SubTopic DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SubTopic)) where?: Where<SubTopic>,
  ): Promise<Count> {
    return this.topicRepository.subTopics(id).delete(where);
  }
}
