import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDfUser, AssignDfUserRelations} from '../models';

export class AssignDfUserRepository extends DefaultCrudRepository<
  AssignDfUser,
  typeof AssignDfUser.prototype.id,
  AssignDfUserRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AssignDfUser, dataSource);
  }
}
