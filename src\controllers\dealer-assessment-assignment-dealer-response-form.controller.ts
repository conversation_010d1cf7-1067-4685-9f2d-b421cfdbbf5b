import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  DealerAssessmentAssignment,
  DealerResponseForm,
} from '../models';
import {DealerAssessmentAssignmentRepository} from '../repositories';

export class DealerAssessmentAssignmentDealerResponseFormController {
  constructor(
    @repository(DealerAssessmentAssignmentRepository)
    public dealerAssessmentAssignmentRepository: DealerAssessmentAssignmentRepository,
  ) { }

  @get('/dealer-assessment-assignments/{id}/dealer-response-form', {
    responses: {
      '200': {
        description: 'DealerResponseForm belonging to DealerAssessmentAssignment',
        content: {
          'application/json': {
            schema: getModelSchemaRef(DealerResponseForm),
          },
        },
      },
    },
  })
  async getDealerResponseForm(
    @param.path.number('id') id: typeof DealerAssessmentAssignment.prototype.id,
  ): Promise<DealerResponseForm> {
    return this.dealerAssessmentAssignmentRepository.form(id);
  }
}
