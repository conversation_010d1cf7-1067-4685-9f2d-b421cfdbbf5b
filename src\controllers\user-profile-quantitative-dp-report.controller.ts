import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  UserProfile,
  QuantitativeDpReport,
} from '../models';
import {UserProfileRepository} from '../repositories';

export class UserProfileQuantitativeDpReportController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
  ) { }

  @get('/user-profiles/{id}/quantitative-dp-reports', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many QuantitativeDpReport',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(QuantitativeDpReport)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<QuantitativeDpReport>,
  ): Promise<QuantitativeDpReport[]> {
    return this.userProfileRepository.quantitativeDpReports(id).find(filter);
  }

  @post('/user-profiles/{id}/quantitative-dp-reports', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(QuantitativeDpReport)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeDpReport, {
            title: 'NewQuantitativeDpReportInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) quantitativeDpReport: Omit<QuantitativeDpReport, 'id'>,
  ): Promise<QuantitativeDpReport> {
    return this.userProfileRepository.quantitativeDpReports(id).create(quantitativeDpReport);
  }

  @patch('/user-profiles/{id}/quantitative-dp-reports', {
    responses: {
      '200': {
        description: 'UserProfile.QuantitativeDpReport PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(QuantitativeDpReport, {partial: true}),
        },
      },
    })
    quantitativeDpReport: Partial<QuantitativeDpReport>,
    @param.query.object('where', getWhereSchemaFor(QuantitativeDpReport)) where?: Where<QuantitativeDpReport>,
  ): Promise<Count> {
    return this.userProfileRepository.quantitativeDpReports(id).patch(quantitativeDpReport, where);
  }

  @del('/user-profiles/{id}/quantitative-dp-reports', {
    responses: {
      '200': {
        description: 'UserProfile.QuantitativeDpReport DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(QuantitativeDpReport)) where?: Where<QuantitativeDpReport>,
  ): Promise<Count> {
    return this.userProfileRepository.quantitativeDpReports(id).delete(where);
  }
}
