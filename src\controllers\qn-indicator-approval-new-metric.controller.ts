import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  QnIndicatorApproval,
  NewMetric,
} from '../models';
import {QnIndicatorApprovalRepository} from '../repositories';

export class QnIndicatorApprovalNewMetricController {
  constructor(
    @repository(QnIndicatorApprovalRepository)
    public qnIndicatorApprovalRepository: QnIndicatorApprovalRepository,
  ) { }

  @get('/qn-indicator-approvals/{id}/new-metric', {
    responses: {
      '200': {
        description: 'NewMetric belonging to QnIndicatorApproval',
        content: {
          'application/json': {
            schema: getModelSchemaRef(NewMetric),
          },
        },
      },
    },
  })
  async getNewMetric(
    @param.path.number('id') id: typeof QnIndicatorApproval.prototype.id,
  ): Promise<NewMetric> {
    return this.qnIndicatorApprovalRepository.newMetric(id);
  }
}
