import { Entity, model, property } from '@loopback/repository';

@model()
export class NewIndicator extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;
  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  baseValue?: string;

  @property({
    type: 'string',
  })
  targetValue?: string;

  @property({
    type: 'string',
  })
  baseDate?: string;

  @property({
    type: 'string',
  })
  targetDate?: string;

  @property({
    type: 'string',
  })
  baseUnit?: string;

  @property({
    type: 'string',
  })
  targetUnit?: string;

  @property({
    type: 'string',
  })
  frequency?: string;

  @property({
    type: 'string',
  })
  interim_frequency?: string;

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  interim_target?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  comments?: any[];

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  newTargetsTwoId?: number;

  constructor(data?: Partial<NewIndicator>) {
    super(data);
  }
}

export interface NewIndicatorRelations {
  // describe navigational properties here
}

export type NewIndicatorWithRelations = NewIndicator & NewIndicatorRelations;
