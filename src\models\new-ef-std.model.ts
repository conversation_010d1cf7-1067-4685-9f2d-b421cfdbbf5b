import {Entity, hasMany, model, property} from '@loopback/repository';
import {GhgCategory} from './ghg-category.model';
import {NewEfCategory} from './new-ef-category.model';
import {NewEfDate} from './new-ef-date.model';

@model()
export class NewEfStd extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'boolean',
  })
  public?: boolean;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    }
  })
  modified_on?: string | null;

  @property({
    type: 'any',
  })
  extra?: any;

  @property({
    type: 'any',
  })
  dcf_ids?: any;

  @property({
    type: 'array',
    itemType: 'any',
  })
  client_ids?: any[];


  @hasMany(() => NewEfDate)
  newEfDates: NewEfDate[];

  @hasMany(() => NewEfCategory)
  newEfCategories: NewEfCategory[];

  @hasMany(() => GhgCategory)
  ghgCategories: GhgCategory[];

  constructor(data?: Partial<NewEfStd>) {
    super(data);
  }
}

export interface NewEfStdRelations {
  // describe navigational properties here
}

export type NewEfStdWithRelations = NewEfStd & NewEfStdRelations;
