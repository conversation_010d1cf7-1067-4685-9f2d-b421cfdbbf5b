import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  AssignSrfEntityUser,
  ConsolidateFormCollection,
} from '../models';
import {AssignSrfEntityUserRepository} from '../repositories';

export class AssignSrfEntityUserConsolidateFormCollectionController {
  constructor(
    @repository(AssignSrfEntityUserRepository)
    public assignSrfEntityUserRepository: AssignSrfEntityUserRepository,
  ) { }

  @get('/assign-srf-entity-users/{id}/consolidate-form-collection', {
    responses: {
      '200': {
        description: 'ConsolidateFormCollection belonging to AssignSrfEntityUser',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(ConsolidateFormCollection)},
          },
        },
      },
    },
  })
  async getConsolidateFormCollection(
    @param.path.number('id') id: typeof AssignSrfEntityUser.prototype.id,
  ): Promise<ConsolidateFormCollection> {
    return this.assignSrfEntityUserRepository.srf(id);
  }
}
