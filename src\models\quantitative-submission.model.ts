import {belongsTo, Entity, model, property} from '@loopback/repository';
import {FormCollection} from './form-collection.model';

@model()
export class QuantitativeSubmission extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;



  @property({
    type: 'number',
  })
  reviewed_by?: number;

  @property({
    type: 'string',
  })
  reviewed_on?: string;

  @property({
    type: 'number',
  })
  user_type?: number;

  @property({
    type: 'number',
  })
  form_type?: number;

  @property({
    type: 'number',
  })
  locationId?: number;
  @property({
    type: 'number',
  })
  level?: number;
  @property({
    type: 'number',
  })
  tier0_id?: number;
  @property({
    type: 'number',
  })
  tier1_id?: number;
  @property({
    type: 'number',
  })
  tier2_id?: number;
  @property({
    type: 'number',
  })
  tier3_id?: number;
  @property({
    type: 'array',
    itemType: 'any',
  })
  reporting_period?: any[];

  @property({
    type: 'any',

    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      type: 'array',
      items: {
        type: 'object',
      },
    }

  })
  response?: any[];

  @property({
    type: 'array',
    itemType: 'object',
  })
  response2?: object[];

  @property({
    type: 'number',
  })
  type?: number;

  @property({
    type: 'any',
  })
  logs?: any;

  @property({

    type: 'any',

  })
  imported?: any;

  @property({
    type: 'any',
  })
  return_remarks?: any;

  @property({
    type: 'any',
  })
  standard?: any;

  @property({
    type: 'number',
  })
  edit?: number;

  @property({
    type: 'array',
    itemType: 'any',
  })
  data1?: any[];

  @property({
    type: 'string',
  })
  submitted_on?: string;
  @property({
    type: 'string',
  })
  approver_modified_on?: string;
  @property({
    type: 'number',
  })
  approver_modified_by?: number;

  @property({
    type: 'string',
  })
  reviewer_modified_on?: string;
  @property({
    type: 'number',
  })
  reviewer_modified_by?: number;

  @property({
    type: 'string',
  })
  reporter_modified_on?: string;
  @property({
    type: 'number',
  })
  reporter_modified_by?: number;

  @property({
    type: 'string',
  })
  last_modified_on?: string;

  @property({
    type: 'number',
  })
  last_modified_by?: number;
  @property({
    type: 'number',
  })
  reject?: number;

  @property({
    type: 'string',
  })
  approved_on?: string;

  @property({
    type: 'string',
  })
  rejected_on?: string;

  @property({
    type: 'number',
  })
  frequency?: number;

  @property({
    type: 'number',
  })
  return_status?: number;




  @property({
    type: 'number',
  })
  approver_return?: number;

  @property({
    type: 'number',
  })
  submitted_by?: number;

  @property({
    type: 'number',
  })
  approved_by?: number;


  @property({
    type: 'number',
  })
  approve_return_by?: number;
  @property({
    type: 'string',
  })
  approve_return_on?: string;

  @property({
    type: 'boolean',
  })
  self?: boolean;

  @property({
    type: 'any',
  })
  entityUserAssId?: any;
  @property({
    type: 'any',
  })
  entityAssId?: any;
  @property({
    type: 'any',
  })
  documents?: any;

  // add from dev
  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
  })
  l2_approved_on?: string | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    },
  })
  l2_approved_by?: number | null;

  @property({
    type: 'string',
    jsonSchema: {
      nullable: true,
    },
  })
  l2_approver_modified_on?: string | null;
  @property({
    type: 'number',
    jsonSchema: {
      nullable: true,
    },
  })
  l2_approver_modified_by?: number | null;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => FormCollection)
  dcfId: number;



  constructor(data?: Partial<QuantitativeSubmission>) {
    super(data);
  }
}

export interface QuantitativeSubmissionRelations {
  // describe navigational properties here
}

export type QuantitativeSubmissionWithRelations = QuantitativeSubmission & QuantitativeSubmissionRelations;
