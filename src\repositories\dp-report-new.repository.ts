import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {DpReportNew, DpReportNewRelations} from '../models';

export class DpReportNewRepository extends DefaultCrudRepository<
  DpReportNew,
  typeof DpReportNew.prototype.id,
  DpReportNewRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(DpReportNew, dataSource);
  }
}
