import {Entity, model, property, hasMany} from '@loopback/repository';
import {NewEfSubcategory3} from './new-ef-subcategory3.model';

@model()
export class NewEfSubcategory2 extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  extra?: string;

  @property({
    type: 'number',
  })
  newEfSubcategory1Id?: number;

  @hasMany(() => NewEfSubcategory3)
  newEfSubcategory3s: NewEfSubcategory3[];

  constructor(data?: Partial<NewEfSubcategory2>) {
    super(data);
  }
}

export interface NewEfSubcategory2Relations {
  // describe navigational properties here
}

export type NewEfSubcategory2WithRelations = NewEfSubcategory2 & NewEfSubcategory2Relations;
