import {Entity, model, property} from '@loopback/repository';

@model()
export class DealerResponseForm extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  comments?: string;




  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;

  @property({
    type: 'number',
  })
  modified_by?: number;
  @property({
    type: 'number',
  })
  type?: number;
  @property({
    type: 'number',
  })
  category?: number;
  @property({
    type: 'string',
    mysql: {
      dataType: 'LONGTEXT'
    }
  })
  data1?: string;


  @property({
    type: 'number',
  })
  userProfileId?: number;

  constructor(data?: Partial<DealerResponseForm>) {
    super(data);
  }
}

export interface DealerResponseFormRelations {
  // describe navigational properties here
}

export type DealerResponseFormWithRelations = DealerResponseForm & DealerResponseFormRelations;
