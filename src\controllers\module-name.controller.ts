import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ModuleName} from '../models';
import {ModuleNameRepository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class ModuleNameController {
  constructor(
    @repository(ModuleNameRepository)
    public moduleNameRepository : ModuleNameRepository,
  ) {}

  @post('/module-names')
  @response(200, {
    description: 'ModuleName model instance',
    content: {'application/json': {schema: getModelSchemaRef(ModuleName)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ModuleName, {
            title: 'NewModuleName',
            exclude: ['id'],
          }),
        },
      },
    })
    moduleName: Omit<ModuleName, 'id'>,
  ): Promise<ModuleName> {
    return this.moduleNameRepository.create(moduleName);
  }

  @get('/module-names/count')
  @response(200, {
    description: 'ModuleName model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ModuleName) where?: Where<ModuleName>,
  ): Promise<Count> {
    return this.moduleNameRepository.count(where);
  }

  @get('/module-names')
  @response(200, {
    description: 'Array of ModuleName model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ModuleName, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ModuleName) filter?: Filter<ModuleName>,
  ): Promise<ModuleName[]> {
    return this.moduleNameRepository.find(filter);
  }

  // @patch('/module-names')
  // @response(200, {
  //   description: 'ModuleName PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ModuleName, {partial: true}),
  //       },
  //     },
  //   })
  //   moduleName: ModuleName,
  //   @param.where(ModuleName) where?: Where<ModuleName>,
  // ): Promise<Count> {
  //   return this.moduleNameRepository.updateAll(moduleName, where);
  // }

  @get('/module-names/{id}')
  @response(200, {
    description: 'ModuleName model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ModuleName, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ModuleName, {exclude: 'where'}) filter?: FilterExcludingWhere<ModuleName>
  ): Promise<ModuleName> {
    return this.moduleNameRepository.findById(id, filter);
  }

  @patch('/module-names/{id}')
  @response(204, {
    description: 'ModuleName PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ModuleName, {partial: true}),
        },
      },
    })
    moduleName: ModuleName,
  ): Promise<void> {
    await this.moduleNameRepository.updateById(id, moduleName);
  }

  @put('/module-names/{id}')
  @response(204, {
    description: 'ModuleName PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() moduleName: ModuleName,
  ): Promise<void> {
    await this.moduleNameRepository.replaceById(id, moduleName);
  }

  @del('/module-names/{id}')
  @response(204, {
    description: 'ModuleName DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.moduleNameRepository.deleteById(id);
  }
}
