import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response,
} from '@loopback/rest';
import {ConsolidateFormCollection} from '../models';
import {ConsolidateFormCollectionRepository} from '../repositories';

import {DateTime} from 'luxon';



export class ConsolidateFormCollectionController {
  constructor(
    @repository(ConsolidateFormCollectionRepository)
    public consolidateFormCollectionRepository: ConsolidateFormCollectionRepository,
  ) { }

  @post('/consolidate-form-collections')
  @response(200, {
    description: 'ConsolidateFormCollection model instance',
    content: {'application/json': {schema: getModelSchemaRef(ConsolidateFormCollection)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ConsolidateFormCollection, {
            title: 'NewConsolidateFormCollection',
            exclude: ['id'],
          }),
        },
      },
    })
    consolidateFormCollection: Omit<ConsolidateFormCollection, 'id'>,
  ): Promise<ConsolidateFormCollection> {
    consolidateFormCollection.updated = DateTime.utc().toString()
    consolidateFormCollection.created = DateTime.utc().toString()
    return this.consolidateFormCollectionRepository.create(consolidateFormCollection);
  }

  @get('/consolidate-form-collections/count')
  @response(200, {
    description: 'ConsolidateFormCollection model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ConsolidateFormCollection) where?: Where<ConsolidateFormCollection>,
  ): Promise<Count> {
    return this.consolidateFormCollectionRepository.count(where);
  }

  @get('/consolidate-form-collections')
  @response(200, {
    description: 'Array of ConsolidateFormCollection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ConsolidateFormCollection, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ConsolidateFormCollection) filter?: Filter<ConsolidateFormCollection>,
  ): Promise<ConsolidateFormCollection[]> {
    return this.consolidateFormCollectionRepository.find(filter);
  }

  // @patch('/consolidate-form-collections')
  // @response(200, {
  //   description: 'ConsolidateFormCollection PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(ConsolidateFormCollection, {partial: true}),
  //       },
  //     },
  //   })
  //   consolidateFormCollection: ConsolidateFormCollection,
  //   @param.where(ConsolidateFormCollection) where?: Where<ConsolidateFormCollection>,
  // ): Promise<Count> {
  //   return this.consolidateFormCollectionRepository.updateAll(consolidateFormCollection, where);
  // }

  @get('/consolidate-form-collections/{id}')
  @response(200, {
    description: 'ConsolidateFormCollection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(ConsolidateFormCollection, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(ConsolidateFormCollection, {exclude: 'where'}) filter?: FilterExcludingWhere<ConsolidateFormCollection>
  ): Promise<ConsolidateFormCollection> {
    return this.consolidateFormCollectionRepository.findById(id, filter);
  }

  @patch('/consolidate-form-collections/{id}')
  @response(204, {
    description: 'ConsolidateFormCollection PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ConsolidateFormCollection, {partial: true}),
        },
      },
    })
    consolidateFormCollection: ConsolidateFormCollection,
  ): Promise<void> {
    consolidateFormCollection.updated = DateTime.utc().toString()
    await this.consolidateFormCollectionRepository.updateById(id, consolidateFormCollection);
  }

  @put('/consolidate-form-collections/{id}')
  @response(204, {
    description: 'ConsolidateFormCollection PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() consolidateFormCollection: ConsolidateFormCollection,
  ): Promise<void> {
    await this.consolidateFormCollectionRepository.replaceById(id, consolidateFormCollection);
  }

  @del('/consolidate-form-collections/{id}')
  @response(204, {
    description: 'ConsolidateFormCollection DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.consolidateFormCollectionRepository.deleteById(id);
  }
}
