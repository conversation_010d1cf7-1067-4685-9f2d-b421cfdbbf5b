import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {QRequirement, QRequirementRelations} from '../models';

export class QRequirementRepository extends DefaultCrudRepository<
  QRequirement,
  typeof QRequirement.prototype.id,
  QRequirementRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(QRequirement, dataSource);
  }
}
