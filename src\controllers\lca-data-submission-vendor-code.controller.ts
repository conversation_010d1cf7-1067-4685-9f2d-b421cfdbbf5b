import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  LcaDataSubmission,
  VendorCode,
} from '../models';
import {LcaDataSubmissionRepository} from '../repositories';

export class LcaDataSubmissionVendorCodeController {
  constructor(
    @repository(LcaDataSubmissionRepository)
    public lcaDataSubmissionRepository: LcaDataSubmissionRepository,
  ) { }

  @get('/lca-data-submissions/{id}/vendor-code', {
    responses: {
      '200': {
        description: 'VendorCode belonging to LcaDataSubmission',
        content: {
          'application/json': {
            schema: getModelSchemaRef(VendorCode),
          },
        },
      },
    },
  })
  async getVendorCode(
    @param.path.number('id') id: typeof LcaDataSubmission.prototype.id,
  ): Promise<VendorCode> {
    return this.lcaDataSubmissionRepository.vendor(id);
  }
}
