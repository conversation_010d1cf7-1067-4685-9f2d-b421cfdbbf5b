import {Entity, model, property} from '@loopback/repository';

@model()
export class Frequency extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'any',
  })
  data?: any;

  @property({
    type: 'string',
  })
  created?: string;

  @property({
    type: 'number',
  })
  locationThreeId?: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @property({
    type: 'number',
  })
  topicNameId?: number;

  constructor(data?: Partial<Frequency>) {
    super(data);
  }
}

export interface FrequencyRelations {
  // describe navigational properties here
}

export type FrequencyWithRelations = Frequency & FrequencyRelations;
