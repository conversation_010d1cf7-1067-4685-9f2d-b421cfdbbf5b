import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {SubmitDcf, SubmitDcfRelations} from '../models';

export class SubmitDcfRepository extends DefaultCrudRepository<
  SubmitDcf,
  typeof SubmitDcf.prototype.id,
  SubmitDcfRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(SubmitDcf, dataSource);
  }
}
