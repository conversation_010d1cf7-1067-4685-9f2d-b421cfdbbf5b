import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {AssignDcfUser, AssignDcfUserRelations} from '../models';

export class AssignDcfUserRepository extends DefaultCrudRepository<
  AssignDcfUser,
  typeof AssignDcfUser.prototype.id,
  AssignDcfUserRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(AssignDcfUser, dataSource);
  }
}
