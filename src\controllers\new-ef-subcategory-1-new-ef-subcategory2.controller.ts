import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  NewEfSubcategory1,
  NewEfSubcategory2,
} from '../models';
import {NewEfSubcategory1Repository} from '../repositories';

import {authenticate} from '@loopback/authentication';



export class NewEfSubcategory1NewEfSubcategory2Controller {
  constructor(
    @repository(NewEfSubcategory1Repository) protected newEfSubcategory1Repository: NewEfSubcategory1Repository,
  ) { }

  @get('/new-ef-subcategory1s/{id}/new-ef-subcategory2s', {
    responses: {
      '200': {
        description: 'Array of NewEfSubcategory1 has many NewEfSubcategory2',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(NewEfSubcategory2)},
          },
        },
      },
    },
  })
  async find(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<NewEfSubcategory2>,
  ): Promise<NewEfSubcategory2[]> {
    return this.newEfSubcategory1Repository.newEfSubcategory2s(id).find(filter);
  }

  @post('/new-ef-subcategory1s/{id}/new-ef-subcategory2s', {
    responses: {
      '200': {
        description: 'NewEfSubcategory1 model instance',
        content: {'application/json': {schema: getModelSchemaRef(NewEfSubcategory2)}},
      },
    },
  })
  async create(
    @param.path.number('id') id: typeof NewEfSubcategory1.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(NewEfSubcategory2, {
            title: 'NewNewEfSubcategory2InNewEfSubcategory1',
            exclude: ['id'],
            optional: ['newEfSubcategory1Id']
          }),
        },
      },
    }) newEfSubcategory2: Omit<NewEfSubcategory2, 'id'>,
  ): Promise<NewEfSubcategory2> {
    return this.newEfSubcategory1Repository.newEfSubcategory2s(id).create(newEfSubcategory2);
  }

  // @patch('/new-ef-subcategory1s/{id}/new-ef-subcategory2s', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfSubcategory1.NewEfSubcategory2 PATCH success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async patch(
  //   @param.path.number('id') id: number,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(NewEfSubcategory2, {partial: true}),
  //       },
  //     },
  //   })
  //   newEfSubcategory2: Partial<NewEfSubcategory2>,
  //   @param.query.object('where', getWhereSchemaFor(NewEfSubcategory2)) where?: Where<NewEfSubcategory2>,
  // ): Promise<Count> {
  //   return this.newEfSubcategory1Repository.newEfSubcategory2s(id).patch(newEfSubcategory2, where);
  // }

  // @del('/new-ef-subcategory1s/{id}/new-ef-subcategory2s', {
  //   responses: {
  //     '200': {
  //       description: 'NewEfSubcategory1.NewEfSubcategory2 DELETE success count',
  //       content: {'application/json': {schema: CountSchema}},
  //     },
  //   },
  // })
  // async delete(
  //   @param.path.number('id') id: number,
  //   @param.query.object('where', getWhereSchemaFor(NewEfSubcategory2)) where?: Where<NewEfSubcategory2>,
  // ): Promise<Count> {
  //   return this.newEfSubcategory1Repository.newEfSubcategory2s(id).delete(where);
  // }
}
