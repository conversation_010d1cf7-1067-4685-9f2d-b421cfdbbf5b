import {Entity, model, property, hasMany} from '@loopback/repository';
import {StdTopic} from './std-topic.model';

@model()
export class StdScope extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'number',
  })
  stdNameId?: number;

  @hasMany(() => StdTopic)
  stdTopics: StdTopic[];

  constructor(data?: Partial<StdScope>) {
    super(data);
  }
}

export interface StdScopeRelations {
  // describe navigational properties here
}

export type StdScopeWithRelations = StdScope & StdScopeRelations;
