import {Entity, model, property} from '@loopback/repository';

@model({settings: {strict: false}})
export class Standard extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'number',
    default: 1,
  })
  editable?: number;

  @property({
    type: 'number',
    default: 1,
  })
  sasb?: number;

  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<Standard>) {
    super(data);
  }
}

export interface StandardRelations {
  // describe navigational properties here
}

export type StandardWithRelations = Standard & StandardRelations;
