import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  NewEfItem,
  NewEfSubcategory1,
} from '../models';
import {NewEfItemRepository} from '../repositories';

export class NewEfItemNewEfSubcategory1Controller {
  constructor(
    @repository(NewEfItemRepository)
    public newEfItemRepository: NewEfItemRepository,
  ) { }

  @get('/new-ef-items/{id}/new-ef-subcategory1', {
    responses: {
      '200': {
        description: 'NewEfSubcategory1 belonging to NewEfItem',
        content: {
          'application/json': {
            schema: getModelSchemaRef(NewEfSubcategory1),
          },
        },
      },
    },
  })
  async getNewEfSubcategory1(
    @param.path.number('id') id: typeof NewEfItem.prototype.id,
  ): Promise<NewEfSubcategory1> {
    return this.newEfItemRepository.subcat1(id);
  }
}
