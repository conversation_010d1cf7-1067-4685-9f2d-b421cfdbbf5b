import {belongsTo, Entity, model, property} from '@loopback/repository';
import {ConsolidateFormCollection} from './consolidate-form-collection.model';
import {QCategory} from './q-category.model';
import {QSection} from './q-section.model';
import {QTopic} from './q-topic.model';

@model()
export class AssignQlEntity extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'any',
  })
  comments?: any;

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier0_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier1_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier2_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  tier3_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  consolidator_ids?: number[];

  @property({
    type: 'string',
  })
  created_on?: string;

  @property({
    type: 'number',
  })
  created_by?: number;
  @property({
    type: 'string',
  })
  modified_on?: string;

  @property({
    type: 'number',
  })
  modified_by?: number;

  @property({
    type: 'number',
  })
  type?: number;
  @property({
    type: 'any',
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  response?: any;
  @belongsTo(() => QCategory)
  qCategoryId: number;

  @belongsTo(() => QTopic)
  qTopicId: number;

  @belongsTo(() => QSection)
  qSectionId: number;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @belongsTo(() => ConsolidateFormCollection)
  srfId: number;

  constructor(data?: Partial<AssignQlEntity>) {
    super(data);
  }
}

export interface AssignQlEntityRelations {
  // describe navigational properties here
}

export type AssignQlEntityWithRelations = AssignQlEntity & AssignQlEntityRelations;
