import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Standard} from '../models';
import {StandardRepository} from '../repositories';

export class StandardController {
  constructor(
    @repository(StandardRepository)
    public standardRepository : StandardRepository,
  ) {}

  @post('/standards')
  @response(200, {
    description: 'Standard model instance',
    content: {'application/json': {schema: getModelSchemaRef(Standard)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Standard, {
            title: 'NewStandard',
            exclude: ['id'],
          }),
        },
      },
    })
    standard: Omit<Standard, 'id'>,
  ): Promise<Standard> {
    return this.standardRepository.create(standard);
  }

  @get('/standards/count')
  @response(200, {
    description: 'Standard model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(Standard) where?: Where<Standard>,
  ): Promise<Count> {
    return this.standardRepository.count(where);
  }

  @get('/standards')
  @response(200, {
    description: 'Array of Standard model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Standard, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Standard) filter?: Filter<Standard>,
  ): Promise<Standard[]> {
    return this.standardRepository.find(filter);
  }

  // @patch('/standards')
  // @response(200, {
  //   description: 'Standard PATCH success count',
  //   content: {'application/json': {schema: CountSchema}},
  // })
  // async updateAll(
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(Standard, {partial: true}),
  //       },
  //     },
  //   })
  //   standard: Standard,
  //   @param.where(Standard) where?: Where<Standard>,
  // ): Promise<Count> {
  //   return this.standardRepository.updateAll(standard, where);
  // }

  @get('/standards/{id}')
  @response(200, {
    description: 'Standard model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Standard, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(Standard, {exclude: 'where'}) filter?: FilterExcludingWhere<Standard>
  ): Promise<Standard> {
    return this.standardRepository.findById(id, filter);
  }

  @patch('/standards/{id}')
  @response(204, {
    description: 'Standard PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Standard, {partial: true}),
        },
      },
    })
    standard: Standard,
  ): Promise<void> {
    await this.standardRepository.updateById(id, standard);
  }

  @put('/standards/{id}')
  @response(204, {
    description: 'Standard PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() standard: Standard,
  ): Promise<void> {
    await this.standardRepository.replaceById(id, standard);
  }

  @del('/standards/{id}')
  @response(204, {
    description: 'Standard DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.standardRepository.deleteById(id);
  }
}
