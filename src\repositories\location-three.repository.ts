import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {LocationThree, LocationThreeRelations, Frequency} from '../models';
import {FrequencyRepository} from './frequency.repository';

export class LocationThreeRepository extends DefaultCrudRepository<
  LocationThree,
  typeof LocationThree.prototype.id,
  LocationThreeRelations
> {

  public readonly frequencies: HasManyRepositoryFactory<Frequency, typeof LocationThree.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('FrequencyRepository') protected frequencyRepositoryGetter: Getter<FrequencyRepository>,
  ) {
    super(LocationThree, dataSource);
    this.frequencies = this.createHasManyRepositoryFactoryFor('frequencies', frequencyRepositoryGetter,);
    this.registerInclusionResolver('frequencies', this.frequencies.inclusionResolver);
  }
}
