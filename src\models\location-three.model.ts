import {Entity, model, property, hasMany} from '@loopback/repository';
import {Frequency} from './frequency.model';

@model({settings: {strict: false}})
export class LocationThree extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  name?: string;

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  initiatives?: any[];
  
  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];
  
  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'number',
  })
  locationTwoId?: number;

  @hasMany(() => Frequency)
  frequencies: Frequency[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<LocationThree>) {
    super(data);
  }
}

export interface LocationThreeRelations {
  // describe navigational properties here
}

export type LocationThreeWithRelations = LocationThree & LocationThreeRelations;
