import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {GhgSubCategory} from '../models';
import {GhgSubCategoryRepository} from '../repositories';

export class GhgSubCategoryController {
  constructor(
    @repository(GhgSubCategoryRepository)
    public ghgSubCategoryRepository : GhgSubCategoryRepository,
  ) {}

  @post('/ghg-sub-categories')
  @response(200, {
    description: 'GhgSubCategory model instance',
    content: {'application/json': {schema: getModelSchemaRef(GhgSubCategory)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgSubCategory, {
            title: 'NewGhgSubCategory',
            exclude: ['id'],
          }),
        },
      },
    })
    ghgSubCategory: Omit<GhgSubCategory, 'id'>,
  ): Promise<GhgSubCategory> {
    return this.ghgSubCategoryRepository.create(ghgSubCategory);
  }

  @get('/ghg-sub-categories/count')
  @response(200, {
    description: 'GhgSubCategory model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(GhgSubCategory) where?: Where<GhgSubCategory>,
  ): Promise<Count> {
    return this.ghgSubCategoryRepository.count(where);
  }

  @get('/ghg-sub-categories')
  @response(200, {
    description: 'Array of GhgSubCategory model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(GhgSubCategory, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(GhgSubCategory) filter?: Filter<GhgSubCategory>,
  ): Promise<GhgSubCategory[]> {
    return this.ghgSubCategoryRepository.find(filter);
  }

  @patch('/ghg-sub-categories')
  @response(200, {
    description: 'GhgSubCategory PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgSubCategory, {partial: true}),
        },
      },
    })
    ghgSubCategory: GhgSubCategory,
    @param.where(GhgSubCategory) where?: Where<GhgSubCategory>,
  ): Promise<Count> {
    return this.ghgSubCategoryRepository.updateAll(ghgSubCategory, where);
  }

  @get('/ghg-sub-categories/{id}')
  @response(200, {
    description: 'GhgSubCategory model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(GhgSubCategory, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(GhgSubCategory, {exclude: 'where'}) filter?: FilterExcludingWhere<GhgSubCategory>
  ): Promise<GhgSubCategory> {
    return this.ghgSubCategoryRepository.findById(id, filter);
  }

  @patch('/ghg-sub-categories/{id}')
  @response(204, {
    description: 'GhgSubCategory PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(GhgSubCategory, {partial: true}),
        },
      },
    })
    ghgSubCategory: GhgSubCategory,
  ): Promise<void> {
    await this.ghgSubCategoryRepository.updateById(id, ghgSubCategory);
  }

  @put('/ghg-sub-categories/{id}')
  @response(204, {
    description: 'GhgSubCategory PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() ghgSubCategory: GhgSubCategory,
  ): Promise<void> {
    await this.ghgSubCategoryRepository.replaceById(id, ghgSubCategory);
  }

  @del('/ghg-sub-categories/{id}')
  @response(204, {
    description: 'GhgSubCategory DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.ghgSubCategoryRepository.deleteById(id);
  }
}
