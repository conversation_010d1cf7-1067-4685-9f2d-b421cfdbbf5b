import {inject} from '@loopback/core';
import {DefaultCrudRepository} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {EmployeeData, EmployeeDataRelations} from '../models';

export class EmployeeDataRepository extends DefaultCrudRepository<
  EmployeeData,
  typeof EmployeeData.prototype.id,
  EmployeeDataRelations
> {
  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource,
  ) {
    super(EmployeeData, dataSource);
  }
}
