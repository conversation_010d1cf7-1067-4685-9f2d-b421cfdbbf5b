import {Entity, hasMany, model, property} from '@loopback/repository';
import {AssignDcfUser} from './assign-dcf-user.model';

@model({settings: {strict: false}})
export class AssignDcfClient extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'array',
    itemType: 'number',
  })
  dcf_ids?: number[];

  @property({
    type: 'any',
    mysql: {
      dataType: 'LONGTEXT',
    },
    jsonSchema: {
      anyOf: [
        {
          type: 'array',
          items: {
            type: 'integer'
          }
        },
        {
          type: 'null'
        }
      ]
    }
  })
  sap_ids?: number[] | null;

  @property({
    type: 'array',
    itemType: 'number',
  })
  cf_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  dp_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  metric_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  category_ids?: number[];


  @property({
    type: 'array',
    itemType: 'number',
  })
  topic_ids?: number[];

  @property({
    type: 'array',
    itemType: 'number',
  })
  selected_ids?: number[];


  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data1?: any[];

  @property({
    type: 'array',
    itemType: 'any',
    required: false,
    mysql: {
      dataType: "LONGTEXT"
    }
  })
  data2?: any[];

  @property({
    type: 'number',
  })
  user_id?: number;

  @property({
    type: 'any',
  })
  requestKey?: any

  @property({
    type: 'string',
  })
  modified?: string;

  @property({
    type: 'string',
  })
  created?: string;


  @property({
    type: 'object',
    jsonSchema: {
      nullable: true,
    }
  })
  dcf_indicator_mapping?: object | null;

  @property({
    type: 'object',
    jsonSchema: {
      nullable: true,
    }
  })
  sap_indicator_mapping?: object | null;


  @property({
    type: 'object',
    jsonSchema: {
      nullable: true,
    }
  })
  datapoint_dcf_mapping?: object | null;

  @property({
    type: 'object',
    jsonSchema: {
      nullable: true,
    }
  })
  datapoint_sap_mapping?: object | null;

  @property({
    type: 'number',
  })
  userProfileId?: number;

  @hasMany(() => AssignDcfUser)
  assignDcfUsers: AssignDcfUser[];
  // Define well-known properties here

  // Indexer property to allow additional data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [prop: string]: any;

  constructor(data?: Partial<AssignDcfClient>) {
    super(data);
  }
}

export interface AssignDcfClientRelations {
  // describe navigational properties here
}

export type AssignDcfClientWithRelations = AssignDcfClient & AssignDcfClientRelations;
