import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  NewEfItem,
  NewEfSubcategory3,
} from '../models';
import {NewEfItemRepository} from '../repositories';

export class NewEfItemNewEfSubcategory3Controller {
  constructor(
    @repository(NewEfItemRepository)
    public newEfItemRepository: NewEfItemRepository,
  ) { }

  @get('/new-ef-items/{id}/new-ef-subcategory3', {
    responses: {
      '200': {
        description: 'NewEfSubcategory3 belonging to NewEfItem',
        content: {
          'application/json': {
            schema: getModelSchemaRef(NewEfSubcategory3),
          },
        },
      },
    },
  })
  async getNewEfSubcategory3(
    @param.path.number('id') id: typeof NewEfItem.prototype.id,
  ): Promise<NewEfSubcategory3> {
    return this.newEfItemRepository.subcat3(id);
  }
}
