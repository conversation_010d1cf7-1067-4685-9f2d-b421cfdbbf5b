import { Entity, model, property, hasMany} from '@loopback/repository';
import {ReportNameTwo} from './report-name-two.model';

@model()
export class ReportNameOne extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
  })
  title?: string;

  @property({
    type: 'string',
  })
  suffix?: string;

  @property({
    type: 'string',
  })
  data1?: string;

  @property({
    type: 'string',
  })
  data2?: string;

  @property({
    type: 'string',
  })
  extra?: string;

  @property({
    type: 'string',
  })
  created?: string;

  @hasMany(() => ReportNameTwo)
  reportNameTwos: ReportNameTwo[];

  constructor(data?: Partial<ReportNameOne>) {
    super(data);
  }
}

export interface ReportNameOneRelations {
  // describe navigational properties here
}

export type ReportNameOneWithRelations = ReportNameOne & ReportNameOneRelations;
