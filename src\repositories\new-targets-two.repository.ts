import {inject, Getter} from '@loopback/core';
import {DefaultCrudRepository, repository, HasManyRepositoryFactory} from '@loopback/repository';
import {MysqlDataSource} from '../datasources';
import {NewTargetsTwo, NewTargetsTwoRelations, NewIndicator} from '../models';
import {NewIndicatorRepository} from './new-indicator.repository';

export class NewTargetsTwoRepository extends DefaultCrudRepository<
  NewTargetsTwo,
  typeof NewTargetsTwo.prototype.id,
  NewTargetsTwoRelations
> {

  public readonly newIndicators: HasManyRepositoryFactory<NewIndicator, typeof NewTargetsTwo.prototype.id>;

  constructor(
    @inject('datasources.mysql') dataSource: MysqlDataSource, @repository.getter('NewIndicatorRepository') protected newIndicatorRepositoryGetter: Getter<NewIndicatorRepository>,
  ) {
    super(NewTargetsTwo, dataSource);
    this.newIndicators = this.createHasManyRepositoryFactoryFor('newIndicators', newIndicatorRepositoryGetter,);
    this.registerInclusionResolver('newIndicators', this.newIndicators.inclusionResolver);
  }
}
